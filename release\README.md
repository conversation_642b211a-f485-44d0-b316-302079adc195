# 微信群用户自我介绍分析器

一个基于AI的微信群用户画像自动分析工具，能够自动识别群聊中的自我介绍并提取结构化的用户信息。

## 🌟 主要功能

- **智能识别**：基于正则表达式模式匹配和AI智能识别自我介绍消息
- **画像提取**：自动提取昵称、职业、个人介绍、能提供的帮助、寻求的帮助、行业领域等信息
- **数据同步**：支持同步到飞书多维表格，便于团队协作
- **可视化界面**：现代化的图形界面，操作简单直观
- **数据导出**：支持导出Excel、JSON等格式
- **实时监控**：分析进度实时显示，任务状态清晰可见

## 🏗️ 技术架构

- **编程语言**：Python 3.8.1+
- **包管理器**：uv（现代化Python包管理器）
- **图形界面**：ttkbootstrap（现代化tkinter）
- **AI服务**：LlamaIndex + OpenAI兼容API（如DeepSeek）
- **数据源**：Chatlog HTTP API（微信聊天记录）
- **数据存储**：飞书多维表格
- **异步处理**：asyncio + threading

## 📦 快速开始

### 1. 环境要求

- Python 3.8.1 或更高版本
- uv 包管理器
- Windows 10/11 或 macOS 或 Linux

### 2. 安装uv（如果尚未安装）

```bash
# 使用pip安装
pip install uv

# 或者直接下载安装（推荐）
# 访问: https://docs.astral.sh/uv/getting-started/installation/
```

### 3. 克隆并设置项目

```bash
# 克隆项目
git clone <repository_url>
cd 微信群自我介绍分析器

# 同步基础依赖
uv sync

# 可选：安装AI功能（需要API密钥）
uv sync --extra ai

# 可选：安装开发工具
uv sync --extra dev
```

### 4. 配置环境变量

```bash
# 复制环境变量示例文件
cp env.example .env

# 编辑 .env 文件，填入你的API密钥
# DEEPSEEK_API_KEY=your_deepseek_api_key
# FEISHU_APP_ID=your_feishu_app_id
# FEISHU_APP_SECRET=your_feishu_app_secret
```

### 5. 运行应用

```bash
# 启动程序
uv run python app/main.py

# 或者使用预设命令
uv run wechat-analyzer
```

## 🔧 uv 使用指南

### 依赖管理

```bash
# 查看项目信息
uv tree

# 安装新依赖
uv add package_name

# 安装开发依赖
uv add --dev package_name

# 移除依赖
uv remove package_name

# 查看已安装包
uv pip list

# 更新依赖
uv sync --upgrade
```

### 可选功能安装

```bash
# 安装AI功能（LlamaIndex等）
uv sync --extra ai

# 安装开发工具
uv sync --extra dev

# 安装构建工具
uv sync --extra build

# 安装全部功能
uv sync --extra full
```

### 开发工具

```bash
# 代码格式化
uv run black app/
uv run isort app/

# 代码检查
uv run flake8 app/

# 运行测试
uv run pytest

# 类型检查
uv run mypy app/
```

## 🔧 API配置

### 1. DeepSeek API

1. 注册账号：https://platform.deepseek.com/
2. 获取API密钥
3. 在 `.env` 文件中设置 `DEEPSEEK_API_KEY`

### 2. 飞书应用

1. 创建飞书应用：https://open.feishu.cn/
2. 获取App ID和App Secret
3. 创建多维表格并获取app_token和table_id
4. 在应用配置中填入相关信息

### 3. Chatlog API

1. 安装并运行Chatlog工具
2. 确保API服务在指定端口运行
3. 在配置中设置正确的API地址

## 🚀 使用指南

### 1. 配置设置

在"配置设置"标签页中：
- 填入DeepSeek API密钥
- 配置飞书应用信息
- 设置Chatlog API地址
- 调整分析参数

### 2. 群组管理

在"群组管理"标签页中：
- 查看可用的微信群组
- 选择要分析的群组
- 预览群组信息

### 3. 分析任务

在"分析任务"标签页中：
- 设置分析时间范围
- 启动分析任务
- 监控分析进度
- 查看实时状态

### 4. 结果查看

在"结果查看"标签页中：
- 查看提取的用户画像
- 使用筛选和搜索功能
- 导出分析结果
- 管理数据

## 📊 数据模型

### 用户画像字段

- **昵称**：用户昵称或真实姓名
- **职业**：职业或工作岗位
- **个人介绍**：个人简介或背景描述
- **能够提供**：能够提供的帮助或服务
- **寻求帮助**：希望得到的帮助或机会
- **行业领域**：所属行业或专业领域
- **置信度**：AI提取的置信度评分（0-1）

### 输出格式

支持多种数据导出格式：
- Excel（.xlsx）
- JSON（.json）
- 飞书多维表格同步

## 🛠️ 开发说明

### 项目结构

```
app/
├── config.py          # 配置管理
├── models.py          # 数据模型
├── main.py           # 程序入口
├── services/         # 服务层
│   ├── chatlog_client.py
│   ├── deepseek_client.py
│   ├── feishu_client.py
│   ├── introduction_detector.py
│   ├── profile_extractor.py
│   └── analysis_service.py
├── ui/               # 用户界面
│   ├── main_window.py
│   ├── config_tab.py
│   ├── groups_tab.py
│   ├── analysis_tab.py
│   └── results_tab.py
└── utils/            # 工具类
    └── logger.py

pyproject.toml        # 项目配置和依赖
```

### 开发环境设置

```bash
# 安装开发依赖
uv sync --extra dev

# 安装git hooks（可选）
uv run pre-commit install

# 运行所有检查
uv run python build.py
```

### 打包发布

```bash
# 自动构建流程
uv run python build.py

# 选择选项9进行完整构建
# 包括：代码格式化、测试、打包、生成发布包
```

## 📝 更新日志

### v1.0.0 (2024-12-xx)

- ✨ 首次发布
- 🎉 完整的用户画像分析功能
- 🔧 现代化图形界面
- 📊 数据导出和同步功能
- 🤖 AI智能识别和提取
- ⚡ 使用uv进行现代化包管理

## 🤝 贡献指南

### 开发流程

1. Fork项目并创建分支
2. 设置开发环境：`uv sync --extra dev`
3. 进行开发和测试
4. 运行代码检查：`uv run black app/ && uv run flake8 app/`
5. 提交PR

### 代码规范

- 使用Black进行代码格式化
- 使用isort进行导入排序
- 使用flake8进行代码检查
- 添加类型注释（mypy）
- 编写测试用例

## 🐛 故障排除

### 常见问题

1. **uv命令未找到**
   ```bash
   pip install uv
   ```

2. **依赖安装失败**
   ```bash
   uv sync --reinstall
   ```

3. **Python版本不兼容**
   ```bash
   uv python install 3.11  # 安装Python 3.11
   uv python pin 3.11      # 设置项目Python版本
   ```

4. **AI功能无法使用**
   ```bash
   uv sync --extra ai
   # 确保在.env中配置了正确的API密钥
   ```

## 📄 许可证

本项目采用MIT许可证。

## 🆘 技术支持

如有问题，请通过以下方式联系：
- 提交GitHub Issue
- 查看项目文档
- 参考技术架构文档

---

**注意**：本工具仅用于合法合规的数据分析用途，请确保遵守相关隐私法规和使用条款。 