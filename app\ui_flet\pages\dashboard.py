"""
仪表盘页面
显示系统概览和快捷操作
"""

import flet as ft
from typing import TYPE_CHECKING
from datetime import datetime, timedelta

if TYPE_CHECKING:
    from ..main_app import FletMainApp

class DashboardPage:
    """仪表盘页面"""
    
    def __init__(self, app: 'FletMainApp'):
        self.app = app
    
    def build(self) -> ft.Column:
        """构建仪表盘页面"""
        
        # 统计卡片
        stats_cards = ft.Row(
            controls=[
                self._create_stat_card(
                    title="今日分析",
                    value="12",
                    subtitle="个群组",
                    icon=ft.icons.ANALYTICS,
                    color=ft.colors.BLUE,
                    trend="+8.5%"
                ),
                self._create_stat_card(
                    title="提取档案",
                    value="156",
                    subtitle="个用户",
                    icon=ft.icons.PEOPLE,
                    color=ft.colors.GREEN,
                    trend="+12.3%"
                ),
                self._create_stat_card(
                    title="成功率",
                    value="94.2%",
                    subtitle="识别准确",
                    icon=ft.icons.CHECK_CIRCLE,
                    color=ft.colors.ORANGE,
                    trend="+2.1%"
                ),
                self._create_stat_card(
                    title="API调用",
                    value="1,234",
                    subtitle="次请求",
                    icon=ft.icons.API,
                    color=ft.colors.PURPLE,
                    trend="+15.7%"
                )
            ],
            spacing=20,
            wrap=True
        )
        
        # 快捷操作区域
        quick_actions = ft.Container(
            content=ft.Column(
                controls=[
                    ft.Text(
                        "🚀 快捷操作",
                        size=18,
                        weight=ft.FontWeight.BOLD,
                        color=ft.colors.GREY_800
                    ),
                    ft.Container(height=10),
                    ft.Row(
                        controls=[
                            self._create_action_button(
                                title="开始分析",
                                subtitle="分析群组自我介绍",
                                icon=ft.icons.PLAY_ARROW,
                                color=ft.colors.GREEN,
                                on_click=lambda e: self.app.navigate_to("analysis")
                            ),
                            self._create_action_button(
                                title="查看结果",
                                subtitle="浏览分析结果",
                                icon=ft.icons.ASSESSMENT,
                                color=ft.colors.BLUE,
                                on_click=lambda e: self.app.navigate_to("results")
                            ),
                            self._create_action_button(
                                title="管理群组",
                                subtitle="选择分析群组",
                                icon=ft.icons.GROUP,
                                color=ft.colors.ORANGE,
                                on_click=lambda e: self.app.navigate_to("groups")
                            ),
                            self._create_action_button(
                                title="系统设置",
                                subtitle="配置API和参数",
                                icon=ft.icons.SETTINGS,
                                color=ft.colors.GREY_700,
                                on_click=lambda e: self.app.navigate_to("settings")
                            )
                        ],
                        spacing=15,
                        wrap=True
                    )
                ]
            ),
            padding=ft.padding.all(25),
            bgcolor=ft.colors.WHITE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.colors.BLACK12,
                offset=ft.Offset(0, 4)
            )
        )
        
        # 系统状态区域
        system_status = ft.Container(
            content=ft.Column(
                controls=[
                    ft.Text(
                        "🔧 系统状态",
                        size=18,
                        weight=ft.FontWeight.BOLD,
                        color=ft.colors.GREY_800
                    ),
                    ft.Container(height=10),
                    ft.Column(
                        controls=[
                            self._create_status_item("DeepSeek AI服务", "已连接", ft.colors.GREEN, ft.icons.CHECK_CIRCLE),
                            self._create_status_item("Chatlog服务", "未连接", ft.colors.RED, ft.icons.ERROR),
                            self._create_status_item("飞书同步", "已配置", ft.colors.BLUE, ft.icons.CLOUD_DONE),
                            self._create_status_item("数据库", "正常", ft.colors.GREEN, ft.icons.STORAGE),
                        ],
                        spacing=12
                    )
                ]
            ),
            padding=ft.padding.all(25),
            bgcolor=ft.colors.WHITE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.colors.BLACK12,
                offset=ft.Offset(0, 4)
            )
        )
        
        # 最近活动
        recent_activity = self._create_recent_activity()
        
        # 主布局
        return ft.Column(
            controls=[
                # 统计卡片行
                stats_cards,
                
                ft.Container(height=20),
                
                # 内容区域
                ft.Row(
                    controls=[
                        # 左列
                        ft.Column(
                            controls=[
                                quick_actions,
                                ft.Container(height=20),
                                system_status
                            ],
                            expand=2
                        ),
                        
                        ft.Container(width=20),
                        
                        # 右列
                        ft.Column(
                            controls=[recent_activity],
                            expand=1
                        )
                    ],
                    expand=True,
                    alignment=ft.MainAxisAlignment.START,
                    vertical_alignment=ft.CrossAxisAlignment.START
                )
            ],
            spacing=0,
            expand=True
        )
    
    def _create_stat_card(self, title: str, value: str, subtitle: str, icon: ft.icons, color: str, trend: str) -> ft.Container:
        """创建统计卡片"""
        return ft.Container(
            content=ft.Column(
                controls=[
                    ft.Row(
                        controls=[
                            ft.Icon(icon, size=24, color=color),
                            ft.Container(
                                content=ft.Text(
                                    trend,
                                    size=12,
                                    color=ft.colors.GREEN if trend.startswith('+') else ft.colors.RED,
                                    weight=ft.FontWeight.BOLD
                                ),
                                bgcolor=ft.colors.GREEN_50 if trend.startswith('+') else ft.colors.RED_50,
                                padding=ft.padding.symmetric(horizontal=8, vertical=4),
                                border_radius=12
                            )
                        ],
                        alignment=ft.MainAxisAlignment.SPACE_BETWEEN
                    ),
                    ft.Container(height=10),
                    ft.Text(
                        value,
                        size=28,
                        weight=ft.FontWeight.BOLD,
                        color=ft.colors.GREY_800
                    ),
                    ft.Text(
                        title,
                        size=14,
                        color=ft.colors.GREY_600,
                        weight=ft.FontWeight.W500
                    ),
                    ft.Text(
                        subtitle,
                        size=12,
                        color=ft.colors.GREY_500
                    )
                ],
                spacing=5,
                horizontal_alignment=ft.CrossAxisAlignment.START
            ),
            padding=ft.padding.all(20),
            bgcolor=ft.colors.WHITE,
            border_radius=12,
            width=280,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.colors.BLACK12,
                offset=ft.Offset(0, 4)
            )
        )
    
    def _create_action_button(self, title: str, subtitle: str, icon: ft.icons, color: str, on_click) -> ft.Container:
        """创建快捷操作按钮"""
        return ft.Container(
            content=ft.Column(
                controls=[
                    ft.Icon(icon, size=32, color=color),
                    ft.Container(height=8),
                    ft.Text(
                        title,
                        size=14,
                        weight=ft.FontWeight.BOLD,
                        color=ft.colors.GREY_800,
                        text_align=ft.TextAlign.CENTER
                    ),
                    ft.Text(
                        subtitle,
                        size=12,
                        color=ft.colors.GREY_600,
                        text_align=ft.TextAlign.CENTER
                    )
                ],
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                spacing=5
            ),
            padding=ft.padding.all(20),
            bgcolor=ft.colors.GREY_50,
            border_radius=12,
            width=200,
            height=120,
            on_click=on_click,
            ink=True,
            animate=ft.animation.Animation(200, ft.AnimationCurve.EASE_OUT),
            border=ft.border.all(1, ft.colors.GREY_200)
        )
    
    def _create_status_item(self, name: str, status: str, color: str, icon: ft.icons) -> ft.Row:
        """创建状态项"""
        return ft.Row(
            controls=[
                ft.Icon(icon, size=20, color=color),
                ft.Text(name, size=14, color=ft.colors.GREY_800, expand=True),
                ft.Container(
                    content=ft.Text(
                        status,
                        size=12,
                        color=color,
                        weight=ft.FontWeight.BOLD
                    ),
                    bgcolor=f"{color}20",  # 20% opacity
                    padding=ft.padding.symmetric(horizontal=8, vertical=4),
                    border_radius=8
                )
            ],
            spacing=12,
            alignment=ft.MainAxisAlignment.START
        )
    
    def _create_recent_activity(self) -> ft.Container:
        """创建最近活动区域"""
        activities = [
            {"time": "2分钟前", "action": "完成群组分析", "detail": "技术交流群 - 发现12个自我介绍"},
            {"time": "15分钟前", "action": "同步到飞书", "detail": "成功同步8个用户档案"},
            {"time": "1小时前", "action": "开始分析任务", "detail": "创业者群组 - 处理中"},
            {"time": "2小时前", "action": "配置更新", "detail": "更新DeepSeek API配置"},
            {"time": "今天 14:30", "action": "导出数据", "detail": "导出Excel格式分析结果"},
        ]
        
        activity_items = []
        for activity in activities:
            activity_items.append(
                ft.Container(
                    content=ft.Column(
                        controls=[
                            ft.Row(
                                controls=[
                                    ft.Text(
                                        activity["action"],
                                        size=14,
                                        weight=ft.FontWeight.W500,
                                        color=ft.colors.GREY_800
                                    ),
                                    ft.Text(
                                        activity["time"],
                                        size=12,
                                        color=ft.colors.GREY_500
                                    )
                                ],
                                alignment=ft.MainAxisAlignment.SPACE_BETWEEN
                            ),
                            ft.Text(
                                activity["detail"],
                                size=12,
                                color=ft.colors.GREY_600
                            )
                        ],
                        spacing=4
                    ),
                    padding=ft.padding.all(12),
                    border_radius=8,
                    bgcolor=ft.colors.GREY_50
                )
            )
        
        return ft.Container(
            content=ft.Column(
                controls=[
                    ft.Text(
                        "📋 最近活动",
                        size=18,
                        weight=ft.FontWeight.BOLD,
                        color=ft.colors.GREY_800
                    ),
                    ft.Container(height=10),
                    ft.Column(
                        controls=activity_items,
                        spacing=8,
                        scroll=ft.ScrollMode.AUTO
                    )
                ]
            ),
            padding=ft.padding.all(25),
            bgcolor=ft.colors.WHITE,
            border_radius=12,
            height=500,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.colors.BLACK12,
                offset=ft.Offset(0, 4)
            )
        )
