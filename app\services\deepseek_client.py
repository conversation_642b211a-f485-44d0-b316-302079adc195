"""
DeepSeek API 客户端
使用 LlamaIndex 的结构化输出功能进行AI智能分析和用户画像提取
支持 DeepSeek 官方集成和 OpenAILike 通用方式
"""

import asyncio
import json
from typing import Dict, Any, Optional, List
import logging
import os
from datetime import datetime
from pydantic import BaseModel, Field
import re

from llama_index.core import Settings
from llama_index.core.llms import ChatMessage
from llama_index.core.program import LLMTextCompletionProgram

try:
    from llama_index.llms.deepseek import DeepSeek
    DEEPSEEK_AVAILABLE = True
except ImportError:
    DEEPSEEK_AVAILABLE = False

try:
    from llama_index.llms.openai_like import OpenAILike
    OPENAI_LIKE_AVAILABLE = True
except ImportError:
    OPENAI_LIKE_AVAILABLE = False

from ..models import UserProfile, ProfileField
from ..config import get_config

logger = logging.getLogger(__name__)

# 定义结构化输出的Pydantic模型
class IntroductionDetection(BaseModel):
    """自我介绍检测结果模型"""
    is_introduction: bool = Field(description="是否为自我介绍")
    confidence: float = Field(description="置信度，范围0.0-1.0", ge=0.0, le=1.0)
    reason: str = Field(description="判断理由")
    keywords: List[str] = Field(description="识别到的关键特征词汇")
    introduction_type: str = Field(description="自我介绍类型：professional(职业型), personal(个人型), service(服务型), mixed(混合型)")

class ExtractedProfile(BaseModel):
    """提取的用户画像模型"""
    industry: Optional[str] = Field(default=None, description="所在行业")
    personal_intro: Optional[str] = Field(default=None, description="个人介绍或描述")
    location: Optional[str] = Field(default=None, description="地区或城市")
    can_provide: List[str] = Field(default_factory=list, description="能提供的服务或资源")
    need_help: List[str] = Field(default_factory=list, description="需要的帮助或寻求的机会")
    tags: List[str] = Field(default_factory=list, description="个人标签或关键词")

class BatchDetectionResult(BaseModel):
    """批量检测结果模型"""
    results: List[IntroductionDetection] = Field(description="检测结果列表，按输入消息顺序排列")

class BatchExtractionResult(BaseModel):
    """批量提取结果模型"""
    results: List[ExtractedProfile] = Field(description="提取结果列表，按输入自我介绍顺序排列")

class AIAnalysisResult(BaseModel):
    """AI分析结果的完整记录"""
    request_id: str = Field(description="请求ID")
    timestamp: datetime = Field(description="分析时间")
    input_text: str = Field(description="输入文本")
    analysis_type: str = Field(description="分析类型：detection或extraction")
    ai_response_raw: str = Field(description="AI原始响应")
    structured_result: Dict[str, Any] = Field(description="结构化结果")
    processing_time_ms: int = Field(description="处理时间（毫秒）")
    success: bool = Field(description="是否成功")
    error_message: Optional[str] = Field(default=None, description="错误信息")

class DeepSeekClient:
    """DeepSeek API客户端"""
    
    def __init__(self, use_openai_like: bool = None):
        self.config = get_config().deepseek
        self.llm = None
        self.structured_detection_llm = None
        self.structured_extraction_llm = None
        self.ai_analysis_log: List[AIAnalysisResult] = []
        
        # 决定使用哪种客户端
        if use_openai_like is None:
            # 从配置中读取，默认优先使用DeepSeek官方集成
            self.use_openai_like = getattr(self.config, 'use_openai_like', False)
        else:
            self.use_openai_like = use_openai_like
        
        self._initialize_llm()
    
    def _initialize_llm(self):
        """初始化LLM客户端"""
        try:
            if self.use_openai_like:
                # 使用OpenAILike通用方式
                if not OPENAI_LIKE_AVAILABLE:
                    logger.error("OpenAILike不可用，请安装 llama-index-llms-openai-like")
                    raise ImportError("OpenAILike不可用")
                
                self.llm = OpenAILike(
                    model=self.config.model,
                    api_base=self.config.base_url,
                    api_key=self.config.api_key or os.environ.get("DEEPSEEK_API_KEY"),
                    max_tokens=self.config.max_tokens,
                    temperature=self.config.temperature,
                    timeout=self.config.timeout,
                    is_chat_model=True,
                    is_function_calling_model=True,
                    context_window=32768  # DeepSeek默认上下文窗口
                )
                logger.info("使用OpenAILike通用方式初始化DeepSeek客户端成功")
                
            elif DEEPSEEK_AVAILABLE:
                # 使用官方DeepSeek集成
                self.llm = DeepSeek(
                    model=self.config.model,
                    api_key=self.config.api_key or os.environ.get("DEEPSEEK_API_KEY"),
                    api_base=self.config.base_url,
                    max_tokens=self.config.max_tokens,
                    temperature=self.config.temperature,
                    timeout=self.config.timeout
                )
                logger.info("使用官方DeepSeek集成初始化成功")
                
            else:
                # Fallback到OpenAILike
                if not OPENAI_LIKE_AVAILABLE:
                    logger.error("DeepSeek和OpenAILike都不可用，请安装相应的包")
                    raise ImportError("缺少必要的LLM包")
                
                self.llm = OpenAILike(
                    model=self.config.model,
                    api_base=self.config.base_url,
                    api_key=self.config.api_key or os.environ.get("DEEPSEEK_API_KEY"),
                    max_tokens=self.config.max_tokens,
                    temperature=self.config.temperature,
                    timeout=self.config.timeout,
                    is_chat_model=True,
                    is_function_calling_model=True,
                    context_window=32768
                )
                logger.info("使用OpenAILike fallback初始化成功")
            
            # 创建结构化输出LLM
            self.structured_detection_llm = self.llm.as_structured_llm(output_cls=IntroductionDetection)
            self.structured_extraction_llm = self.llm.as_structured_llm(output_cls=ExtractedProfile)
            
            # 创建批量处理的结构化LLM
            self.batch_detection_llm = self.llm.as_structured_llm(output_cls=BatchDetectionResult)
            self.batch_extraction_llm = self.llm.as_structured_llm(output_cls=BatchExtractionResult)
            
            Settings.llm = self.llm
            logger.info(f"结构化LLM初始化完成 (使用{'OpenAILike' if self.use_openai_like else 'DeepSeek官方集成'})")
            
        except Exception as e:
            logger.error(f"DeepSeek客户端初始化失败: {e}")
            raise
    
    async def test_connection(self) -> bool:
        """测试API连接"""
        try:
            test_message = ChatMessage.from_str("请回复'连接成功'")
            
            if hasattr(self.llm, 'achat'):
                response = await self.llm.achat([test_message])
            else:
                # 如果不支持异步，使用同步方法
                response = self.llm.chat([test_message])
            
            result = "连接成功" in str(response) or len(str(response)) > 0
            if result:
                logger.info("DeepSeek API连接测试成功")
            else:
                logger.warning("DeepSeek API连接测试失败：响应为空")
            return result
            
        except Exception as e:
            logger.error(f"测试DeepSeek连接失败: {e}")
            return False
    
    def _log_ai_analysis(self, 
                        request_id: str,
                        input_text: str,
                        analysis_type: str,
                        ai_response_raw: str,
                        structured_result: Dict[str, Any],
                        processing_time_ms: int,
                        success: bool,
                        error_message: Optional[str] = None):
        """记录AI分析结果"""
        log_entry = AIAnalysisResult(
            request_id=request_id,
            timestamp=datetime.now(),
            input_text=input_text[:500] + "..." if len(input_text) > 500 else input_text,  # 截断长文本
            analysis_type=analysis_type,
            ai_response_raw=ai_response_raw,
            structured_result=structured_result,
            processing_time_ms=processing_time_ms,
            success=success,
            error_message=error_message
        )
        
        self.ai_analysis_log.append(log_entry)
        
        # 保持日志大小，只保留最新的1000条记录
        if len(self.ai_analysis_log) > 1000:
            self.ai_analysis_log = self.ai_analysis_log[-1000:]
        
        # 详细日志输出
        logger.info(f"AI分析记录 [ID: {request_id}]")
        logger.info(f"  类型: {analysis_type}")
        logger.info(f"  成功: {success}")
        logger.info(f"  处理时间: {processing_time_ms}ms")
        if success:
            logger.info(f"  结构化结果: {structured_result}")
        else:
            logger.error(f"  错误: {error_message}")
        logger.debug(f"  AI原始响应: {ai_response_raw}")
    
    async def detect_introduction(self, message_content: str) -> Dict[str, Any]:
        """
        使用结构化输出检测消息是否为自我介绍
        
        Args:
            message_content: 消息内容
            
        Returns:
            包含检测结果的字典
        """
        import time
        import uuid
        
        request_id = str(uuid.uuid4())[:8]
        start_time = time.time()
        
        try:
            prompt = self._build_detection_prompt(message_content)
            input_message = ChatMessage.from_str(prompt)
            
            logger.debug(f"开始自我介绍检测 [ID: {request_id}]")
            
            # 使用结构化LLM进行检测
            if hasattr(self.structured_detection_llm, 'achat'):
                response = await self.structured_detection_llm.achat([input_message])
            else:
                response = self.structured_detection_llm.chat([input_message])
            
            processing_time = int((time.time() - start_time) * 1000)
            
            # 获取结构化结果
            structured_result = response.raw  # IntroductionDetection对象
            result_dict = structured_result.dict()
            
            # 记录AI分析结果
            self._log_ai_analysis(
                request_id=request_id,
                input_text=message_content,
                analysis_type="detection",
                ai_response_raw=str(response),
                structured_result=result_dict,
                processing_time_ms=processing_time,
                success=True
            )
            
            logger.info(f"自我介绍检测完成 [ID: {request_id}]: {result_dict['is_introduction']} (置信度: {result_dict['confidence']})")
            
            return {
                'request_id': request_id,
                'is_introduction': result_dict['is_introduction'],
                'confidence': result_dict['confidence'],
                'reason': result_dict['reason'],
                'keywords': result_dict['keywords'],
                'introduction_type': result_dict['introduction_type'],
                'success': True
            }
            
        except Exception as e:
            processing_time = int((time.time() - start_time) * 1000)
            error_msg = str(e)
            
            # 记录失败的分析
            self._log_ai_analysis(
                request_id=request_id,
                input_text=message_content,
                analysis_type="detection",
                ai_response_raw="",
                structured_result={},
                processing_time_ms=processing_time,
                success=False,
                error_message=error_msg
            )
            
            logger.error(f"检测自我介绍失败 [ID: {request_id}]: {e}")
            return {
                'request_id': request_id,
                'is_introduction': False,
                'confidence': 0.0,
                'reason': f'AI检测失败: {error_msg}',
                'keywords': [],
                'introduction_type': 'unknown',
                'success': False,
                'error': error_msg
            }
    
    async def extract_profile(self, message_content: str) -> Dict[str, Any]:
        """
        使用结构化输出从自我介绍中提取用户画像
        
        Args:
            message_content: 自我介绍内容
            
        Returns:
            提取的用户画像信息
        """
        import time
        import uuid
        
        request_id = str(uuid.uuid4())[:8]
        start_time = time.time()
        
        try:
            prompt = self._build_extraction_prompt(message_content)
            input_message = ChatMessage.from_str(prompt)
            
            logger.debug(f"开始用户画像提取 [ID: {request_id}]")
            
            # 使用结构化LLM进行提取
            if hasattr(self.structured_extraction_llm, 'achat'):
                response = await self.structured_extraction_llm.achat([input_message])
            else:
                response = self.structured_extraction_llm.chat([input_message])
            
            processing_time = int((time.time() - start_time) * 1000)
            
            # 获取结构化结果
            structured_result = response.raw  # ExtractedProfile对象
            result_dict = structured_result.dict()
            
            # 记录AI分析结果
            self._log_ai_analysis(
                request_id=request_id,
                input_text=message_content,
                analysis_type="extraction",
                ai_response_raw=str(response),
                structured_result=result_dict,
                processing_time_ms=processing_time,
                success=True
            )
            
            logger.info(f"用户画像提取完成 [ID: {request_id}]: {result_dict.get('industry', '未知行业')}")
            
            return {
                'request_id': request_id,
                'success': True,
                'profile': result_dict
            }
            
        except Exception as e:
            processing_time = int((time.time() - start_time) * 1000)
            error_msg = str(e)
            
            # 记录失败的分析
            self._log_ai_analysis(
                request_id=request_id,
                input_text=message_content,
                analysis_type="extraction",
                ai_response_raw="",
                structured_result={},
                processing_time_ms=processing_time,
                success=False,
                error_message=error_msg
            )
            
            logger.error(f"提取用户画像失败 [ID: {request_id}]: {e}")
            return {
                'request_id': request_id,
                'success': False,
                'error': error_msg,
                'profile': {}
            }
    
    def _build_detection_prompt(self, message_content: str) -> str:
        """构建自我介绍检测提示词"""
        prompt = f"""
你是一个专业的文本分析专家，需要判断以下消息是否为微信群中的自我介绍。

自我介绍的典型特征：
1. 包含个人基本信息（姓名、昵称、职业、工作等）
2. 介绍自己的技能、经验或专业领域
3. 说明自己能提供什么帮助或服务
4. 表达希望得到什么帮助或寻求什么机会
5. 使用第一人称进行自我描述
6. 通常包含"大家好"、"我是"、"我叫"、"我做"等开场白
7. 有明确的自我介绍意图

自我介绍类型分类：
- professional: 主要介绍职业、工作相关信息
- personal: 主要介绍个人生活、兴趣爱好
- service: 主要介绍能提供的服务或产品
- mixed: 混合多种类型的介绍

请分析以下消息：
=================
{message_content}
=================

请判断这是否为自我介绍，并提供详细的分析。严格判断，只有非常确定时才判断为是自我介绍。
"""
        return prompt
    
    def _build_extraction_prompt(self, message_content: str) -> str:
        """构建用户画像提取提示词"""
        prompt = f"""
你是一个专业的信息提取专家，需要从微信群自我介绍中提取结构化的用户画像信息。

请从以下自我介绍中提取信息：
=================
{message_content}
=================

提取要求：
1. 只提取明确提到的信息，不要推测或添加未提及的内容
2. 如果某项信息未提及，请设置为null或空列表
3. 能提供的服务和需要的帮助请提取成列表形式
4. 标签应该是能概括此人特点的关键词
5. 个人介绍应该简洁概括此人的主要信息

需要提取的信息：
- 所在行业
- 个人介绍或描述
- 地区或城市
- 能提供的服务或资源
- 需要的帮助或寻求的机会
- 个人标签或关键词
"""
        return prompt
    
    def _build_batch_detection_prompt(self, messages: List[str]) -> str:
        """构建批量检测提示词"""
        prompt = f"""
你是一个专业的文本分析专家，需要批量判断以下消息是否为微信群中的自我介绍。

自我介绍的典型特征：
1. 包含个人基本信息（姓名、昵称、职业、工作等）
2. 介绍自己的技能、经验或专业领域
3. 说明自己能提供什么帮助或服务
4. 表达希望得到什么帮助或寻求什么机会
5. 使用第一人称进行自我描述
6. 通常包含"大家好"、"我是"、"我叫"、"我做"等开场白
7. 有明确的自我介绍意图

自我介绍类型分类：
- professional: 主要介绍职业、工作相关信息
- personal: 主要介绍个人生活、兴趣爱好
- service: 主要介绍能提供的服务或产品
- mixed: 混合多种类型的介绍

请分析以下 {len(messages)} 条消息，严格判断每条消息是否为自我介绍：

"""
        
        for i, message in enumerate(messages, 1):
            prompt += f"\n=== 消息 {i} ===\n{message}\n"
        
        prompt += f"""
请对每条消息进行详细分析并返回结构化结果。按照输入顺序返回 {len(messages)} 个检测结果。
"""
        
        return prompt
    
    def _build_batch_extraction_prompt(self, introduction_contents: List[str]) -> str:
        """构建批量提取提示词"""
        prompt = f"""
你是一个专业的信息提取专家，需要从多条微信群自我介绍中批量提取结构化的用户画像信息。

提取要求：
1. 只提取明确提到的信息，不要推测或添加未提及的内容
2. 如果某项信息未提及，请设置为null或空列表
3. 能提供的服务和需要的帮助请提取成列表形式
4. 标签应该是能概括此人特点的关键词
5. 个人介绍应该简洁概括此人的主要信息

需要提取的信息：
- 所在行业
- 个人介绍或描述
- 地区或城市
- 能提供的服务或资源
- 需要的帮助或寻求的机会
- 个人标签或关键词

请分析以下 {len(introduction_contents)} 条自我介绍：

"""
        
        for i, content in enumerate(introduction_contents, 1):
            prompt += f"\n=== 自我介绍 {i} ===\n{content}\n"
        
        prompt += f"""
请为每条自我介绍提取用户画像信息。按照输入顺序返回 {len(introduction_contents)} 个提取结果。
"""
        
        return prompt
    
    def get_analysis_log(self) -> List[Dict[str, Any]]:
        """获取AI分析日志"""
        return [log.dict() for log in self.ai_analysis_log]
    
    def get_analysis_stats(self) -> Dict[str, Any]:
        """获取AI分析统计信息"""
        if not self.ai_analysis_log:
            return {
                'total_requests': 0,
                'success_rate': 0.0,
                'avg_processing_time_ms': 0.0,
                'detection_count': 0,
                'extraction_count': 0
            }
        
        total = len(self.ai_analysis_log)
        successful = len([log for log in self.ai_analysis_log if log.success])
        detection_count = len([log for log in self.ai_analysis_log if log.analysis_type == 'detection'])
        extraction_count = len([log for log in self.ai_analysis_log if log.analysis_type == 'extraction'])
        avg_time = sum(log.processing_time_ms for log in self.ai_analysis_log) / total if total > 0 else 0
        
        return {
            'total_requests': total,
            'success_rate': successful / total if total > 0 else 0.0,
            'avg_processing_time_ms': avg_time,
            'detection_count': detection_count,
            'extraction_count': extraction_count
        }
    
    def _local_prefilter_introduction(self, message: str) -> Dict[str, Any]:
        """
        本地预筛选：使用关键词规则快速过滤明显不是自我介绍的消息
        大幅减少需要AI处理的消息数量
        """
        message_lower = message.lower().strip()
        
        # 明显不是自我介绍的特征（直接排除）
        exclude_patterns = [
            r'^[+\-\s]*$',  # 只有符号
            r'^\d+$',  # 只有数字
            r'^[？？！!。.，,、]+$',  # 只有标点
            r'^(好的|收到|谢谢|ok|👍|👌|💪|🙏|😊)$',  # 简单回复
            r'^@',  # @某人
            r'http[s]?://',  # 包含链接
            r'^\[图片\]|\[表情\]|\[文件\]',  # 微信特殊消息
        ]
        
        for pattern in exclude_patterns:
            if re.search(pattern, message_lower):
                return {
                    'is_introduction': False,
                    'confidence': 0.95,
                    'reason': '本地规则过滤：不符合自我介绍格式',
                    'keywords': [],
                    'introduction_type': 'filtered',
                    'success': True,
                    'local_filtered': True
                }
        
        # 长度过滤
        if len(message.strip()) < 10:
            return {
                'is_introduction': False,
                'confidence': 0.9,
                'reason': '本地规则过滤：内容过短',
                'keywords': [],
                'introduction_type': 'filtered',
                'success': True,
                'local_filtered': True
            }
        
        if len(message.strip()) > 1000:
            return {
                'is_introduction': False,
                'confidence': 0.8,
                'reason': '本地规则过滤：内容过长',
                'keywords': [],
                'introduction_type': 'filtered',
                'success': True,
                'local_filtered': True
            }
        
        # 自我介绍的积极特征（提高通过率）
        positive_keywords = [
            '大家好', '我是', '我叫', '我来自', '我在', '我做', '我从事',
            '自我介绍', '认识一下', '交个朋友', '请多指教',
            '我能提供', '我可以', '我需要', '我希望', '寻求',
            '行业', '公司', '工作', '专业', '经验', '技能',
            '地区', '城市', '联系', '合作', '资源', '服务'
        ]
        
        found_positive = 0
        for keyword in positive_keywords:
            if keyword in message:
                found_positive += 1
        
        # 如果有足够的积极特征，标记为可能的自我介绍
        if found_positive >= 2:
            return {
                'is_introduction': True,
                'confidence': min(0.7 + found_positive * 0.05, 0.95),
                'reason': f'本地规则筛选：发现{found_positive}个积极特征',
                'keywords': [kw for kw in positive_keywords if kw in message][:5],
                'introduction_type': 'potential',
                'success': True,
                'local_filtered': True
            }
        
        # 其他情况需要AI判断
        return None
    
    async def batch_detect_introductions_optimized(self, messages: List[str], 
                                                  use_local_filter: bool = True,
                                                  real_batch_size: int = 10,
                                                  cancel_event: Optional[asyncio.Event] = None) -> List[Dict[str, Any]]:
        """
        极度优化的批量检测：本地预筛选 + 简单文本批量处理
        彻底避免结构化输出兼容性问题
        
        Args:
            messages: 消息内容列表
            use_local_filter: 是否使用本地预筛选（默认True）
            real_batch_size: 真正的批量大小，一次发送给AI的消息数量
            cancel_event: 取消事件
            
        Returns:
            检测结果列表
        """
        import time
        import re
        
        logger.info(f"开始极度优化检测 {len(messages)} 条消息，批量大小: {real_batch_size}")
        
        results = [None] * len(messages)
        ai_needed_indices = []  # 需要AI处理的消息索引
        
        # 第一阶段：本地预筛选
        if use_local_filter:
            logger.info("第一阶段：本地预筛选...")
            local_filtered = 0
            local_positive = 0
            
            for i, message in enumerate(messages):
                if cancel_event and cancel_event.is_set():
                    raise asyncio.CancelledError("检测被取消")
                
                local_result = self._local_prefilter_introduction(message)
                if local_result:
                    results[i] = local_result
                    local_filtered += 1
                    if local_result['is_introduction']:
                        local_positive += 1
                else:
                    ai_needed_indices.append(i)
            
            logger.info(f"本地预筛选完成：{local_filtered} 条已处理（{local_positive} 个自我介绍），{len(ai_needed_indices)} 条需要AI处理")
            
            # 如果本地筛选已经处理了大部分，直接返回
            if len(ai_needed_indices) == 0:
                logger.info("所有消息已通过本地筛选处理完成，无需AI")
                return [r for r in results if r is not None]
        else:
            ai_needed_indices = list(range(len(messages)))
        
        # 第二阶段：AI简单文本批量处理
        if ai_needed_indices:
            logger.info(f"第二阶段：AI简单文本批量处理 {len(ai_needed_indices)} 条消息...")
            
            # 简单批量处理：分组发送给AI，使用普通LLM
            start_time = time.time()
            
            for batch_start in range(0, len(ai_needed_indices), real_batch_size):
                if cancel_event and cancel_event.is_set():
                    raise asyncio.CancelledError("AI批量检测被取消")
                
                batch_indices = ai_needed_indices[batch_start:batch_start + real_batch_size]
                batch_messages = [messages[i] for i in batch_indices]
                
                try:
                    # 构建简单的批量提示词
                    simple_batch_prompt = self._build_simple_batch_detection_prompt(batch_messages)
                    
                    # 使用普通LLM（避免结构化输出问题）
                    from llama_index.core.llms import ChatMessage
                    input_message = ChatMessage.from_str(simple_batch_prompt)
                    
                    logger.info(f"发送简单批量请求：{len(batch_messages)} 条消息")
                    
                    if hasattr(self.llm, 'achat'):
                        response = await self.llm.achat([input_message], timeout=60)
                    else:
                        response = self.llm.chat([input_message])
                    
                    # 简单文本解析
                    response_text = str(response)
                    batch_results = self._parse_simple_batch_detection(response_text, len(batch_messages))
                    
                    # 将结果映射回原始索引
                    for j, detection_result in enumerate(batch_results[:len(batch_indices)]):
                        original_index = batch_indices[j]
                        results[original_index] = detection_result
                    
                    logger.info(f"简单批量 {batch_start//real_batch_size + 1} 完成：{len(batch_results)} 个结果")
                    
                    # 批次间隔，避免API限制
                    if batch_start + real_batch_size < len(ai_needed_indices):
                        await asyncio.sleep(1)  # 1秒间隔
                
                except Exception as e:
                    logger.error(f"AI简单批量处理失败，回退到单条处理: {e}")
                    
                    # 回退：对失败的批次使用单条处理
                    for j, original_index in enumerate(batch_indices):
                        try:
                            single_result = await self.detect_introduction(messages[original_index])
                            results[original_index] = single_result
                        except Exception as single_e:
                            logger.warning(f"单条处理也失败，消息 {original_index}: {single_e}")
                            results[original_index] = {
                                'is_introduction': False,
                                'confidence': 0.0,
                                'reason': f'处理失败: {single_e}',
                                'keywords': [],
                                'introduction_type': 'error',
                                'success': False,
                                'error': str(single_e),
                                'local_filtered': False
                            }
                        
                        # 单条处理间隔
                        await asyncio.sleep(0.3)
            
            ai_time = time.time() - start_time
            logger.info(f"AI处理完成，耗时 {ai_time:.2f}秒")
        
        # 统计和返回结果
        final_results = [r for r in results if r is not None]
        
        # 填充遗漏的结果
        for i, result in enumerate(results):
            if result is None:
                final_results.insert(i, {
                    'is_introduction': False,
                    'confidence': 0.0,
                    'reason': '处理遗漏',
                    'keywords': [],
                    'introduction_type': 'unknown',
                    'success': False,
                    'error': '处理遗漏',
                    'local_filtered': False
                })
        
        # 统计信息
        total_introductions = sum(1 for r in final_results if r.get('is_introduction', False))
        local_processed = sum(1 for r in final_results if r.get('local_filtered', False))
        ai_processed = len(final_results) - local_processed
        actual_ai_requests = max(1, (ai_processed + real_batch_size - 1) // real_batch_size) if ai_processed > 0 else 0
        
        logger.info(f"极度优化检测完成:")
        logger.info(f"  总消息: {len(final_results)} 条")
        logger.info(f"  发现自我介绍: {total_introductions} 个")
        logger.info(f"  本地处理: {local_processed} 条")
        logger.info(f"  AI处理: {ai_processed} 条")
        logger.info(f"  实际API请求次数: {actual_ai_requests} 次（vs 原来的 {len(messages)} 次）")
        if len(messages) > 0:
            logger.info(f"  请求减少: {(1 - actual_ai_requests/len(messages))*100:.1f}%")
        
        return final_results
    
    def _build_simple_batch_detection_prompt(self, messages: List[str]) -> str:
        """构建简单的批量检测提示词（避免结构化输出）"""
        prompt = f"""请判断以下 {len(messages)} 条微信群消息是否为自我介绍。

自我介绍特征：包含个人信息、能力介绍、服务提供、需求表达、使用第一人称等。

请逐条分析并回答，格式为：
1. 是/否 - 理由
2. 是/否 - 理由
...

消息列表：
"""
        
        for i, message in enumerate(messages, 1):
            # 限制每条消息长度，避免提示词过长
            display_message = message[:200] + "..." if len(message) > 200 else message
            prompt += f"\n{i}. {display_message}"
        
        prompt += f"""

请严格按照"序号. 是/否 - 理由"的格式回答，不要添加其他内容。"""
        
        return prompt
    
    def _parse_simple_batch_detection(self, response_text: str, expected_count: int) -> List[Dict[str, Any]]:
        """解析简单批量检测的文本响应"""
        results = []
        lines = response_text.split('\n')
        
        found_results = 0
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 查找格式：数字. 是/否 - 理由
            import re
            match = re.match(r'(\d+)\.\s*(是|否)\s*[-\-—]\s*(.+)', line)
            if match:
                number, answer, reason = match.groups()
                is_intro = answer == '是'
                confidence = 0.8 if is_intro else 0.9
                
                results.append({
                    'is_introduction': is_intro,
                    'confidence': confidence,
                    'reason': f'AI批量判断: {reason.strip()}',
                    'keywords': [],
                    'introduction_type': 'professional' if is_intro else 'not_intro',
                    'success': True,
                    'local_filtered': False
                })
                found_results += 1
                
                if found_results >= expected_count:
                    break
            
            # 备用解析：简单查找是/否
            elif any(word in line for word in ['是', '否']) and found_results < expected_count:
                is_intro = '是' in line and '否' not in line
                confidence = 0.7
                
                results.append({
                    'is_introduction': is_intro,
                    'confidence': confidence,
                    'reason': f'AI简单判断: {line[:50]}...',
                    'keywords': [],
                    'introduction_type': 'unknown',
                    'success': True,
                    'local_filtered': False
                })
                found_results += 1
        
        # 如果解析结果不足，补充默认值
        while len(results) < expected_count:
            results.append({
                'is_introduction': False,
                'confidence': 0.0,
                'reason': '批量解析失败',
                'keywords': [],
                'introduction_type': 'error',
                'success': False,
                'error': '解析失败',
                'local_filtered': False
            })
        
        return results[:expected_count]
    
    def _parse_batch_detection_text(self, response_text: str, expected_count: int) -> List[IntroductionDetection]:
        """
        解析批量检测的文本响应（回退方案）
        """
        results = []
        lines = response_text.split('\n')
        
        # 简单的文本解析逻辑
        found_results = 0
        for line in lines:
            line = line.strip().lower()
            if any(word in line for word in ['是', '否', 'true', 'false', '自我介绍']):
                is_intro = '是' in line or 'true' in line or ('自我介绍' in line and '不是' not in line)
                confidence = 0.7 if is_intro else 0.8
                
                results.append(IntroductionDetection(
                    is_introduction=is_intro,
                    confidence=confidence,
                    reason=f'文本解析: {line[:50]}...',
                    keywords=[],
                    introduction_type='parsed'
                ))
                found_results += 1
                
                if found_results >= expected_count:
                    break
        
        # 如果解析结果不足，补充默认值
        while len(results) < expected_count:
            results.append(IntroductionDetection(
                is_introduction=False,
                confidence=0.0,
                reason='文本解析失败',
                keywords=[],
                introduction_type='error'
            ))
        
        return results[:expected_count]
    
    def _parse_batch_extraction_text(self, response_text: str, expected_count: int) -> List[ExtractedProfile]:
        """
        解析批量提取的文本响应（回退方案）
        """
        results = []
        
        # 简单的文本解析逻辑 - 为每个预期结果创建空的profile
        for i in range(expected_count):
            results.append(ExtractedProfile(
                industry=None,
                personal_intro=f'文本解析失败，原始响应片段: {response_text[:100]}...',
                location=None,
                can_provide=[],
                need_help=[],
                tags=['解析失败']
            ))
        
        return results

    # 向后兼容的方法别名
    async def batch_detect_introductions(self, messages: List[str], 
                                       batch_size: int = 15, 
                                       max_concurrent: int = 5, 
                                       cancel_event: Optional[asyncio.Event] = None,
                                       use_progressive: bool = True) -> List[Dict[str, Any]]:
        """
        向后兼容的方法：可选择使用递进式筛选或简单批量处理
        
        Args:
            messages: 消息内容列表
            batch_size: 批量大小（递进式时为chunk_size）
            max_concurrent: 兼容参数，不再使用
            cancel_event: 取消事件
            use_progressive: 是否使用递进式筛选（默认True，推荐）
            
        Returns:
            检测结果列表
        """
        if use_progressive:
            # 使用创新的递进式筛选方法
            chunk_size = min(batch_size * 3, 50)  # 递进式可以用更大的块
            return await self.progressive_batch_detect_introductions(
                messages=messages,
                chunk_size=chunk_size,
                cancel_event=cancel_event
            )
        else:
            # 使用原来的简单批量方法
            real_batch_size = min(batch_size, 15)
            return await self.batch_detect_introductions_optimized(
                messages=messages,
                use_local_filter=True,
                real_batch_size=real_batch_size,
                cancel_event=cancel_event
            )
    
    async def batch_extract_profiles(self, introduction_contents: List[str], 
                                   batch_size: int = 8, 
                                   max_concurrent: int = 3, 
                                   cancel_event: Optional[asyncio.Event] = None) -> List[Dict[str, Any]]:
        """
        简化的用户画像提取：完全避免结构化输出问题，使用稳定的单条处理
        
        Args:
            introduction_contents: 自我介绍内容列表
            batch_size: 兼容参数，实际使用单条处理
            max_concurrent: 兼容参数，实际使用串行处理
            cancel_event: 取消事件
            
        Returns:
            提取结果列表
        """
        import time
        
        logger.info(f"开始稳定提取 {len(introduction_contents)} 个用户画像（避免批量结构化输出问题）")
        
        results = []
        start_time = time.time()
        
        for i, content in enumerate(introduction_contents):
            if cancel_event and cancel_event.is_set():
                raise asyncio.CancelledError("用户画像提取被取消")
            
            try:
                # 使用现有的稳定单条提取方法（已验证可靠）
                result = await self.extract_profile(content)
                results.append(result)
                
                # 进度报告
                if (i + 1) % 5 == 0 or i == len(introduction_contents) - 1:
                    progress = (i + 1) / len(introduction_contents) * 100
                    elapsed = time.time() - start_time
                    speed = (i + 1) / elapsed if elapsed > 0 else 0
                    eta = (len(introduction_contents) - i - 1) / speed if speed > 0 else 0
                    
                    logger.info(f"提取进度: {i+1}/{len(introduction_contents)} ({progress:.1f}%) - 速度: {speed:.1f} 个/秒 - 预计剩余: {eta:.1f}秒")
                
                # 合理的请求间隔，避免API限制
                if i < len(introduction_contents) - 1:
                    await asyncio.sleep(1.0)  # 1秒间隔，比较安全
                    
            except Exception as e:
                logger.warning(f"自我介绍 {i+1} 提取失败: {e}")
                results.append({
                    'success': False,
                    'error': str(e),
                    'profile': {}
                })
                
                # 失败后等待更长时间
                await asyncio.sleep(2)
        
        total_time = time.time() - start_time
        successful_extractions = sum(1 for r in results if r.get('success', False))
        
        logger.info(f"稳定提取完成:")
        logger.info(f"  总自我介绍: {len(results)} 个")
        logger.info(f"  成功提取: {successful_extractions} 个")
        logger.info(f"  成功率: {successful_extractions/len(results)*100:.1f}%")
        logger.info(f"  总耗时: {total_time:.2f}秒")
        logger.info(f"  平均速度: {len(results)/total_time:.1f} 个/秒")
        
        return results

    async def progressive_batch_detect_introductions(self, messages: List[str], 
                                                   chunk_size: int = 50,
                                                   cancel_event: Optional[asyncio.Event] = None) -> List[Dict[str, Any]]:
        """
        多轮递进式筛选：大幅减少API请求的创新方法
        
        流程：
        1. 粗筛选：大段聊天记录 → 可能的自我介绍（条件宽松）
        2. 精筛选：筛选结果 → 确定的自我介绍（条件严格）
        3. 结构化：最终结果 → 结构化信息提取
        
        Args:
            messages: 消息内容列表
            chunk_size: 每次发送给AI的消息数量
            cancel_event: 取消事件
            
        Returns:
            检测结果列表
        """
        import time
        
        logger.info(f"开始多轮递进式筛选 {len(messages)} 条消息，块大小: {chunk_size}")
        
        # 初始化结果
        results = [None] * len(messages)
        start_time = time.time()
        
        # 第零轮：本地预筛选（保留原有优势）
        logger.info("第0轮：本地预筛选...")
        local_filtered_indices = []
        remaining_indices = []
        
        for i, message in enumerate(messages):
            if cancel_event and cancel_event.is_set():
                raise asyncio.CancelledError("筛选被取消")
            
            local_result = self._local_prefilter_introduction(message)
            if local_result:
                results[i] = local_result
                local_filtered_indices.append(i)
            else:
                remaining_indices.append(i)
        
        logger.info(f"本地预筛选完成：{len(local_filtered_indices)} 条已处理，{len(remaining_indices)} 条需要AI处理")
        
        if not remaining_indices:
            logger.info("所有消息已通过本地筛选处理完成")
            return [r for r in results if r is not None]
        
        # 第一轮：粗筛选 - 找出可能的自我介绍
        logger.info("第1轮：AI粗筛选（条件宽松）...")
        potential_introductions = {}  # {original_index: message}
        
        for chunk_start in range(0, len(remaining_indices), chunk_size):
            if cancel_event and cancel_event.is_set():
                raise asyncio.CancelledError("粗筛选被取消")
            
            chunk_indices = remaining_indices[chunk_start:chunk_start + chunk_size]
            chunk_messages = [messages[i] for i in chunk_indices]
            
            try:
                # 构建粗筛选提示词
                coarse_prompt = self._build_coarse_filter_prompt(chunk_messages, chunk_indices)
                
                # 发送给AI
                from llama_index.core.llms import ChatMessage
                input_message = ChatMessage.from_str(coarse_prompt)
                
                logger.info(f"粗筛选块 {chunk_start//chunk_size + 1}: {len(chunk_messages)} 条消息")
                
                if hasattr(self.llm, 'achat'):
                    response = await self.llm.achat([input_message], timeout=60)
                else:
                    response = self.llm.chat([input_message])
                
                # 解析粗筛选结果
                response_text = str(response)
                potential_indices = self._parse_coarse_filter_response(response_text, chunk_indices)
                
                # 记录可能的自我介绍
                for idx in potential_indices:
                    potential_introductions[idx] = messages[idx]
                
                logger.info(f"粗筛选块 {chunk_start//chunk_size + 1} 完成：发现 {len(potential_indices)} 个可能的自我介绍")
                
                # 间隔
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"粗筛选失败: {e}")
                # 失败的话，保守地认为都可能是自我介绍
                for idx in chunk_indices:
                    potential_introductions[idx] = messages[idx]
        
        coarse_time = time.time() - start_time
        logger.info(f"粗筛选完成：从 {len(remaining_indices)} 条中筛选出 {len(potential_introductions)} 条可能的自我介绍，耗时 {coarse_time:.2f}秒")
        
        if not potential_introductions:
            logger.info("粗筛选未发现可能的自我介绍")
            # 为剩余的消息设置默认结果
            for idx in remaining_indices:
                results[idx] = {
                    'is_introduction': False,
                    'confidence': 0.9,
                    'reason': '粗筛选未通过',
                    'keywords': [],
                    'introduction_type': 'filtered',
                    'success': True,
                    'local_filtered': False
                }
            return [r for r in results if r is not None]
        
        # 第二轮：精筛选 - 严格判断是否为自我介绍
        logger.info("第2轮：AI精筛选（条件严格）...")
        confirmed_introductions = {}  # {original_index: detection_result}
        
        potential_list = list(potential_introductions.items())
        fine_chunk_size = min(20, len(potential_list))  # 精筛选用更小的块
        
        for chunk_start in range(0, len(potential_list), fine_chunk_size):
            if cancel_event and cancel_event.is_set():
                raise asyncio.CancelledError("精筛选被取消")
            
            chunk_items = potential_list[chunk_start:chunk_start + fine_chunk_size]
            chunk_indices = [item[0] for item in chunk_items]
            chunk_messages = [item[1] for item in chunk_items]
            
            try:
                # 构建精筛选提示词
                fine_prompt = self._build_fine_filter_prompt(chunk_messages)
                
                # 发送给AI
                input_message = ChatMessage.from_str(fine_prompt)
                
                logger.info(f"精筛选块 {chunk_start//fine_chunk_size + 1}: {len(chunk_messages)} 条消息")
                
                if hasattr(self.llm, 'achat'):
                    response = await self.llm.achat([input_message], timeout=60)
                else:
                    response = self.llm.chat([input_message])
                
                # 解析精筛选结果
                response_text = str(response)
                fine_results = self._parse_fine_filter_response(response_text, len(chunk_messages))
                
                # 将结果映射回原始索引
                for j, detection_result in enumerate(fine_results):
                    if j < len(chunk_indices):
                        original_idx = chunk_indices[j]
                        results[original_idx] = detection_result
                        
                        if detection_result['is_introduction']:
                            confirmed_introductions[original_idx] = chunk_messages[j]
                
                logger.info(f"精筛选块 {chunk_start//fine_chunk_size + 1} 完成")
                
                # 间隔
                await asyncio.sleep(0.5)
                
            except Exception as e:
                logger.error(f"精筛选失败: {e}")
                # 失败的话，回退到单条处理
                for j, original_idx in enumerate(chunk_indices):
                    try:
                        single_result = await self.detect_introduction(chunk_messages[j])
                        results[original_idx] = single_result
                        if single_result['is_introduction']:
                            confirmed_introductions[original_idx] = chunk_messages[j]
                    except Exception as single_e:
                        logger.warning(f"单条精筛选失败: {single_e}")
                        results[original_idx] = {
                            'is_introduction': False,
                            'confidence': 0.0,
                            'reason': f'精筛选失败: {single_e}',
                            'keywords': [],
                            'introduction_type': 'error',
                            'success': False,
                            'error': str(single_e),
                            'local_filtered': False
                        }
                    await asyncio.sleep(0.3)
        
        # 为未处理的消息设置默认结果
        for idx in remaining_indices:
            if results[idx] is None:
                results[idx] = {
                    'is_introduction': False,
                    'confidence': 0.8,
                    'reason': '未通过精筛选',
                    'keywords': [],
                    'introduction_type': 'filtered',
                    'success': True,
                    'local_filtered': False
                }
        
        total_time = time.time() - start_time
        final_results = [r for r in results if r is not None]
        total_introductions = sum(1 for r in final_results if r.get('is_introduction', False))
        
        # 计算API请求次数
        coarse_requests = max(1, (len(remaining_indices) + chunk_size - 1) // chunk_size)
        fine_requests = max(1, (len(potential_introductions) + fine_chunk_size - 1) // fine_chunk_size)
        total_api_requests = coarse_requests + fine_requests
        
        logger.info(f"多轮递进式筛选完成:")
        logger.info(f"  总消息: {len(final_results)} 条")
        logger.info(f"  发现自我介绍: {total_introductions} 个")
        logger.info(f"  本地处理: {len(local_filtered_indices)} 条")
        logger.info(f"  粗筛选: {len(remaining_indices)} → {len(potential_introductions)} 条")
        logger.info(f"  精筛选: {len(potential_introductions)} → {len(confirmed_introductions)} 条")
        logger.info(f"  API请求次数: {total_api_requests} 次（vs 原来的 {len(messages)} 次）")
        logger.info(f"  请求减少: {(1 - total_api_requests/len(messages))*100:.1f}%")
        logger.info(f"  总耗时: {total_time:.2f}秒")
        
        return final_results
    
    def _build_coarse_filter_prompt(self, messages: List[str], indices: List[int]) -> str:
        """构建粗筛选提示词（条件宽松）"""
        prompt = f"""你是聊天记录分析专家。请从以下 {len(messages)} 条微信群消息中，找出所有**可能**是自我介绍的消息。

粗筛选标准（条件宽松）：
- 包含个人信息（姓名、职业、地区等）
- 提到自己的能力、经验、服务
- 表达需求或寻求合作
- 使用第一人称介绍自己
- 任何形式的自我展示

请返回可能的自我介绍的编号（多个编号用逗号分隔），如果没有则返回"无"。

消息列表：
"""
        
        for i, message in enumerate(messages, 1):
            display_message = message[:150] + "..." if len(message) > 150 else message
            prompt += f"\n{i}. {display_message}"
        
        prompt += f"""

请回答格式：可能的自我介绍编号：1,3,5,7（如果没有则回答"无"）"""
        
        return prompt
    
    def _build_fine_filter_prompt(self, messages: List[str]) -> str:
        """构建精筛选提示词（条件严格）"""
        prompt = f"""请严格判断以下 {len(messages)} 条消息是否为自我介绍。

严格标准：
- 明确的自我介绍意图
- 包含完整的个人信息介绍
- 不是简单的聊天或回复
- 有实质性的自我展示内容

请逐条回答，格式：
1. 是/否 - 理由
2. 是/否 - 理由
...

消息列表：
"""
        
        for i, message in enumerate(messages, 1):
            display_message = message[:200] + "..." if len(message) > 200 else message
            prompt += f"\n{i}. {display_message}"
        
        prompt += f"""

请严格按照"序号. 是/否 - 理由"的格式回答。"""
        
        return prompt
    
    def _parse_coarse_filter_response(self, response_text: str, chunk_indices: List[int]) -> List[int]:
        """解析粗筛选响应，返回可能的自我介绍的原始索引"""
        import re
        
        potential_indices = []
        
        # 查找编号模式
        numbers = re.findall(r'(\d+)', response_text)
        
        for num_str in numbers:
            try:
                num = int(num_str)
                if 1 <= num <= len(chunk_indices):
                    original_idx = chunk_indices[num - 1]
                    potential_indices.append(original_idx)
            except ValueError:
                continue
        
        # 如果没有找到数字，检查是否有"无"或"没有"
        if not potential_indices and any(word in response_text for word in ['无', '没有', 'none', '0']):
            return []
        
        # 如果解析失败，保守地返回所有索引
        if not potential_indices and not any(word in response_text for word in ['无', '没有', 'none']):
            logger.warning("粗筛选解析失败，保守地认为所有消息都可能是自我介绍")
            return chunk_indices
        
        return potential_indices
    
    def _parse_fine_filter_response(self, response_text: str, expected_count: int) -> List[Dict[str, Any]]:
        """解析精筛选响应"""
        results = []
        lines = response_text.split('\n')
        
        found_results = 0
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 查找格式：数字. 是/否 - 理由
            import re
            match = re.match(r'(\d+)\.\s*(是|否)\s*[-\-—]\s*(.+)', line)
            if match:
                number, answer, reason = match.groups()
                is_intro = answer == '是'
                confidence = 0.85 if is_intro else 0.9
                
                results.append({
                    'is_introduction': is_intro,
                    'confidence': confidence,
                    'reason': f'精筛选: {reason.strip()}',
                    'keywords': [],
                    'introduction_type': 'confirmed' if is_intro else 'rejected',
                    'success': True,
                    'local_filtered': False
                })
                found_results += 1
                
                if found_results >= expected_count:
                    break
        
        # 补充缺失的结果
        while len(results) < expected_count:
            results.append({
                'is_introduction': False,
                'confidence': 0.0,
                'reason': '精筛选解析失败',
                'keywords': [],
                'introduction_type': 'error',
                'success': False,
                'error': '解析失败',
                'local_filtered': False
            })
        
        return results[:expected_count]

# 保持兼容性的同步客户端
class SyncDeepSeekClient:
    """同步DeepSeek客户端（为了向后兼容）"""
    
    def __init__(self):
        self.async_client = DeepSeekClient()
    
    def test_connection(self) -> bool:
        """测试API连接"""
        return asyncio.run(self.async_client.test_connection())
    
    def detect_introduction(self, message_content: str) -> Dict[str, Any]:
        """检测自我介绍"""
        return asyncio.run(self.async_client.detect_introduction(message_content))
    
    def extract_profile(self, message_content: str) -> Dict[str, Any]:
        """提取用户画像"""
        return asyncio.run(self.async_client.extract_profile(message_content)) 