"""
群组管理标签页
显示和管理微信群组
"""

import tkinter as tk
from tkinter import messagebox
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import logging
from typing import List

logger = logging.getLogger(__name__)

class GroupsTab:
    """群组管理标签页"""
    
    def __init__(self, parent, analysis_service):
        self.parent = parent
        self.analysis_service = analysis_service
        
        # 创建主框架
        self.frame = ttk.Frame(parent)
        
        # 群组列表
        self.groups = []
        
        # 创建界面
        self._create_widgets()
        
        logger.debug("群组标签页初始化完成")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 工具栏
        toolbar = ttk.Frame(self.frame)
        toolbar.pack(fill=X, padx=10, pady=5)
        
        ttk.Button(
            toolbar,
            text="🔄 刷新群组",
            command=self.refresh_groups,
            bootstyle="primary"
        ).pack(side=LEFT, padx=(0, 10))
        
        ttk.Label(toolbar, text="群组列表:").pack(side=LEFT)
        
        # 群组列表
        list_frame = ttk.Frame(self.frame)
        list_frame.pack(fill=BOTH, expand=True, padx=10, pady=5)
        
        # 创建Treeview
        columns = ("ID", "名称", "成员数", "状态")
        self.tree = ttk.Treeview(list_frame, columns=columns, show="tree headings")
        
        # 设置列
        self.tree.heading("#0", text="")
        self.tree.column("#0", width=0, stretch=False)
        
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=150, anchor="center")
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
    
    def refresh_groups(self):
        """刷新群组列表"""
        try:
            # 清空现有列表
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # 添加示例数据（实际使用时需要异步获取）
            sample_groups = [
                ("1", "测试群组1", "50", "活跃"),
                ("2", "测试群组2", "30", "活跃"),
                ("3", "测试群组3", "100", "活跃"),
            ]
            
            for group in sample_groups:
                self.tree.insert("", "end", values=group)
            
            logger.info("群组列表刷新完成")
            
        except Exception as e:
            logger.error(f"刷新群组失败: {e}")
            messagebox.showerror("错误", f"刷新群组失败:\n{str(e)}") 