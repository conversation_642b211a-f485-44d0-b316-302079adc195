# 飞书多维表格同步配置指南

## 🚀 功能介绍

本系统支持将分析得到的用户画像自动同步到飞书多维表格，方便团队协作和数据管理。

## 📋 配置步骤

### 1. 创建飞书应用

1. 打开 [飞书开放平台](https://open.feishu.cn/)
2. 登录您的飞书账号
3. 点击"创建应用" -> "企业自建应用"
4. 填写应用信息：
   - 应用名称：微信群用户画像分析
   - 应用描述：自动分析微信群自我介绍并同步到飞书
   - 应用图标：选择一个合适的图标

### 2. 获取应用凭证

创建应用后，在应用管理页面找到：
- **App ID**：应用的唯一标识
- **App Secret**：应用密钥

⚠️ **注意**：App Secret 请妥善保管，不要泄露给他人。

### 3. 配置应用权限

在应用管理页面，进入"权限管理"：

#### 必需权限：
- `bitable:app` - 多维表格应用权限
- `bitable:app:readonly` - 读取多维表格
- `bitable:app:readwrite` - 读写多维表格

#### 配置步骤：
1. 点击"权限管理"
2. 搜索并添加上述权限
3. 提交审核（通常几分钟内通过）

### 4. 创建多维表格

1. 在飞书中创建一个新的多维表格
2. 记录表格的 **App Token**（在浏览器地址栏中）
   ```
   https://xxx.feishu.cn/base/YourAppTokenHere?table=xxx
   ```
3. 创建表格字段（系统会自动匹配）：
   - 昵称 (文本)
   - 职业 (文本) 
   - 个人介绍 (多行文本)
   - 能够提供 (多行文本)
   - 寻求帮助 (多行文本)
   - 行业领域 (文本)
   - 置信度 (数字)
   - 消息ID (文本)
   - 群组ID (文本)
   - 创建时间 (日期时间)

### 5. 修改配置文件

编辑 `config/app_config.yaml` 文件：

```yaml
feishu:
  # 是否启用飞书同步
  enabled: true
  
  # 飞书API基础URL (通常不需要修改)
  base_url: "https://open.feishu.cn"
  
  # 应用凭证 (从步骤2获取)
  app_id: "cli_your_app_id_here" 
  app_secret: "your_app_secret_here"
  
  # 多维表格配置
  default_app_token: "your_app_token_here"
  default_table_id: ""  # 留空，系统会自动创建
  
  # 同步设置
  auto_sync: false  # 是否自动同步 (建议设为false，使用手动同步)
  batch_size: 10    # 批量同步大小
  timeout: 30       # 请求超时时间(秒)
```

## 🔧 手动同步使用

### 1. 在分析结果页面同步

1. 完成微信群分析后，进入"分析结果"标签页
2. 点击左侧控制面板中的 **"🚀 同步到飞书"** 按钮
3. 确认同步信息后，系统会显示详细的同步进度
4. 同步完成后，数据会自动出现在飞书多维表格中

### 2. 同步进度监控

同步过程中会显示：
- ✅ 配置检查
- 🔗 飞书连接测试
- 📊 数据同步进度
- 📈 成功/失败统计

## 🛠️ 故障排除

### 常见问题及解决方案

#### 1. "飞书配置不完整"
**原因**：App ID、App Secret 或 App Token 未正确配置
**解决**：检查配置文件中的飞书配置项

#### 2. "无法连接到飞书API"
**原因**：网络问题或应用权限不足
**解决**：
- 检查网络连接
- 确认应用权限已通过审核
- 验证App Secret是否正确

#### 3. "同步失败"
**原因**：表格权限不足或字段不匹配
**解决**：
- 确保应用有表格的读写权限
- 检查表格字段是否按要求创建

#### 4. "部分数据同步失败"
**原因**：单条数据格式问题
**解决**：查看同步详情日志，检查具体失败原因

### 日志查看

同步日志保存在：
- 应用日志：`app/data/logs/app_YYYYMMDD.log`
- 错误日志：`app/data/logs/error_YYYYMMDD.log`

## 💡 最佳实践

### 1. 权限管理
- 只给应用必需的权限
- 定期检查应用权限使用情况

### 2. 数据管理
- 建议为不同项目创建不同的表格
- 定期备份重要数据

### 3. 同步策略
- 推荐使用手动同步，便于控制
- 大量数据时分批次同步

### 4. 安全建议
- 不要在代码中硬编码应用密钥
- 定期更新应用密钥
- 限制应用访问权限

## 📞 技术支持

如果遇到问题，请：

1. 首先查看日志文件获取详细错误信息
2. 检查飞书开放平台的应用状态
3. 确认多维表格的权限设置
4. 提供具体错误信息以获得更好的帮助

---

**注意**：飞书API可能会有更新，请关注 [飞书开放平台文档](https://open.feishu.cn/document/) 获取最新信息。 