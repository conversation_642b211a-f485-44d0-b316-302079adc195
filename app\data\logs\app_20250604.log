2025-06-04 22:17:11,993 - app.utils.logger - INFO - logger.py:81 - 日志系统初始化完成，级别: INFO, 调试模式: True
2025-06-04 22:17:12,037 - app.services.deepseek_client - INFO - deepseek_client.py:51 - 使用官方DeepSeek集成初始化成功
2025-06-04 22:17:12,039 - app.services.deepseek_client - INFO - deepseek_client.py:51 - 使用官方DeepSeek集成初始化成功
2025-06-04 22:17:12,295 - app.ui.results_tab - INFO - results_tab.py:35 - 结果查看标签页初始化完成
2025-06-04 22:17:12,400 - app.ui.main_window - INFO - main_window.py:42 - 主窗口初始化完成
2025-06-04 22:17:12,464 - __main__ - INFO - main.py:62 - 应用程序启动成功
2025-06-04 22:19:02,644 - app.ui.config_tab - INFO - config_tab.py:437 - 配置保存成功
2025-06-04 22:19:03,632 - app.ui.config_tab - INFO - config_tab.py:468 - 配置应用成功
2025-06-04 22:19:10,460 - app.ui.config_tab - INFO - config_tab.py:437 - 配置保存成功
2025-06-04 22:19:11,503 - app.ui.config_tab - INFO - config_tab.py:468 - 配置应用成功
2025-06-04 22:19:14,912 - app.ui.config_tab - INFO - config_tab.py:437 - 配置保存成功
2025-06-04 22:19:17,260 - app.ui.config_tab - INFO - config_tab.py:437 - 配置保存成功
2025-06-04 22:19:17,932 - app.ui.config_tab - INFO - config_tab.py:468 - 配置应用成功
2025-06-04 22:19:40,996 - app.services.deepseek_client - INFO - deepseek_client.py:51 - 使用官方DeepSeek集成初始化成功
2025-06-04 22:19:46,578 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 22:19:49,846 - app.services.deepseek_client - INFO - deepseek_client.py:83 - DeepSeek API连接测试成功
2025-06-04 22:19:50,060 - app.services.feishu_client - ERROR - feishu_client.py:71 - 获取飞书访问令牌异常: name 'timedelta' is not defined
2025-06-04 22:20:48,763 - app.ui.config_tab - INFO - config_tab.py:437 - 配置保存成功
2025-06-04 22:20:49,502 - app.ui.config_tab - INFO - config_tab.py:468 - 配置应用成功
2025-06-04 22:20:53,286 - app.services.chatlog_client - ERROR - chatlog_client.py:53 - 测试Chatlog连接失败: Cannot connect to host 127.0.0.1:5030 ssl:default [[SSL: WRONG_VERSION_NUMBER] wrong version number (_ssl.c:1028)]
2025-06-04 22:20:53,286 - app.services.deepseek_client - INFO - deepseek_client.py:51 - 使用官方DeepSeek集成初始化成功
2025-06-04 22:20:54,265 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 22:20:58,693 - app.services.deepseek_client - INFO - deepseek_client.py:83 - DeepSeek API连接测试成功
2025-06-04 22:20:58,858 - app.services.feishu_client - ERROR - feishu_client.py:71 - 获取飞书访问令牌异常: name 'timedelta' is not defined
2025-06-04 22:21:20,777 - app.ui.config_tab - INFO - config_tab.py:437 - 配置保存成功
2025-06-04 22:21:22,247 - app.services.deepseek_client - INFO - deepseek_client.py:51 - 使用官方DeepSeek集成初始化成功
2025-06-04 22:21:23,222 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 22:21:27,083 - app.services.deepseek_client - INFO - deepseek_client.py:83 - DeepSeek API连接测试成功
2025-06-04 22:21:27,246 - app.services.feishu_client - ERROR - feishu_client.py:71 - 获取飞书访问令牌异常: name 'timedelta' is not defined
2025-06-04 22:21:36,066 - app.ui.config_tab - INFO - config_tab.py:437 - 配置保存成功
2025-06-04 22:21:36,917 - app.ui.config_tab - INFO - config_tab.py:468 - 配置应用成功
2025-06-04 22:22:34,417 - app.ui.groups_tab - INFO - groups_tab.py:89 - 群组列表刷新完成
2025-06-04 22:22:37,232 - app.ui.groups_tab - INFO - groups_tab.py:89 - 群组列表刷新完成
2025-06-04 22:22:38,571 - app.ui.groups_tab - INFO - groups_tab.py:89 - 群组列表刷新完成
2025-06-04 22:22:46,370 - app.ui.config_tab - INFO - config_tab.py:437 - 配置保存成功
2025-06-04 22:37:44,504 - app.utils.logger - INFO - logger.py:81 - 日志系统初始化完成，级别: INFO, 调试模式: True
2025-06-04 22:37:44,548 - app.services.deepseek_client - INFO - deepseek_client.py:51 - 使用官方DeepSeek集成初始化成功
2025-06-04 22:37:44,551 - app.services.deepseek_client - INFO - deepseek_client.py:51 - 使用官方DeepSeek集成初始化成功
2025-06-04 22:37:44,793 - app.ui.results_tab - INFO - results_tab.py:35 - 结果查看标签页初始化完成
2025-06-04 22:37:44,898 - app.ui.main_window - INFO - main_window.py:42 - 主窗口初始化完成
2025-06-04 22:37:44,981 - __main__ - INFO - main.py:62 - 应用程序启动成功
2025-06-04 22:37:49,469 - app.services.deepseek_client - INFO - deepseek_client.py:51 - 使用官方DeepSeek集成初始化成功
2025-06-04 22:37:50,614 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 22:37:54,360 - app.services.deepseek_client - INFO - deepseek_client.py:83 - DeepSeek API连接测试成功
2025-06-04 22:37:54,505 - app.services.feishu_client - ERROR - feishu_client.py:71 - 获取飞书访问令牌异常: name 'timedelta' is not defined
2025-06-04 22:38:02,153 - app.ui.groups_tab - INFO - groups_tab.py:89 - 群组列表刷新完成
2025-06-04 22:38:03,101 - app.ui.groups_tab - INFO - groups_tab.py:89 - 群组列表刷新完成
2025-06-04 22:38:05,217 - app.ui.groups_tab - INFO - groups_tab.py:89 - 群组列表刷新完成
2025-06-04 22:38:06,325 - app.ui.groups_tab - INFO - groups_tab.py:89 - 群组列表刷新完成
2025-06-04 22:38:11,317 - app.ui.groups_tab - INFO - groups_tab.py:89 - 群组列表刷新完成
2025-06-04 22:38:12,261 - app.ui.groups_tab - INFO - groups_tab.py:89 - 群组列表刷新完成
2025-06-04 22:38:18,485 - app.ui.results_tab - INFO - results_tab.py:276 - 结果数据刷新完成
2025-06-04 22:38:29,385 - app.ui.results_tab - INFO - results_tab.py:276 - 结果数据刷新完成
2025-06-04 22:38:30,130 - app.ui.results_tab - INFO - results_tab.py:276 - 结果数据刷新完成
2025-06-04 22:38:34,896 - app.ui.config_tab - INFO - config_tab.py:437 - 配置保存成功
2025-06-04 22:38:36,171 - app.ui.groups_tab - INFO - groups_tab.py:89 - 群组列表刷新完成
2025-06-04 22:38:37,537 - app.ui.groups_tab - INFO - groups_tab.py:89 - 群组列表刷新完成
2025-06-04 22:38:57,190 - app.ui.groups_tab - INFO - groups_tab.py:89 - 群组列表刷新完成
2025-06-04 22:43:24,566 - __main__ - INFO - main.py:112 - 应用程序正常退出
2025-06-04 22:56:01,738 - app.utils.logger - INFO - logger.py:81 - 日志系统初始化完成，级别: INFO, 调试模式: True
2025-06-04 22:56:01,779 - app.services.deepseek_client - INFO - deepseek_client.py:51 - 使用官方DeepSeek集成初始化成功
2025-06-04 22:56:01,782 - app.services.deepseek_client - INFO - deepseek_client.py:51 - 使用官方DeepSeek集成初始化成功
2025-06-04 22:56:02,023 - app.ui.results_tab - INFO - results_tab.py:35 - 结果查看标签页初始化完成
2025-06-04 22:56:02,127 - app.ui.main_window - INFO - main_window.py:42 - 主窗口初始化完成
2025-06-04 22:56:02,196 - __main__ - INFO - main.py:62 - 应用程序启动成功
2025-06-04 22:56:12,016 - app.ui.config_tab - INFO - config_tab.py:437 - 配置保存成功
2025-06-04 22:56:12,676 - app.ui.config_tab - INFO - config_tab.py:468 - 配置应用成功
2025-06-04 22:56:18,191 - app.services.deepseek_client - INFO - deepseek_client.py:51 - 使用官方DeepSeek集成初始化成功
2025-06-04 22:56:19,487 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 22:56:23,712 - app.services.deepseek_client - INFO - deepseek_client.py:83 - DeepSeek API连接测试成功
2025-06-04 22:56:23,954 - app.services.feishu_client - INFO - feishu_client.py:64 - 飞书访问令牌获取成功
2025-06-04 22:56:30,740 - app.ui.groups_tab - INFO - groups_tab.py:89 - 群组列表刷新完成
2025-06-04 22:56:31,681 - app.ui.groups_tab - INFO - groups_tab.py:89 - 群组列表刷新完成
2025-06-04 22:56:37,565 - app.ui.groups_tab - INFO - groups_tab.py:89 - 群组列表刷新完成
2025-06-04 22:56:43,806 - app.ui.results_tab - INFO - results_tab.py:276 - 结果数据刷新完成
2025-06-04 22:56:45,477 - app.ui.groups_tab - INFO - groups_tab.py:89 - 群组列表刷新完成
2025-06-04 22:56:46,500 - app.ui.groups_tab - INFO - groups_tab.py:89 - 群组列表刷新完成
2025-06-04 22:56:48,263 - app.ui.groups_tab - INFO - groups_tab.py:89 - 群组列表刷新完成
2025-06-04 22:59:39,822 - __main__ - INFO - main.py:112 - 应用程序正常退出
2025-06-04 22:59:43,970 - app.utils.logger - INFO - logger.py:81 - 日志系统初始化完成，级别: INFO, 调试模式: True
2025-06-04 22:59:44,014 - app.services.deepseek_client - INFO - deepseek_client.py:51 - 使用官方DeepSeek集成初始化成功
2025-06-04 22:59:44,017 - app.services.deepseek_client - INFO - deepseek_client.py:51 - 使用官方DeepSeek集成初始化成功
2025-06-04 22:59:44,260 - app.ui.results_tab - INFO - results_tab.py:35 - 结果查看标签页初始化完成
2025-06-04 22:59:44,363 - app.ui.main_window - INFO - main_window.py:42 - 主窗口初始化完成
2025-06-04 22:59:44,407 - __main__ - INFO - main.py:62 - 应用程序启动成功
2025-06-04 22:59:49,008 - app.services.chatlog_client - INFO - chatlog_client.py:81 - 获取到 64 个群组
2025-06-04 22:59:49,009 - app.ui.groups_tab - INFO - groups_tab.py:155 - 群组列表刷新完成，共 64 个群组
2025-06-04 23:00:14,862 - app.ui.results_tab - INFO - results_tab.py:276 - 结果数据刷新完成
2025-06-04 23:00:45,027 - app.ui.results_tab - INFO - results_tab.py:276 - 结果数据刷新完成
2025-06-04 23:00:46,897 - app.ui.results_tab - INFO - results_tab.py:276 - 结果数据刷新完成
2025-06-04 23:00:53,317 - app.services.chatlog_client - INFO - chatlog_client.py:81 - 获取到 64 个群组
2025-06-04 23:00:53,320 - app.ui.groups_tab - INFO - groups_tab.py:155 - 群组列表刷新完成，共 64 个群组
2025-06-04 23:01:01,985 - app.ui.results_tab - INFO - results_tab.py:276 - 结果数据刷新完成
2025-06-04 23:01:21,394 - app.ui.results_tab - INFO - results_tab.py:276 - 结果数据刷新完成
2025-06-04 23:02:14,590 - app.ui.config_tab - INFO - config_tab.py:437 - 配置保存成功
2025-06-04 23:03:23,384 - app.utils.logger - INFO - logger.py:81 - 日志系统初始化完成，级别: INFO, 调试模式: True
2025-06-04 23:03:23,426 - app.services.deepseek_client - INFO - deepseek_client.py:51 - 使用官方DeepSeek集成初始化成功
2025-06-04 23:03:23,428 - app.services.deepseek_client - INFO - deepseek_client.py:51 - 使用官方DeepSeek集成初始化成功
2025-06-04 23:03:23,692 - app.ui.results_tab - INFO - results_tab.py:35 - 结果查看标签页初始化完成
2025-06-04 23:03:23,795 - app.ui.main_window - INFO - main_window.py:42 - 主窗口初始化完成
2025-06-04 23:03:23,857 - __main__ - INFO - main.py:62 - 应用程序启动成功
2025-06-04 23:03:40,739 - app.ui.config_tab - INFO - config_tab.py:437 - 配置保存成功
2025-06-04 23:03:49,131 - app.services.chatlog_client - INFO - chatlog_client.py:81 - 获取到 64 个群组
2025-06-04 23:03:49,162 - app.ui.groups_tab - INFO - groups_tab.py:155 - 群组列表刷新完成，共 64 个群组
2025-06-04 23:03:52,163 - app.services.chatlog_client - INFO - chatlog_client.py:81 - 获取到 64 个群组
2025-06-04 23:04:05,093 - app.services.analysis_service - INFO - analysis_service.py:144 - 开始分析群组 58482986673@chatroom，任务ID: 70711b30-f10d-472f-b6e7-825354e67afe
2025-06-04 23:04:05,129 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:05,234 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:05,344 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:05,453 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:05,563 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:05,673 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:05,785 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:05,893 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:06,001 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:06,112 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:06,237 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:06,348 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:06,474 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:06,597 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:06,708 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:06,831 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:06,955 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:07,081 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:07,205 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:07,327 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:07,451 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:07,575 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:07,697 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:07,824 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:07,949 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:08,076 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:08,198 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:08,320 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:08,446 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:08,571 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:08,696 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:08,822 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:08,947 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:09,073 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:09,214 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:09,352 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:09,478 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:09,619 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:09,757 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:09,898 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:10,038 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:10,176 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:10,316 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:10,454 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:10,595 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:10,736 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:10,878 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:11,023 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:11,162 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:11,305 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:11,457 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:11,595 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:11,737 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:11,878 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:12,035 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:12,192 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 10 条消息
2025-06-04 23:04:12,346 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 5 条消息
2025-06-04 23:04:12,346 - app.services.chatlog_client - INFO - chatlog_client.py:183 - 总共获取到 565 条消息
2025-06-04 23:04:12,361 - app.services.introduction_detector - INFO - introduction_detector.py:300 - 开始检测 565 条消息中的自我介绍
2025-06-04 23:04:12,362 - app.services.introduction_detector - INFO - introduction_detector.py:111 - 发现自我介绍候选项: 1748747768000 - 匹配模式: 我.*?可以.*?[，,。]
2025-06-04 23:04:12,362 - app.services.introduction_detector - INFO - introduction_detector.py:111 - 发现自我介绍候选项: 1748768382000 - 匹配模式: 我.*?想.*?[，,。], 我.*?需要.*?[，,。]
2025-06-04 23:04:12,362 - app.services.introduction_detector - INFO - introduction_detector.py:111 - 发现自我介绍候选项: 1748768510000 - 匹配模式: 我.*?需要.*?[，,。]
2025-06-04 23:04:12,362 - app.services.introduction_detector - INFO - introduction_detector.py:111 - 发现自我介绍候选项: 1748777825000 - 匹配模式: 我.*?工作.*?[，,。], 我.*?做.*?[，,。]
2025-06-04 23:04:12,363 - app.services.introduction_detector - INFO - introduction_detector.py:111 - 发现自我介绍候选项: 1748781151000 - 匹配模式: 我.*?可以.*?[，,。], 我.*?会.*?[，,。]
2025-06-04 23:04:12,363 - app.services.introduction_detector - INFO - introduction_detector.py:111 - 发现自我介绍候选项: 1748782459000 - 匹配模式: 我是.*?[，,。]
2025-06-04 23:04:12,363 - app.services.introduction_detector - INFO - introduction_detector.py:111 - 发现自我介绍候选项: 1748783908000 - 匹配模式: 我是.*?[，,。], 大家好.*?我.*?[，,。], 我.*?工作.*?[，,。]
2025-06-04 23:04:12,363 - app.services.introduction_detector - INFO - introduction_detector.py:111 - 发现自我介绍候选项: 1748787073000 - 匹配模式: 我.*?想.*?[，,。]
2025-06-04 23:04:12,364 - app.services.introduction_detector - INFO - introduction_detector.py:111 - 发现自我介绍候选项: 1748839205000 - 匹配模式: 我.*?可以.*?[，,。], 我.*?需要.*?[，,。]
2025-06-04 23:04:12,364 - app.services.introduction_detector - INFO - introduction_detector.py:111 - 发现自我介绍候选项: 1748924573000 - 匹配模式: 我.*?工作.*?[，,。], 我.*?做.*?[，,。]
2025-06-04 23:04:12,364 - app.services.introduction_detector - INFO - introduction_detector.py:111 - 发现自我介绍候选项: 1749017571000 - 匹配模式: 我.*?需要.*?[，,。]
2025-06-04 23:04:12,365 - app.services.introduction_detector - INFO - introduction_detector.py:113 - 模式匹配发现 11 个候选项
2025-06-04 23:04:13,508 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:04:13,514 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:04:13,515 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:04:13,517 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:04:13,518 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:04:13,519 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:04:13,520 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:04:13,520 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:04:13,521 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:04:13,776 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:04:23,121 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:04:29,579 - app.services.introduction_detector - INFO - introduction_detector.py:141 - AI确认了 0/11 个自我介绍
2025-06-04 23:04:29,580 - app.services.introduction_detector - INFO - introduction_detector.py:321 - 检测完成，发现 0 个自我介绍
2025-06-04 23:04:29,596 - app.services.profile_extractor - INFO - profile_extractor.py:41 - 没有确认的自我介绍，跳过画像提取
2025-06-04 23:04:29,596 - app.services.profile_extractor - INFO - profile_extractor.py:358 - 过滤出 0/0 个高质量用户画像
2025-06-04 23:04:29,602 - app.services.analysis_service - INFO - analysis_service.py:306 - 已保存 0 个用户画像到应用状态
2025-06-04 23:04:29,603 - app.services.analysis_service - INFO - analysis_service.py:205 - 未提取到用户画像
2025-06-04 23:27:19,074 - app.utils.logger - INFO - logger.py:81 - 日志系统初始化完成，级别: INFO, 调试模式: True
2025-06-04 23:27:19,118 - app.services.deepseek_client - INFO - deepseek_client.py:92 - 使用官方DeepSeek集成初始化成功
2025-06-04 23:27:19,119 - app.services.deepseek_client - INFO - deepseek_client.py:110 - 结构化LLM初始化完成
2025-06-04 23:27:19,460 - app.ui.results_tab - INFO - results_tab.py:35 - 结果查看标签页初始化完成
2025-06-04 23:27:19,564 - app.ui.main_window - INFO - main_window.py:42 - 主窗口初始化完成
2025-06-04 23:27:19,611 - __main__ - INFO - main.py:62 - 应用程序启动成功
2025-06-04 23:27:39,579 - app.ui.config_tab - INFO - config_tab.py:437 - 配置保存成功
2025-06-04 23:27:44,684 - app.ui.config_tab - INFO - config_tab.py:437 - 配置保存成功
2025-06-04 23:27:49,241 - app.ui.config_tab - INFO - config_tab.py:437 - 配置保存成功
2025-06-04 23:27:50,974 - app.services.chatlog_client - INFO - chatlog_client.py:81 - 获取到 64 个群组
2025-06-04 23:27:51,004 - app.ui.groups_tab - INFO - groups_tab.py:155 - 群组列表刷新完成，共 64 个群组
2025-06-04 23:27:54,623 - app.services.chatlog_client - INFO - chatlog_client.py:81 - 获取到 64 个群组
2025-06-04 23:28:15,376 - app.services.analysis_service - INFO - analysis_service.py:140 - 开始分析群组 58482986673@chatroom，任务ID: e9b7ad12-f131-448e-856c-7d2e59f365c7
2025-06-04 23:28:15,416 - app.services.analysis_service - ERROR - analysis_service.py:239 - 获取消息失败: 'ChatlogClient' object has no attribute 'get_messages'
2025-06-04 23:28:15,417 - app.services.analysis_service - ERROR - analysis_service.py:147 - 分析任务失败: 'ChatlogClient' object has no attribute 'get_messages'
2025-06-04 23:28:31,011 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:28:34,584 - app.services.deepseek_client - INFO - deepseek_client.py:129 - DeepSeek API连接测试成功
2025-06-04 23:28:34,757 - app.services.feishu_client - INFO - feishu_client.py:64 - 飞书访问令牌获取成功
2025-06-04 23:28:54,446 - app.services.analysis_service - INFO - analysis_service.py:140 - 开始分析群组 58482986673@chatroom，任务ID: bcd73c21-9de9-432e-ae6d-78e6336e088b
2025-06-04 23:28:54,455 - app.services.analysis_service - ERROR - analysis_service.py:239 - 获取消息失败: 'ChatlogClient' object has no attribute 'get_messages'
2025-06-04 23:28:54,455 - app.services.analysis_service - ERROR - analysis_service.py:147 - 分析任务失败: 'ChatlogClient' object has no attribute 'get_messages'
2025-06-04 23:29:10,140 - app.services.analysis_service - INFO - analysis_service.py:140 - 开始分析群组 58482986673@chatroom，任务ID: 46f1c99f-d8d7-45b5-8df2-c20690ff17cf
2025-06-04 23:29:10,146 - app.services.analysis_service - ERROR - analysis_service.py:239 - 获取消息失败: 'ChatlogClient' object has no attribute 'get_messages'
2025-06-04 23:29:10,147 - app.services.analysis_service - ERROR - analysis_service.py:147 - 分析任务失败: 'ChatlogClient' object has no attribute 'get_messages'
2025-06-04 23:29:13,360 - app.services.chatlog_client - INFO - chatlog_client.py:81 - 获取到 64 个群组
2025-06-04 23:29:13,405 - app.ui.groups_tab - INFO - groups_tab.py:155 - 群组列表刷新完成，共 64 个群组
2025-06-04 23:29:14,696 - app.services.chatlog_client - INFO - chatlog_client.py:81 - 获取到 64 个群组
2025-06-04 23:29:14,699 - app.ui.groups_tab - INFO - groups_tab.py:155 - 群组列表刷新完成，共 64 个群组
2025-06-04 23:29:17,062 - app.ui.results_tab - INFO - results_tab.py:295 - 未找到已完成的分析结果
2025-06-04 23:29:17,063 - app.ui.results_tab - INFO - results_tab.py:275 - 结果数据刷新完成
2025-06-04 23:29:22,362 - app.ui.results_tab - INFO - results_tab.py:295 - 未找到已完成的分析结果
2025-06-04 23:29:22,363 - app.ui.results_tab - INFO - results_tab.py:275 - 结果数据刷新完成
2025-06-04 23:29:23,821 - app.services.chatlog_client - INFO - chatlog_client.py:81 - 获取到 64 个群组
2025-06-04 23:29:23,824 - app.ui.groups_tab - INFO - groups_tab.py:155 - 群组列表刷新完成，共 64 个群组
2025-06-04 23:29:31,016 - __main__ - INFO - main.py:112 - 应用程序正常退出
2025-06-04 23:37:03,012 - app.utils.logger - INFO - logger.py:81 - 日志系统初始化完成，级别: INFO, 调试模式: True
2025-06-04 23:37:03,055 - app.services.deepseek_client - INFO - deepseek_client.py:86 - 使用官方DeepSeek集成初始化成功
2025-06-04 23:37:03,056 - app.services.deepseek_client - INFO - deepseek_client.py:104 - 结构化LLM初始化完成
2025-06-04 23:37:03,339 - app.ui.results_tab - INFO - results_tab.py:35 - 结果查看标签页初始化完成
2025-06-04 23:37:03,443 - app.ui.main_window - INFO - main_window.py:42 - 主窗口初始化完成
2025-06-04 23:37:03,524 - __main__ - INFO - main.py:62 - 应用程序启动成功
2025-06-04 23:37:07,201 - app.services.chatlog_client - INFO - chatlog_client.py:81 - 获取到 64 个群组
2025-06-04 23:37:07,219 - app.ui.groups_tab - INFO - groups_tab.py:155 - 群组列表刷新完成，共 64 个群组
2025-06-04 23:37:10,670 - app.services.chatlog_client - INFO - chatlog_client.py:81 - 获取到 64 个群组
2025-06-04 23:37:18,461 - app.services.analysis_service - INFO - analysis_service.py:140 - 开始分析群组 58482986673@chatroom，任务ID: 5b63d0f4-db36-4813-9973-757e75a92f3d
2025-06-04 23:37:18,502 - app.services.analysis_service - INFO - analysis_service.py:227 - 正在获取群组 58482986673@chatroom 的消息
2025-06-04 23:37:18,502 - app.services.analysis_service - INFO - analysis_service.py:228 - 时间范围: 2025-05-28 23:37:18 到 2025-06-04 23:37:18
2025-06-04 23:37:18,553 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 565 条消息
2025-06-04 23:37:18,553 - app.services.analysis_service - INFO - analysis_service.py:239 - 获取到 565 条消息
2025-06-04 23:37:18,553 - app.services.analysis_service - INFO - analysis_service.py:249 - 消息统计:
2025-06-04 23:37:18,553 - app.services.analysis_service - INFO - analysis_service.py:250 -   - 总数: 565
2025-06-04 23:37:18,554 - app.services.analysis_service - INFO - analysis_service.py:257 -   - 类型分布: {'text': 467, 'image': 92, 'system': 6}
2025-06-04 23:37:18,554 - app.services.analysis_service - INFO - analysis_service.py:263 -   - 时间范围: 2025-05-28 07:05:20+08:00 到 2025-06-04 21:22:34+08:00
2025-06-04 23:37:18,554 - app.services.analysis_service - ERROR - analysis_service.py:281 - 获取消息失败: name 'MessageType' is not defined
2025-06-04 23:37:18,555 - app.services.analysis_service - ERROR - analysis_service.py:147 - 分析任务失败: name 'MessageType' is not defined
2025-06-04 23:37:31,488 - app.services.analysis_service - INFO - analysis_service.py:140 - 开始分析群组 58482986673@chatroom，任务ID: ccd9a1a6-ac2c-4d03-99f0-4d797e27134b
2025-06-04 23:37:31,496 - app.services.analysis_service - INFO - analysis_service.py:227 - 正在获取群组 58482986673@chatroom 的消息
2025-06-04 23:37:31,497 - app.services.analysis_service - INFO - analysis_service.py:228 - 时间范围: 2025-05-05 23:37:31 到 2025-06-04 23:37:31
2025-06-04 23:37:31,884 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 5000 条消息
2025-06-04 23:37:31,884 - app.services.analysis_service - INFO - analysis_service.py:239 - 获取到 5000 条消息
2025-06-04 23:37:31,884 - app.services.analysis_service - INFO - analysis_service.py:249 - 消息统计:
2025-06-04 23:37:31,884 - app.services.analysis_service - INFO - analysis_service.py:250 -   - 总数: 5000
2025-06-04 23:37:31,885 - app.services.analysis_service - INFO - analysis_service.py:257 -   - 类型分布: {'text': 4239, 'image': 519, 'system': 242}
2025-06-04 23:37:31,887 - app.services.analysis_service - INFO - analysis_service.py:263 -   - 时间范围: 2025-05-05 08:27:08+08:00 到 2025-05-19 21:19:21+08:00
2025-06-04 23:37:31,887 - app.services.analysis_service - ERROR - analysis_service.py:281 - 获取消息失败: name 'MessageType' is not defined
2025-06-04 23:37:31,887 - app.services.analysis_service - ERROR - analysis_service.py:147 - 分析任务失败: name 'MessageType' is not defined
2025-06-04 23:37:56,056 - app.services.analysis_service - INFO - analysis_service.py:140 - 开始分析群组 58482986673@chatroom，任务ID: f5e497fb-7285-4752-959a-e04518ff0204
2025-06-04 23:37:56,064 - app.services.analysis_service - INFO - analysis_service.py:227 - 正在获取群组 58482986673@chatroom 的消息
2025-06-04 23:37:56,064 - app.services.analysis_service - INFO - analysis_service.py:228 - 时间范围: 2025-05-05 23:37:56 到 2025-06-04 23:37:56
2025-06-04 23:37:56,457 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 5000 条消息
2025-06-04 23:37:56,458 - app.services.analysis_service - INFO - analysis_service.py:239 - 获取到 5000 条消息
2025-06-04 23:37:56,458 - app.services.analysis_service - INFO - analysis_service.py:249 - 消息统计:
2025-06-04 23:37:56,459 - app.services.analysis_service - INFO - analysis_service.py:250 -   - 总数: 5000
2025-06-04 23:37:56,459 - app.services.analysis_service - INFO - analysis_service.py:257 -   - 类型分布: {'text': 4239, 'image': 519, 'system': 242}
2025-06-04 23:37:56,461 - app.services.analysis_service - INFO - analysis_service.py:263 -   - 时间范围: 2025-05-05 08:27:08+08:00 到 2025-05-19 21:19:21+08:00
2025-06-04 23:37:56,461 - app.services.analysis_service - ERROR - analysis_service.py:281 - 获取消息失败: name 'MessageType' is not defined
2025-06-04 23:37:56,462 - app.services.analysis_service - ERROR - analysis_service.py:147 - 分析任务失败: name 'MessageType' is not defined
2025-06-04 23:41:30,751 - app.utils.logger - INFO - logger.py:81 - 日志系统初始化完成，级别: INFO, 调试模式: True
2025-06-04 23:41:30,793 - app.services.deepseek_client - INFO - deepseek_client.py:86 - 使用官方DeepSeek集成初始化成功
2025-06-04 23:41:30,793 - app.services.deepseek_client - INFO - deepseek_client.py:104 - 结构化LLM初始化完成
2025-06-04 23:41:31,066 - app.ui.results_tab - INFO - results_tab.py:35 - 结果查看标签页初始化完成
2025-06-04 23:41:31,170 - app.ui.main_window - INFO - main_window.py:42 - 主窗口初始化完成
2025-06-04 23:41:31,233 - __main__ - INFO - main.py:62 - 应用程序启动成功
2025-06-04 23:41:34,451 - app.services.chatlog_client - INFO - chatlog_client.py:81 - 获取到 64 个群组
2025-06-04 23:41:34,462 - app.ui.groups_tab - INFO - groups_tab.py:155 - 群组列表刷新完成，共 64 个群组
2025-06-04 23:41:37,076 - app.services.chatlog_client - INFO - chatlog_client.py:81 - 获取到 64 个群组
2025-06-04 23:41:42,853 - app.services.analysis_service - INFO - analysis_service.py:140 - 开始分析群组 58482986673@chatroom，任务ID: fef1c222-a66e-4f6f-a0af-317086284e1e
2025-06-04 23:41:42,904 - app.services.analysis_service - INFO - analysis_service.py:227 - 正在获取群组 58482986673@chatroom 的消息
2025-06-04 23:41:42,904 - app.services.analysis_service - INFO - analysis_service.py:228 - 时间范围: 2025-05-28 23:41:42 到 2025-06-04 23:41:42
2025-06-04 23:41:42,955 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 565 条消息
2025-06-04 23:41:42,956 - app.services.analysis_service - INFO - analysis_service.py:239 - 获取到 565 条消息
2025-06-04 23:41:42,956 - app.services.analysis_service - INFO - analysis_service.py:249 - 消息统计:
2025-06-04 23:41:42,956 - app.services.analysis_service - INFO - analysis_service.py:250 -   - 总数: 565
2025-06-04 23:41:42,956 - app.services.analysis_service - INFO - analysis_service.py:257 -   - 类型分布: {'text': 467, 'image': 92, 'system': 6}
2025-06-04 23:41:42,957 - app.services.analysis_service - INFO - analysis_service.py:263 -   - 时间范围: 2025-05-28 07:05:20+08:00 到 2025-06-04 21:22:34+08:00
2025-06-04 23:41:42,957 - app.services.analysis_service - INFO - analysis_service.py:267 -   - 可用于检测的文本消息: 208 条
2025-06-04 23:41:42,972 - app.services.analysis_service - INFO - analysis_service.py:294 - 开始AI检测 565 条消息中的自我介绍
2025-06-04 23:41:44,233 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:41:52,562 - app.services.deepseek_client - INFO - deepseek_client.py:161 - AI分析记录 [ID: ce031d80]
2025-06-04 23:41:52,562 - app.services.deepseek_client - INFO - deepseek_client.py:162 -   类型: detection
2025-06-04 23:41:52,562 - app.services.deepseek_client - INFO - deepseek_client.py:163 -   成功: True
2025-06-04 23:41:52,563 - app.services.deepseek_client - INFO - deepseek_client.py:164 -   处理时间: 9588ms
2025-06-04 23:41:52,563 - app.services.deepseek_client - INFO - deepseek_client.py:166 -   结构化结果: {'is_introduction': False, 'confidence': 0.95, 'reason': '该消息主要讨论了美团公司的财报、战略转型和AI技术发展，并未包含任何个人基本信息、技能、经验或服务介绍，也没有使用第一人称进行自我描述或表达明确的自我介绍意图。', 'keywords': ['美团', '财报', '王兴', 'AI', '大语言模型', '战略转型'], 'introduction_type': ''}
2025-06-04 23:41:52,563 - app.services.deepseek_client - INFO - deepseek_client.py:216 - 自我介绍检测完成 [ID: ce031d80]: False (置信度: 0.95)
2025-06-04 23:41:52,760 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:42:03,259 - app.services.deepseek_client - INFO - deepseek_client.py:161 - AI分析记录 [ID: 752e6a72]
2025-06-04 23:42:03,259 - app.services.deepseek_client - INFO - deepseek_client.py:162 -   类型: detection
2025-06-04 23:42:03,259 - app.services.deepseek_client - INFO - deepseek_client.py:163 -   成功: True
2025-06-04 23:42:03,259 - app.services.deepseek_client - INFO - deepseek_client.py:164 -   处理时间: 10592ms
2025-06-04 23:42:03,260 - app.services.deepseek_client - INFO - deepseek_client.py:166 -   结构化结果: {'is_introduction': False, 'confidence': 0.1, 'reason': '该消息内容为对AI播客功能的评价，未包含任何个人基本信息、技能、经验或专业领域的介绍，也未表达希望得到帮助或寻求机会的意图。', 'keywords': [], 'introduction_type': ''}
2025-06-04 23:42:03,260 - app.services.deepseek_client - INFO - deepseek_client.py:216 - 自我介绍检测完成 [ID: 752e6a72]: False (置信度: 0.1)
2025-06-04 23:42:03,445 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:42:12,082 - app.services.deepseek_client - INFO - deepseek_client.py:161 - AI分析记录 [ID: f51df015]
2025-06-04 23:42:12,082 - app.services.deepseek_client - INFO - deepseek_client.py:162 -   类型: detection
2025-06-04 23:42:12,083 - app.services.deepseek_client - INFO - deepseek_client.py:163 -   成功: True
2025-06-04 23:42:12,083 - app.services.deepseek_client - INFO - deepseek_client.py:164 -   处理时间: 8712ms
2025-06-04 23:42:12,083 - app.services.deepseek_client - INFO - deepseek_client.py:166 -   结构化结果: {'is_introduction': False, 'confidence': 0.0, 'reason': '提供的消息仅为随机字符串，未包含任何个人信息、技能描述、服务说明或明确的自我介绍意图，不符合自我介绍的典型特征。', 'keywords': [], 'introduction_type': ''}
2025-06-04 23:42:12,083 - app.services.deepseek_client - INFO - deepseek_client.py:216 - 自我介绍检测完成 [ID: f51df015]: False (置信度: 0.0)
2025-06-04 23:42:12,264 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:42:19,247 - app.services.deepseek_client - INFO - deepseek_client.py:161 - AI分析记录 [ID: 675a4d84]
2025-06-04 23:42:19,248 - app.services.deepseek_client - INFO - deepseek_client.py:162 -   类型: detection
2025-06-04 23:42:19,248 - app.services.deepseek_client - INFO - deepseek_client.py:163 -   成功: True
2025-06-04 23:42:19,248 - app.services.deepseek_client - INFO - deepseek_client.py:164 -   处理时间: 7053ms
2025-06-04 23:42:19,248 - app.services.deepseek_client - INFO - deepseek_client.py:166 -   结构化结果: {'is_introduction': False, 'confidence': 0.1, 'reason': '提供的消息仅为随机字符串，未包含任何个人信息、技能描述、服务说明或明确的自我介绍意图，不符合自我介绍的典型特征。', 'keywords': [], 'introduction_type': 'mixed'}
2025-06-04 23:42:19,249 - app.services.deepseek_client - INFO - deepseek_client.py:216 - 自我介绍检测完成 [ID: 675a4d84]: False (置信度: 0.1)
2025-06-04 23:42:19,428 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:45:04,012 - app.utils.logger - INFO - logger.py:81 - 日志系统初始化完成，级别: INFO, 调试模式: True
2025-06-04 23:45:04,053 - app.services.deepseek_client - INFO - deepseek_client.py:86 - 使用官方DeepSeek集成初始化成功
2025-06-04 23:45:04,053 - app.services.deepseek_client - INFO - deepseek_client.py:104 - 结构化LLM初始化完成
2025-06-04 23:45:04,342 - app.ui.results_tab - INFO - results_tab.py:35 - 结果查看标签页初始化完成
2025-06-04 23:45:04,446 - app.ui.main_window - INFO - main_window.py:42 - 主窗口初始化完成
2025-06-04 23:45:04,506 - __main__ - INFO - main.py:62 - 应用程序启动成功
2025-06-04 23:45:08,402 - app.services.chatlog_client - INFO - chatlog_client.py:81 - 获取到 64 个群组
2025-06-04 23:45:15,276 - app.services.analysis_service - INFO - analysis_service.py:140 - 开始分析群组 58482986673@chatroom，任务ID: 5df50658-cd85-4335-a174-29828928ef7c
2025-06-04 23:45:15,320 - app.services.analysis_service - INFO - analysis_service.py:227 - 正在获取群组 58482986673@chatroom 的消息
2025-06-04 23:45:15,320 - app.services.analysis_service - INFO - analysis_service.py:228 - 时间范围: 2025-05-28 23:45:15 到 2025-06-04 23:45:15
2025-06-04 23:45:15,373 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 565 条消息
2025-06-04 23:45:15,373 - app.services.analysis_service - INFO - analysis_service.py:239 - 获取到 565 条消息
2025-06-04 23:45:15,373 - app.services.analysis_service - INFO - analysis_service.py:249 - 消息统计:
2025-06-04 23:45:15,374 - app.services.analysis_service - INFO - analysis_service.py:250 -   - 总数: 565
2025-06-04 23:45:15,374 - app.services.analysis_service - INFO - analysis_service.py:257 -   - 类型分布: {'text': 467, 'image': 92, 'system': 6}
2025-06-04 23:45:15,374 - app.services.analysis_service - INFO - analysis_service.py:263 -   - 时间范围: 2025-05-28 07:05:20+08:00 到 2025-06-04 21:22:34+08:00
2025-06-04 23:45:15,374 - app.services.analysis_service - INFO - analysis_service.py:267 -   - 可用于检测的文本消息: 208 条
2025-06-04 23:45:15,407 - app.services.analysis_service - INFO - analysis_service.py:303 - 开始批量AI检测 208 条消息中的自我介绍
2025-06-04 23:45:15,407 - app.services.deepseek_client - INFO - deepseek_client.py:467 - 开始批量自我介绍检测 [ID: 0a1f4607] - 第 1 批，20 条消息
2025-06-04 23:45:16,604 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:45:58,798 - app.services.deepseek_client - INFO - deepseek_client.py:161 - AI分析记录 [ID: 0a1f4607]
2025-06-04 23:45:58,798 - app.services.deepseek_client - INFO - deepseek_client.py:162 -   类型: batch_detection
2025-06-04 23:45:58,798 - app.services.deepseek_client - INFO - deepseek_client.py:163 -   成功: True
2025-06-04 23:45:58,799 - app.services.deepseek_client - INFO - deepseek_client.py:164 -   处理时间: 43389ms
2025-06-04 23:45:58,799 - app.services.deepseek_client - INFO - deepseek_client.py:166 -   结构化结果: {'batch_count': 20, 'results': [{'is_introduction': False, 'confidence': 0.05, 'reason': '普通评论，无自我介绍意图', 'keywords': ['AI', '播客'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.01, 'reason': '随机字符，无意义', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.01, 'reason': '随机字符，无意义', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '分享邀请码，无个人信息', 'keywords': ['lovtrat', '邀请码'], 'introduction_type': 'service', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '分享邀请码，无个人信息', 'keywords': ['ELF2GHE', 'lovart'], 'introduction_type': 'service', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '功能说明，无自我介绍意图', 'keywords': ['用户', '邀请码'], 'introduction_type': 'service', 'success': True}, {'is_introduction': False, 'confidence': 0.15, 'reason': '商业服务预告，无个人特征', 'keywords': ['内测', '商业化', '服务'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.2, 'reason': '项目分享，但未明确个人身份', 'keywords': ['高考', '工具', '扣子'], 'introduction_type': 'mixed', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '简单提问，无自我介绍', 'keywords': ['配音', 'ai'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.01, 'reason': '随机字符，无意义', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '问题反馈，无自我介绍', 'keywords': ['电脑', '死机'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.01, 'reason': '表情符号，无意义', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '分享邀请码，无个人信息', 'keywords': ['fURDAMa', '自取'], 'introduction_type': 'service', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '问题反馈，无自我介绍', 'keywords': ['播放', '视频', '死掉'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '针对性回复，无自我介绍', 'keywords': ['显卡', '弱点'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '普通评价，无自我介绍', 'keywords': ['太牛了'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '技术提问，无自我介绍', 'keywords': ['实现', '微信', '播客'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '内容说明，无自我介绍', 'keywords': ['文章', '单拎'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '功能讨论，无自我介绍', 'keywords': ['公众号', '排版'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.0, 'reason': '解析失败', 'keywords': [], 'introduction_type': 'unknown', 'success': True}]}
2025-06-04 23:45:58,801 - app.services.deepseek_client - INFO - deepseek_client.py:492 - 批量检测完成 [ID: 0a1f4607]: 发现 0 个自我介绍
2025-06-04 23:45:59,304 - app.services.deepseek_client - INFO - deepseek_client.py:467 - 开始批量自我介绍检测 [ID: 8463797e] - 第 2 批，20 条消息
2025-06-04 23:45:59,379 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:46:46,528 - app.services.deepseek_client - INFO - deepseek_client.py:161 - AI分析记录 [ID: 8463797e]
2025-06-04 23:46:46,529 - app.services.deepseek_client - INFO - deepseek_client.py:162 -   类型: batch_detection
2025-06-04 23:46:46,529 - app.services.deepseek_client - INFO - deepseek_client.py:163 -   成功: True
2025-06-04 23:46:46,529 - app.services.deepseek_client - INFO - deepseek_client.py:164 -   处理时间: 47224ms
2025-06-04 23:46:46,529 - app.services.deepseek_client - INFO - deepseek_client.py:166 -   结构化结果: {'batch_count': 20, 'results': [{'is_introduction': False, 'confidence': 0.05, 'reason': '模糊回应他人提问，无自我介绍意图', 'keywords': ['不清楚'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '直接@他人查看内容，无自我描述', 'keywords': ['@Robin'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '分享项目链接，无个人信息或意图', 'keywords': ['Easy DataSet', 'GitHub'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '简单点赞互动，无自我介绍内容', 'keywords': ['@锐哥', '👍'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.3, 'reason': '推广性内容但缺乏个人身份信息', 'keywords': ['试试看', '写内容'], 'introduction_type': 'service', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '行业观点分享，非个人介绍', 'keywords': ['红杉', '智能体经济'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '技术文章标题，无个人元素', 'keywords': ['RAG', '技术演进'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '祝福语，无自我介绍特征', 'keywords': ['生意兴隆', '财源广进'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '祝贺消息，无个人描述', 'keywords': ['AGI Bar', '开业'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '向他人提出建议，非自我介绍', 'keywords': ['@张永航', '学习模式'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '财务计算讨论，无个人意图', 'keywords': ['ROI'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '商业选址提问，无自我介绍', 'keywords': ['楼上铺', '街铺'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.4, 'reason': '推广免费服务但缺乏个人身份', 'keywords': ['GAMMA.APP', '优惠券'], 'introduction_type': 'service', 'success': True}, {'is_introduction': False, 'confidence': 0.2, 'reason': '会员福利通知，无个人描述', 'keywords': ['会员福利', '亲测有效'], 'introduction_type': 'service', 'success': True}, {'is_introduction': False, 'confidence': 0.3, 'reason': '产品推广链接，无自我介绍', 'keywords': ['小仙女 Agent', '文件系统'], 'introduction_type': 'service', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '用户观点表达，非自我介绍', 'keywords': ['AI agent', '编辑器'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '提问他人，无自我描述', 'keywords': ['@Nick', 'AI编辑器'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '技术工具澄清，无个人意图', 'keywords': ['cursor', 'copilot'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '简短技术术语回应，无自我介绍', 'keywords': ['AI Coding'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.0, 'reason': '解析失败', 'keywords': [], 'introduction_type': 'unknown', 'success': True}]}
2025-06-04 23:46:46,530 - app.services.deepseek_client - INFO - deepseek_client.py:492 - 批量检测完成 [ID: 8463797e]: 发现 0 个自我介绍
2025-06-04 23:46:47,032 - app.services.deepseek_client - INFO - deepseek_client.py:467 - 开始批量自我介绍检测 [ID: 1a5e2626] - 第 3 批，20 条消息
2025-06-04 23:46:47,106 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:47:13,900 - app.utils.logger - INFO - logger.py:81 - 日志系统初始化完成，级别: INFO, 调试模式: True
2025-06-04 23:47:13,941 - app.services.deepseek_client - INFO - deepseek_client.py:86 - 使用官方DeepSeek集成初始化成功
2025-06-04 23:47:13,942 - app.services.deepseek_client - INFO - deepseek_client.py:104 - 结构化LLM初始化完成
2025-06-04 23:47:14,211 - app.ui.results_tab - INFO - results_tab.py:35 - 结果查看标签页初始化完成
2025-06-04 23:47:14,314 - app.ui.main_window - INFO - main_window.py:42 - 主窗口初始化完成
2025-06-04 23:47:14,372 - __main__ - INFO - main.py:62 - 应用程序启动成功
2025-06-04 23:47:33,466 - app.services.chatlog_client - INFO - chatlog_client.py:81 - 获取到 64 个群组
2025-06-04 23:47:33,473 - app.ui.groups_tab - INFO - groups_tab.py:155 - 群组列表刷新完成，共 64 个群组
2025-06-04 23:47:44,671 - app.services.chatlog_client - INFO - chatlog_client.py:81 - 获取到 64 个群组
2025-06-04 23:47:48,903 - app.services.analysis_service - INFO - analysis_service.py:140 - 开始分析群组 58482986673@chatroom，任务ID: 6411c180-a311-4a62-9ea8-07714020c737
2025-06-04 23:47:48,946 - app.services.analysis_service - INFO - analysis_service.py:227 - 正在获取群组 58482986673@chatroom 的消息
2025-06-04 23:47:48,946 - app.services.analysis_service - INFO - analysis_service.py:228 - 时间范围: 2025-05-27 23:47:48 到 2025-06-04 23:47:48
2025-06-04 23:47:49,013 - app.services.chatlog_client - INFO - chatlog_client.py:141 - 获取到群组 58482986673@chatroom 的 818 条消息
2025-06-04 23:47:49,014 - app.services.analysis_service - INFO - analysis_service.py:239 - 获取到 818 条消息
2025-06-04 23:47:49,014 - app.services.analysis_service - INFO - analysis_service.py:249 - 消息统计:
2025-06-04 23:47:49,014 - app.services.analysis_service - INFO - analysis_service.py:250 -   - 总数: 818
2025-06-04 23:47:49,014 - app.services.analysis_service - INFO - analysis_service.py:257 -   - 类型分布: {'system': 11, 'text': 679, 'image': 128}
2025-06-04 23:47:49,015 - app.services.analysis_service - INFO - analysis_service.py:263 -   - 时间范围: 2025-05-27 00:04:04+08:00 到 2025-06-04 21:22:34+08:00
2025-06-04 23:47:49,015 - app.services.analysis_service - INFO - analysis_service.py:267 -   - 可用于检测的文本消息: 330 条
2025-06-04 23:47:49,032 - app.services.analysis_service - INFO - analysis_service.py:303 - 开始批量AI检测 330 条消息中的自我介绍
2025-06-04 23:47:49,032 - app.services.analysis_service - INFO - analysis_service.py:321 - 检测参数: 批次大小=30, 并发数=3
2025-06-04 23:47:49,032 - app.services.deepseek_client - INFO - deepseek_client.py:455 - 开始并发批量检测 330 条消息，批次大小: 30，并发数: 3
2025-06-04 23:47:49,033 - app.services.deepseek_client - INFO - deepseek_client.py:463 - 总共分为 11 个批次
2025-06-04 23:47:49,033 - app.services.deepseek_client - INFO - deepseek_client.py:480 - 开始处理批次 1/11 [ID: 98d2ee4d] - 30 条消息
2025-06-04 23:47:50,103 - app.services.deepseek_client - INFO - deepseek_client.py:480 - 开始处理批次 2/11 [ID: 8d2d78b7] - 30 条消息
2025-06-04 23:47:50,104 - app.services.deepseek_client - INFO - deepseek_client.py:480 - 开始处理批次 3/11 [ID: 8e0350d6] - 30 条消息
2025-06-04 23:47:50,315 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:47:50,332 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:47:50,342 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:48:42,770 - app.services.deepseek_client - INFO - deepseek_client.py:161 - AI分析记录 [ID: 98d2ee4d]
2025-06-04 23:48:42,770 - app.services.deepseek_client - INFO - deepseek_client.py:162 -   类型: batch_detection
2025-06-04 23:48:42,770 - app.services.deepseek_client - INFO - deepseek_client.py:163 -   成功: True
2025-06-04 23:48:42,770 - app.services.deepseek_client - INFO - deepseek_client.py:164 -   处理时间: 53736ms
2025-06-04 23:48:42,771 - app.services.deepseek_client - INFO - deepseek_client.py:166 -   结构化结果: {'batch_count': 30, 'results': [{'is_introduction': False, 'confidence': 0.05, 'reason': '调侃性内容，无自我介绍特征', 'keywords': ['印度', 'AI'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.3, 'reason': '提问讨论性质，虽有第一人称但无自我介绍意图', 'keywords': ['疑问', '编程', '产品'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '工具使用吐槽，无自我介绍要素', 'keywords': ['cursor', 'windsurf'], 'introduction_type': 'personal', 'success': True}, {'is_introduction': False, 'confidence': 0.4, 'reason': '观点陈述含经验分享，但非自我介绍', 'keywords': ['AI', 'IDE', '研发团队'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '简单评论，无个人描述', 'keywords': ['图', '探讨'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.2, 'reason': '技术概念阐述，无个人信息', 'keywords': ['编程语言', 'LLM'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '简短对比陈述', 'keywords': ['lovart', 'veo3'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '抽象标语，无具体信息', 'keywords': ['Structure', 'Intelligence'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.15, 'reason': '技术门槛讨论，非自我介绍', 'keywords': ['编程语言基础'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '图表坐标提问', 'keywords': ['纵坐标', '横坐标'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '岗位分类短语', 'keywords': ['专家工程师', '非技术岗位'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.2, 'reason': '业务与技术关系分析', 'keywords': ['技术', '业务'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.25, 'reason': '观点探讨含第一人称但非介绍', 'keywords': ['暴论', 'coding', 'manus'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '普遍性结论陈述', 'keywords': ['解决方案'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.2, 'reason': '技术趋势观点，无个人描述', 'keywords': ['IT系统', 'AI', 'coding'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.15, 'reason': '行业局限性讨论', 'keywords': ['行业专精', 'AI能力'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.15, 'reason': '技术形态提问', 'keywords': ['low-code', '中间态'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '技术风险疑问', 'keywords': ['大模型', '迭代'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '需求实现观点', 'keywords': ['需求', 'coding'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.2, 'reason': '技术解决方案建议', 'keywords': ['AI', 'low-code'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.2, 'reason': '逻辑反驳讨论', 'keywords': ['AI', '效率', '成本'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.15, 'reason': '补充说明性质', 'keywords': ['低代码厂商'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.5, 'reason': '含个人工作方法论但非正式介绍', 'keywords': ['产品导向', '需求', '打磨'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '工具问题吐槽', 'keywords': ['ai studio', '历史'], 'introduction_type': 'personal', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '非正式询问', 'keywords': ['小花', '工作'], 'introduction_type': 'personal', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '简短支持性回复', 'keywords': ['支持', '产品二姐'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '商业布局评论', 'keywords': ['字节', '场景'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.15, 'reason': '调侃性描述他人', 'keywords': ['小花', '创业'], 'introduction_type': 'personal', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '具体事务提及', 'keywords': ['预约', '点餐'], 'introduction_type': 'service', 'success': True}, {'is_introduction': False, 'confidence': 0.0, 'reason': '解析失败', 'keywords': [], 'introduction_type': 'unknown', 'success': True}]}
2025-06-04 23:48:42,774 - app.services.deepseek_client - INFO - deepseek_client.py:509 - 批次 1 完成 [ID: 98d2ee4d]: 发现 0 个自我介绍，耗时 53736ms
2025-06-04 23:48:42,775 - app.services.deepseek_client - INFO - deepseek_client.py:480 - 开始处理批次 4/11 [ID: 70fbac4a] - 30 条消息
2025-06-04 23:48:42,857 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:48:44,035 - app.services.deepseek_client - INFO - deepseek_client.py:161 - AI分析记录 [ID: 8d2d78b7]
2025-06-04 23:48:44,035 - app.services.deepseek_client - INFO - deepseek_client.py:162 -   类型: batch_detection
2025-06-04 23:48:44,036 - app.services.deepseek_client - INFO - deepseek_client.py:163 -   成功: True
2025-06-04 23:48:44,036 - app.services.deepseek_client - INFO - deepseek_client.py:164 -   处理时间: 53932ms
2025-06-04 23:48:44,036 - app.services.deepseek_client - INFO - deepseek_client.py:166 -   结构化结果: {'batch_count': 30, 'results': [{'is_introduction': False, 'confidence': 0.1, 'reason': '提及他人产品，无第一人称描述', 'keywords': ['广州', '产品'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '群聊信息分享，无个人描述', 'keywords': ['隔壁群'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '普通回复，无自我介绍意图', 'keywords': ['公众号'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '引用他人内容，无自我描述', 'keywords': ['文章'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '新闻类信息，无个人相关', 'keywords': ['大模型', '考试'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '鼓励性话语，无自我介绍', 'keywords': ['加油'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '技术分析，无第一人称', 'keywords': ['Agent', '编程'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.15, 'reason': '技术讨论，虽有个人经历但非自我介绍', 'keywords': ['鸿蒙', 'AI编程'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '广告或活动宣传，无个人描述', 'keywords': ['表白', '实况图'], 'introduction_type': 'service', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '产品推广链接，无个人信息', 'keywords': ['天工', '邀请链接'], 'introduction_type': 'service', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '提问互动，无自我介绍', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.2, 'reason': '提及个人行程但非正式介绍', 'keywords': ['出差', '生病'], 'introduction_type': 'personal', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '需求征集，无个人描述', 'keywords': ['需求'], 'introduction_type': 'service', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '评价他人，无自我介绍', 'keywords': ['总结'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '互动夸奖，无自我描述', 'keywords': ['点赞'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '技术提问，无个人信息', 'keywords': ['平台'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '技术回答，无自我介绍', 'keywords': ['社区', 'API'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '需求询问，无个人描述', 'keywords': ['小程序'], 'introduction_type': 'service', 'success': True}, {'is_introduction': False, 'confidence': 0.15, 'reason': '个人需求陈述但非正式介绍', 'keywords': ['小程序'], 'introduction_type': 'personal', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '追问他人需求，无自我介绍', 'keywords': ['小程序'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '工具推荐，无个人描述', 'keywords': ['Cursor'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '使用体验分享，非正式介绍', 'keywords': ['使用'], 'introduction_type': 'personal', 'success': True}, {'is_introduction': False, 'confidence': 0.15, 'reason': '技术建议，含个人体验但非介绍', 'keywords': ['腾讯', 'Cursor'], 'introduction_type': 'mixed', 'success': True}, {'is_introduction': False, 'confidence': 0.2, 'reason': '提及个人体验但未满足其他特征', 'keywords': ['体验'], 'introduction_type': 'personal', 'success': True}, {'is_introduction': False, 'confidence': 0.15, 'reason': '业务说明但非自我介绍', 'keywords': ['小程序开发'], 'introduction_type': 'service', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '技术评论，无个人描述', 'keywords': ['框架'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '观点陈述，无自我介绍', 'keywords': ['大语言模型'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '会议通知，无个人描述', 'keywords': ['开会'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '情绪表达，无自我介绍', 'keywords': ['好难'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.0, 'reason': '解析失败', 'keywords': [], 'introduction_type': 'unknown', 'success': True}]}
2025-06-04 23:48:44,039 - app.services.deepseek_client - INFO - deepseek_client.py:509 - 批次 2 完成 [ID: 8d2d78b7]: 发现 0 个自我介绍，耗时 53932ms
2025-06-04 23:48:44,039 - app.services.deepseek_client - INFO - deepseek_client.py:480 - 开始处理批次 5/11 [ID: 17518ce2] - 30 条消息
2025-06-04 23:48:44,115 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:48:44,155 - app.services.deepseek_client - INFO - deepseek_client.py:161 - AI分析记录 [ID: 8e0350d6]
2025-06-04 23:48:44,155 - app.services.deepseek_client - INFO - deepseek_client.py:162 -   类型: batch_detection
2025-06-04 23:48:44,156 - app.services.deepseek_client - INFO - deepseek_client.py:163 -   成功: True
2025-06-04 23:48:44,156 - app.services.deepseek_client - INFO - deepseek_client.py:164 -   处理时间: 54050ms
2025-06-04 23:48:44,156 - app.services.deepseek_client - INFO - deepseek_client.py:166 -   结构化结果: {'batch_count': 30, 'results': [{'is_introduction': False, 'confidence': 0.1, 'reason': '内容分享，无个人信息或意图', 'keywords': ['读后感'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.2, 'reason': '产品评价，无自我介绍特征', 'keywords': ['国产AI眼镜', '雷鸟'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '行业分析，无个人描述', 'keywords': ['应用场景', '厂商'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '吐槽观点，无自我介绍意图', 'keywords': ['厂商'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.2, 'reason': '行业趋势评论，无个人描述', 'keywords': ['XR', '歌尔', 'Pico'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '抽象观点，无实际内容', 'keywords': ['超人'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '技术批评，无个人描述', 'keywords': ['ar眼镜', '开发水平'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.2, 'reason': '行业动态，无自我介绍', 'keywords': ['rokid', 'cto', '美国'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.2, 'reason': '产品历史回顾，无个人描述', 'keywords': ['颈环', '联想'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '产品功能评价，无自我介绍', 'keywords': ['viture', '手机线'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '历史对标说明，无个人意图', 'keywords': ['epson'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '技术参数讨论，无自我介绍', 'keywords': ['可视角', '55度'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.3, 'reason': '个人使用体验，但非正式介绍', 'keywords': ['xreal', '三星手机', '办公'], 'introduction_type': 'personal', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '产品佩戴感受，无自我介绍', 'keywords': ['联想', '佩戴'], 'introduction_type': 'personal', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '产品吐槽，无个人描述', 'keywords': ['期货', 'OTA'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.2, 'reason': '个人使用调整，非正式介绍', 'keywords': ['鼻梁', '橡皮泥'], 'introduction_type': 'personal', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '产品缺陷评价，无自我介绍', 'keywords': ['xreal', '边缘清晰度'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.2, 'reason': '技术历史回顾，无个人描述', 'keywords': ['蚁视', '光机', '可视角'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '技术参数评价，无自我介绍', 'keywords': ['fov', '边缘'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.2, 'reason': '市场趋势分析，无个人描述', 'keywords': ['主机游戏', '销量'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '重复内容，同消息15', 'keywords': ['联想', '佩戴'], 'introduction_type': 'personal', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '产品列举，无自我介绍', 'keywords': ['Viture', 'Inair', '颈环'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '使用感受描述，无自我介绍', 'keywords': ['走路', '颠簸'], 'introduction_type': 'personal', 'success': True}, {'is_introduction': False, 'confidence': 0.2, 'reason': '产品设计分析，无个人描述', 'keywords': ['颈环', '成本', '收纳'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.2, 'reason': '技术缺陷讨论，无自我介绍', 'keywords': ['调焦', 'magicleap'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '假设性技术需求，无个人描述', 'keywords': ['手机', '线'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '佩戴改进建议，无自我介绍', 'keywords': ['脖子', '衣夹'], 'introduction_type': 'personal', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '技术设想，无个人描述', 'keywords': ['无线充'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.2, 'reason': '技术问题评价，无自我介绍', 'keywords': ['边缘模糊', 'rokid'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.0, 'reason': '解析失败', 'keywords': [], 'introduction_type': 'unknown', 'success': True}]}
2025-06-04 23:48:44,159 - app.services.deepseek_client - INFO - deepseek_client.py:509 - 批次 3 完成 [ID: 8e0350d6]: 发现 0 个自我介绍，耗时 54050ms
2025-06-04 23:48:44,159 - app.services.deepseek_client - INFO - deepseek_client.py:480 - 开始处理批次 6/11 [ID: 4360f8f8] - 30 条消息
2025-06-04 23:48:44,240 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:49:18,543 - app.services.analysis_service - INFO - analysis_service.py:610 - 分析任务已取消: 6411c180-a311-4a62-9ea8-07714020c737
2025-06-04 23:49:21,539 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:49:27,564 - app.services.deepseek_client - INFO - deepseek_client.py:123 - DeepSeek API连接测试成功
2025-06-04 23:49:27,807 - app.services.feishu_client - INFO - feishu_client.py:64 - 飞书访问令牌获取成功
2025-06-04 23:49:36,722 - openai._base_client - INFO - _base_client.py:1579 - Retrying request to /chat/completions in 0.497010 seconds
2025-06-04 23:49:37,308 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:49:38,497 - app.services.deepseek_client - INFO - deepseek_client.py:161 - AI分析记录 [ID: 70fbac4a]
2025-06-04 23:49:38,497 - app.services.deepseek_client - INFO - deepseek_client.py:162 -   类型: batch_detection
2025-06-04 23:49:38,498 - app.services.deepseek_client - INFO - deepseek_client.py:163 -   成功: True
2025-06-04 23:49:38,498 - app.services.deepseek_client - INFO - deepseek_client.py:164 -   处理时间: 55722ms
2025-06-04 23:49:38,498 - app.services.deepseek_client - INFO - deepseek_client.py:166 -   结构化结果: {'batch_count': 30, 'results': [{'is_introduction': False, 'confidence': 0.01, 'reason': '简短陈述，无个人信息', 'keywords': ['steamdeck'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.01, 'reason': '产品评论，无自我介绍意图', 'keywords': ['sony', '光机'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '使用体验描述，非自我介绍', 'keywords': ['走路', '锁骨疼'], 'introduction_type': 'personal', 'success': True}, {'is_introduction': False, 'confidence': 0.01, 'reason': '疑问句，无自我介绍特征', 'keywords': ['苹果', 'Meta'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '行业背景陈述，无个人描述', 'keywords': ['颈环', '尝试'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '厂商观点总结，非个人介绍', 'keywords': ['厂商', 'pass'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '个人使用场景描述，但非正式介绍', 'keywords': ['xreal', '陀螺仪'], 'introduction_type': 'personal', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '产品使用评价，无自我介绍', 'keywords': ['Viture', '游戏'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '技术发展分析，无个人描述', 'keywords': ['VR', 'AR', 'Meta'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '产品缺陷陈述，非自我介绍', 'keywords': ['AR眼镜', 'SLAM'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '技术原理解释，无个人意图', 'keywords': ['AR眼镜', '摄像头', 'IMU'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.2, 'reason': '提及工作经历但非完整介绍', 'keywords': ['同事', '6-7年'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '产品问题讨论，无自我介绍', 'keywords': ['大屏', '视疲劳'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.3, 'reason': '提及职业背景但非正式介绍', 'keywords': ['同事', 'Google', 'HoloLens'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '产品评价，无个人描述', 'keywords': ['hololens 2'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '行业现状描述，非个人介绍', 'keywords': ['产业链', '人体工学'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '历史事件陈述，无个人意图', 'keywords': ['微软', '霉菌'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '技术预测，无自我介绍', 'keywords': ['眼镜', 'WiFi7'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.15, 'reason': '提及职业关注但非介绍', 'keywords': ['关注'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '交互方式评价，无个人描述', 'keywords': ['手环', '指环'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '不确定陈述，无自我介绍', 'keywords': ['手环'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '技术问题讨论，非个人介绍', 'keywords': ['EMG腕带', '体毛'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '行业难题总结，无个人意图', 'keywords': ['XR交互'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '产品体验批评，非自我介绍', 'keywords': ['眼控', 'visionpro'], 'introduction_type': 'personal', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '个人交互偏好，非正式介绍', 'keywords': ['掌心', '虚拟交互'], 'introduction_type': 'personal', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '第三方提及，无个人描述', 'keywords': ['苹果designer'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '产品交互分析，非自我介绍', 'keywords': ['Vision Pro', 'Gaze'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '公司状态评论，无个人意图', 'keywords': ['Humane', 'GPT'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '技术问题对比，非个人介绍', 'keywords': ['Direct touch', 'Hololens'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.0, 'reason': '解析失败', 'keywords': [], 'introduction_type': 'unknown', 'success': True}]}
2025-06-04 23:49:38,500 - app.services.deepseek_client - INFO - deepseek_client.py:509 - 批次 4 完成 [ID: 70fbac4a]: 发现 0 个自我介绍，耗时 55722ms
2025-06-04 23:49:38,500 - app.services.deepseek_client - INFO - deepseek_client.py:480 - 开始处理批次 7/11 [ID: 17d59f6c] - 30 条消息
2025-06-04 23:49:38,578 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:49:41,539 - app.services.deepseek_client - INFO - deepseek_client.py:161 - AI分析记录 [ID: 4360f8f8]
2025-06-04 23:49:41,539 - app.services.deepseek_client - INFO - deepseek_client.py:162 -   类型: batch_detection
2025-06-04 23:49:41,540 - app.services.deepseek_client - INFO - deepseek_client.py:163 -   成功: True
2025-06-04 23:49:41,540 - app.services.deepseek_client - INFO - deepseek_client.py:164 -   处理时间: 57379ms
2025-06-04 23:49:41,540 - app.services.deepseek_client - INFO - deepseek_client.py:166 -   结构化结果: {'batch_count': 30, 'results': [{'is_introduction': False, 'confidence': 0.05, 'reason': '祝贺消息，无个人信息或意图', 'keywords': ['恭贺', '开业'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '提及他人讨论模式，非自我介绍', 'keywords': ['学习', '模式'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '商业术语讨论，无个人描述', 'keywords': ['roi'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '商业选址提问，无自我介绍', 'keywords': ['楼上铺', '街铺'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.15, 'reason': '推广优惠信息，非个人介绍', 'keywords': ['免费', '优惠券', '会员'], 'introduction_type': 'service', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '会员福利通知，无个人内容', 'keywords': ['会员福利', '有效'], 'introduction_type': 'service', 'success': True}, {'is_introduction': False, 'confidence': 0.15, 'reason': '产品推广链接，非自我介绍', 'keywords': ['Agent', 'app', '文件系统'], 'introduction_type': 'service', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '工具使用评论，无个人描述', 'keywords': ['ai agent', '编辑器'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '提问他人，非自我介绍', 'keywords': ['AI编辑器'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '工具名称列举，无个人内容', 'keywords': ['cursor', 'copilot'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '简短术语确认，无自我介绍', 'keywords': ['AI Coding'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '观点陈述，非个人介绍', 'keywords': ['coding', '方案'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '工具名称列举，无其他内容', 'keywords': ['cursor', 'trae'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '提及他人互动，非自我介绍', 'keywords': ['互动', '谢总'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.2, 'reason': '产品创意描述，无个人信息', 'keywords': ['商业计划书', '生成器', 'AI'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.15, 'reason': '功能补充说明，非自我介绍', 'keywords': ['成本结构', 'AI'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.15, 'reason': '创意项目描述，无个人内容', 'keywords': ['AI', 'Vlog'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '对他人评论，非自我介绍', 'keywords': ['合同审核', '自动化'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '提及智能体工具，无个人描述', 'keywords': ['bp', '智能体'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '竞品讨论提问，非自我介绍', 'keywords': ['国外', '竞品'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.15, 'reason': '需求描述，但无个人背景', 'keywords': ['系统梳理', '动态生成'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '技术术语讨论，非自我介绍', 'keywords': ['agent', '工作流'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '行业观点陈述，无个人内容', 'keywords': ['实体行业', '投入'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '技术实施讨论，非自我介绍', 'keywords': ['底层模型', '工程化'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '技术能力评价，无个人描述', 'keywords': ['底层模型', '功能'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '行业场景讨论，非自我介绍', 'keywords': ['toB', '工程化'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '工具好评，无个人内容', 'keywords': ['DeepResearch'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '个人工具使用经历，但非正式介绍', 'keywords': ['Claude', 'Gemini'], 'introduction_type': 'personal', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '账号管理吐槽，非自我介绍', 'keywords': ['电话号码', '黑名单'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.0, 'reason': '解析失败', 'keywords': [], 'introduction_type': 'unknown', 'success': True}]}
2025-06-04 23:49:41,542 - app.services.deepseek_client - INFO - deepseek_client.py:509 - 批次 6 完成 [ID: 4360f8f8]: 发现 0 个自我介绍，耗时 57379ms
2025-06-04 23:49:41,542 - app.services.deepseek_client - INFO - deepseek_client.py:480 - 开始处理批次 8/11 [ID: 2b8ad05d] - 30 条消息
2025-06-04 23:49:41,612 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:50:24,715 - app.services.deepseek_client - INFO - deepseek_client.py:161 - AI分析记录 [ID: 17518ce2]
2025-06-04 23:50:24,716 - app.services.deepseek_client - INFO - deepseek_client.py:162 -   类型: batch_detection
2025-06-04 23:50:24,716 - app.services.deepseek_client - INFO - deepseek_client.py:163 -   成功: True
2025-06-04 23:50:24,716 - app.services.deepseek_client - INFO - deepseek_client.py:164 -   处理时间: 100675ms
2025-06-04 23:50:24,716 - app.services.deepseek_client - INFO - deepseek_client.py:166 -   结构化结果: {'batch_count': 30, 'results': [{'is_introduction': False, 'confidence': 0.1, 'reason': '回复他人消息，无自我介绍意图', 'keywords': ['谢谢', '指引'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '商业分析文章，无个人信息', 'keywords': ['美团', 'AI', '战略'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '评论AI功能，无自我介绍', 'keywords': ['AI', '播客'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '随机字符，无意义', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '随机字符，无意义', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.2, 'reason': '分享邀请码，无个人信息', 'keywords': ['邀请码'], 'introduction_type': 'service', 'success': True}, {'is_introduction': False, 'confidence': 0.2, 'reason': '交换邀请码，无自我介绍', 'keywords': ['邀请码'], 'introduction_type': 'service', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '说明性文字，无个人信息', 'keywords': ['邀请码'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '商业服务说明，无个人信息', 'keywords': ['内测', '商业化'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.3, 'reason': '描述工具开发，但未明确自我介绍', 'keywords': ['工具', '免费', '扣子'], 'introduction_type': 'mixed', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '简短提问，无自我介绍', 'keywords': ['配音', 'AI'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '随机字符，无意义', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '问题反馈，无自我介绍', 'keywords': ['电脑', '死机'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '仅表情符号，无内容', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.2, 'reason': '分享邀请码，无个人信息', 'keywords': ['邀请码'], 'introduction_type': 'service', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '技术问题描述，无自我介绍', 'keywords': ['播放', '视频'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '回复他人，无自我介绍', 'keywords': ['显卡'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '感叹性评论，无自我介绍', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '技术提问，无自我介绍', 'keywords': ['微信', '播客'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '说明性文字，无个人信息', 'keywords': ['文章'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '技术讨论，无自我介绍', 'keywords': ['排版', '组件'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '技术提问，无自我介绍', 'keywords': ['音频', '接口'], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '回复他人，无自我介绍', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '提及他人，无自我介绍', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '分享链接，无个人信息', 'keywords': ['GitHub'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '简短回复，无自我介绍', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.2, 'reason': '推广性文字，但无个人信息', 'keywords': ['试试看', '内容'], 'introduction_type': 'service', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '行业观点分享，无自我介绍', 'keywords': ['AI', '经济'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.1, 'reason': '技术文章标题，无自我介绍', 'keywords': ['RAG', '技术'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.0, 'reason': '解析失败', 'keywords': [], 'introduction_type': 'unknown', 'success': True}]}
2025-06-04 23:50:24,719 - app.services.deepseek_client - INFO - deepseek_client.py:509 - 批次 5 完成 [ID: 17518ce2]: 发现 0 个自我介绍，耗时 100675ms
2025-06-04 23:50:24,719 - app.services.deepseek_client - INFO - deepseek_client.py:480 - 开始处理批次 9/11 [ID: 3c2a96ff] - 30 条消息
2025-06-04 23:50:24,802 - httpx - INFO - _client.py:1740 - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-04 23:50:25,945 - app.services.deepseek_client - INFO - deepseek_client.py:161 - AI分析记录 [ID: 17d59f6c]
2025-06-04 23:50:25,946 - app.services.deepseek_client - INFO - deepseek_client.py:162 -   类型: batch_detection
2025-06-04 23:50:25,946 - app.services.deepseek_client - INFO - deepseek_client.py:163 -   成功: True
2025-06-04 23:50:25,946 - app.services.deepseek_client - INFO - deepseek_client.py:164 -   处理时间: 47445ms
2025-06-04 23:50:25,946 - app.services.deepseek_client - INFO - deepseek_client.py:166 -   结构化结果: {'batch_count': 30, 'results': [{'is_introduction': False, 'confidence': 0.05, 'reason': '普通聊天消息，无自我介绍意图', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '普通聊天消息，无自我介绍意图', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '普通聊天消息，无自我介绍意图', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '普通聊天消息，无自我介绍意图', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '普通聊天消息，无自我介绍意图', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '普通聊天消息，无自我介绍意图', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '普通聊天消息，无自我介绍意图', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '普通聊天消息，无自我介绍意图', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '普通聊天消息，无自我介绍意图', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '普通聊天消息，无自我介绍意图', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': True, 'confidence': 0.85, 'reason': '包含个人活动信息和能提供的服务', 'keywords': ['分享', '盈利', '变现'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '普通聊天消息，无自我介绍意图', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '普通聊天消息，无自我介绍意图', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '普通聊天消息，无自我介绍意图', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '普通聊天消息，无自我介绍意图', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '普通聊天消息，无自我介绍意图', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '普通聊天消息，无自我介绍意图', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '普通聊天消息，无自我介绍意图', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '普通聊天消息，无自我介绍意图', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '普通聊天消息，无自我介绍意图', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '普通聊天消息，无自我介绍意图', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '普通聊天消息，无自我介绍意图', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '普通聊天消息，无自我介绍意图', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '普通聊天消息，无自我介绍意图', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '普通聊天消息，无自我介绍意图', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '普通聊天消息，无自我介绍意图', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': False, 'confidence': 0.05, 'reason': '普通聊天消息，无自我介绍意图', 'keywords': [], 'introduction_type': 'none', 'success': True}, {'is_introduction': True, 'confidence': 0.95, 'reason': '包含个人基本信息和能提供的服务', 'keywords': ['大家好', '总监', '顾问', '合伙人'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': True, 'confidence': 0.8, 'reason': '包含个人活动信息和能提供的服务', 'keywords': ['活动海报', '报名'], 'introduction_type': 'professional', 'success': True}, {'is_introduction': False, 'confidence': 0.0, 'reason': '解析失败', 'keywords': [], 'introduction_type': 'unknown', 'success': True}]}
2025-06-04 23:50:25,948 - app.services.deepseek_client - INFO - deepseek_client.py:509 - 批次 7 完成 [ID: 17d59f6c]: 发现 3 个自我介绍，耗时 47445ms
2025-06-04 23:50:25,949 - app.services.deepseek_client - INFO - deepseek_client.py:480 - 开始处理批次 10/11 [ID: a5fbc776] - 30 条消息
2025-06-04 23:56:17,754 - app.utils.logger - INFO - logger.py:81 - 日志系统初始化完成，级别: INFO, 调试模式: True
2025-06-04 23:56:17,796 - app.services.deepseek_client - INFO - deepseek_client.py:118 - 使用官方DeepSeek集成初始化成功
2025-06-04 23:56:17,796 - app.services.deepseek_client - INFO - deepseek_client.py:144 - 结构化LLM初始化完成 (使用DeepSeek官方集成)
2025-06-04 23:56:18,084 - app.ui.results_tab - INFO - results_tab.py:35 - 结果查看标签页初始化完成
2025-06-04 23:56:18,189 - app.ui.main_window - INFO - main_window.py:42 - 主窗口初始化完成
2025-06-04 23:56:18,264 - __main__ - INFO - main.py:62 - 应用程序启动成功
2025-06-04 23:57:20,246 - app.ui.config_tab - INFO - config_tab.py:437 - 配置保存成功
2025-06-04 23:57:21,487 - __main__ - INFO - main.py:112 - 应用程序正常退出
