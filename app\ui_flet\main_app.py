"""
Flet主应用程序
现代化的用户界面主入口
"""

import flet as ft
import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime

from ..services.analysis_service import AnalysisService
from ..config import get_config, save_config
from .components.sidebar import Sidebar
from .components.header import Header
from .pages.dashboard import DashboardPage
from .pages.groups import GroupsPage
from .pages.analysis import AnalysisPage
from .pages.results import ResultsPage
from .pages.settings import SettingsPage

logger = logging.getLogger(__name__)

class FletMainApp:
    """Flet主应用程序类"""
    
    def __init__(self):
        self.analysis_service = None
        self.current_page = "dashboard"
        self.page_instances = {}
        
        # 初始化服务
        self._init_services()
    
    def _init_services(self):
        """初始化服务"""
        try:
            self.analysis_service = AnalysisService()
            logger.info("分析服务初始化完成")
        except Exception as e:
            logger.error(f"初始化分析服务失败: {e}")
            # 创建模拟服务以避免错误
            self.analysis_service = self._create_mock_service()
    
    def _create_mock_service(self):
        """创建模拟服务"""
        class MockAnalysisService:
            def __init__(self):
                self.config = None
            
            def set_progress_callback(self, callback):
                pass
            
            def get_app_state(self):
                class MockState:
                    def __init__(self):
                        self.is_analyzing = False
                return MockState()
        
        return MockAnalysisService()
    
    async def main(self, page: ft.Page):
        """主应用程序入口"""
        self.page = page
        await self._setup_page()
        await self._build_layout()
        
        # 检查首次启动
        await self._check_first_launch()
    
    async def _setup_page(self):
        """设置页面属性"""
        try:
            config = get_config()
            
            # 基本设置
            self.page.title = "🤖 微信群自我介绍分析工具"
            self.page.window_width = config.ui.window_width
            self.page.window_height = config.ui.window_height
            self.page.window_min_width = config.ui.min_width
            self.page.window_min_height = config.ui.min_height
            
            # 主题设置
            self.page.theme_mode = ft.ThemeMode.LIGHT
            self.page.theme = ft.Theme(
                color_scheme_seed=ft.colors.BLUE,
                visual_density=ft.ThemeVisualDensity.COMFORTABLE
            )
            
            # 字体设置
            self.page.fonts = {
                "Microsoft YaHei": "https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap"
            }
            
            # 页面边距
            self.page.padding = 0
            self.page.spacing = 0
            
        except Exception as e:
            logger.warning(f"设置页面属性失败: {e}")
            # 使用默认设置
            self.page.title = "微信群自我介绍分析工具"
            self.page.window_width = 1200
            self.page.window_height = 800
    
    async def _build_layout(self):
        """构建页面布局"""
        # 创建组件
        self.header = Header(self)
        self.sidebar = Sidebar(self)
        
        # 创建页面实例
        self.page_instances = {
            "dashboard": DashboardPage(self),
            "groups": GroupsPage(self),
            "analysis": AnalysisPage(self),
            "results": ResultsPage(self),
            "settings": SettingsPage(self)
        }
        
        # 主布局
        main_layout = ft.Row(
            controls=[
                # 侧边栏
                self.sidebar.build(),
                
                # 主内容区域
                ft.Container(
                    content=ft.Column(
                        controls=[
                            # 顶部标题栏
                            self.header.build(),
                            
                            # 页面内容区域
                            ft.Container(
                                content=self.page_instances[self.current_page].build(),
                                expand=True,
                                padding=ft.padding.all(20),
                                bgcolor=ft.colors.GREY_50
                            )
                        ],
                        spacing=0,
                        expand=True
                    ),
                    expand=True
                )
            ],
            spacing=0,
            expand=True
        )
        
        # 添加到页面
        self.page.add(main_layout)
        await self.page.update_async()
    
    async def navigate_to(self, page_name: str):
        """导航到指定页面"""
        if page_name in self.page_instances:
            self.current_page = page_name
            
            # 更新侧边栏选中状态
            await self.sidebar.update_selection(page_name)
            
            # 更新主内容区域
            main_content = self.page.controls[0].controls[1].controls[1]
            main_content.content = self.page_instances[page_name].build()
            
            # 更新页面标题
            await self.header.update_title(self._get_page_title(page_name))
            
            await self.page.update_async()
            logger.info(f"导航到页面: {page_name}")
    
    def _get_page_title(self, page_name: str) -> str:
        """获取页面标题"""
        titles = {
            "dashboard": "📊 仪表盘",
            "groups": "📱 群组管理", 
            "analysis": "🚀 分析任务",
            "results": "📋 分析结果",
            "settings": "⚙️ 配置设置"
        }
        return titles.get(page_name, "未知页面")
    
    async def _check_first_launch(self):
        """检查首次启动"""
        try:
            config = get_config()
            
            # 检查关键配置是否为空
            is_first_launch = (
                not config.deepseek.api_key.strip() and
                not config.chatlog.base_url.strip()
            )
            
            if is_first_launch:
                await self._show_welcome_dialog()
                
        except Exception as e:
            logger.warning(f"检查首次启动状态失败: {e}")
    
    async def _show_welcome_dialog(self):
        """显示欢迎对话框"""
        def close_dialog(e):
            welcome_dialog.open = False
            self.page.update()
        
        def start_config(e):
            welcome_dialog.open = False
            self.page.update()
            # 导航到设置页面
            asyncio.create_task(self.navigate_to("settings"))
        
        welcome_dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("🎉 欢迎使用微信群自我介绍分析工具", size=20, weight=ft.FontWeight.BOLD),
            content=ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Text(
                            "这是一个基于AI的智能化用户画像分析软件，可以帮助您：",
                            size=14
                        ),
                        ft.Container(height=10),
                        ft.Column(
                            controls=[
                                ft.Row([ft.Icon(ft.icons.CHECK_CIRCLE, color=ft.colors.GREEN), ft.Text("🤖 智能识别群组中的自我介绍消息")]),
                                ft.Row([ft.Icon(ft.icons.CHECK_CIRCLE, color=ft.colors.GREEN), ft.Text("📊 自动提取用户画像信息")]),
                                ft.Row([ft.Icon(ft.icons.CHECK_CIRCLE, color=ft.colors.GREEN), ft.Text("📋 支持导出分析结果")]),
                                ft.Row([ft.Icon(ft.icons.CHECK_CIRCLE, color=ft.colors.GREEN), ft.Text("📱 可选择同步到飞书多维表格")]),
                            ],
                            spacing=8
                        ),
                        ft.Container(height=15),
                        ft.Container(
                            content=ft.Text(
                                "💡 开始前需要配置AI服务和聊天记录服务，点击下方按钮开始配置！",
                                size=13,
                                color=ft.colors.BLUE_700
                            ),
                            bgcolor=ft.colors.BLUE_50,
                            padding=ft.padding.all(10),
                            border_radius=8
                        )
                    ],
                    spacing=5,
                    tight=True
                ),
                width=500,
                height=300
            ),
            actions=[
                ft.TextButton("稍后配置", on_click=close_dialog),
                ft.ElevatedButton(
                    "🔧 开始配置",
                    on_click=start_config,
                    style=ft.ButtonStyle(
                        bgcolor=ft.colors.BLUE,
                        color=ft.colors.WHITE
                    )
                ),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        self.page.dialog = welcome_dialog
        welcome_dialog.open = True
        await self.page.update_async()
    
    async def show_message(self, title: str, message: str, message_type: str = "info"):
        """显示消息对话框"""
        icon_map = {
            "info": ft.icons.INFO,
            "success": ft.icons.CHECK_CIRCLE,
            "warning": ft.icons.WARNING,
            "error": ft.icons.ERROR
        }
        
        color_map = {
            "info": ft.colors.BLUE,
            "success": ft.colors.GREEN,
            "warning": ft.colors.ORANGE,
            "error": ft.colors.RED
        }
        
        def close_dialog(e):
            dialog.open = False
            self.page.update()
        
        dialog = ft.AlertDialog(
            modal=True,
            title=ft.Row([
                ft.Icon(icon_map.get(message_type, ft.icons.INFO), color=color_map.get(message_type, ft.colors.BLUE)),
                ft.Text(title, weight=ft.FontWeight.BOLD)
            ]),
            content=ft.Text(message),
            actions=[
                ft.TextButton("确定", on_click=close_dialog)
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        self.page.dialog = dialog
        dialog.open = True
        await self.page.update_async()

def run_flet_app():
    """运行Flet应用程序"""
    app = FletMainApp()
    ft.app(target=app.main, view=ft.AppView.FLET_APP)

if __name__ == "__main__":
    run_flet_app()
