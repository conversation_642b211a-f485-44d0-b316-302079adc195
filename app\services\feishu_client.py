"""
飞书多维表格 API 客户端
负责与飞书多维表格API通信，同步用户画像数据
"""

import asyncio
import aiohttp
import requests
import json
from typing import List, Optional, Dict, Any
import logging
from datetime import datetime, timedelta

from ..models import UserProfile, FeishuTableConfig, ProfileField
from ..config import get_config

logger = logging.getLogger(__name__)

class FeishuClient:
    """飞书多维表格API客户端"""
    
    def __init__(self):
        self.config = get_config().feishu
        self.session: Optional[aiohttp.ClientSession] = None
        self.access_token: Optional[str] = None
        self.token_expires_at: Optional[datetime] = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        connector = aiohttp.TCPConnector(limit=10, limit_per_host=5)
        timeout = aiohttp.ClientTimeout(total=self.config.timeout)
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={'Content-Type': 'application/json'}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        if self.session:
            await self.session.close()
    
    async def _get_access_token(self) -> Optional[str]:
        """获取访问令牌"""
        if self.access_token and self.token_expires_at and datetime.now() < self.token_expires_at:
            return self.access_token
        
        try:
            url = f"{self.config.base_url}/open-apis/auth/v3/tenant_access_token/internal"
            data = {
                "app_id": self.config.app_id,
                "app_secret": self.config.app_secret
            }
            
            async with self.session.post(url, json=data) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get('code') == 0:
                        self.access_token = result['tenant_access_token']
                        # 设置过期时间为当前时间+expires_in秒-60秒缓冲
                        expires_in = result.get('expire', 7200) - 60
                        self.token_expires_at = datetime.now() + timedelta(seconds=expires_in)
                        logger.info("飞书访问令牌获取成功")
                        return self.access_token
                    else:
                        logger.error(f"获取飞书访问令牌失败: {result}")
                else:
                    logger.error(f"获取飞书访问令牌失败: HTTP {response.status}")
        except Exception as e:
            logger.error(f"获取飞书访问令牌异常: {e}")
        
        return None
    
    async def test_connection(self) -> bool:
        """测试API连接"""
        try:
            token = await self._get_access_token()
            return token is not None
        except Exception as e:
            logger.error(f"测试飞书连接失败: {e}")
            return False
    
    async def create_table(self, app_token: str, table_name: str) -> Optional[str]:
        """创建多维表格"""
        try:
            token = await self._get_access_token()
            if not token:
                return None
            
            url = f"{self.config.base_url}/open-apis/bitable/v1/apps/{app_token}/tables"
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            # 定义表格字段
            fields = [
                {
                    "field_name": "昵称",
                    "type": 1,  # 多行文本
                    "property": {}
                },
                {
                    "field_name": "职业",
                    "type": 1,  # 多行文本
                    "property": {}
                },
                {
                    "field_name": "个人介绍",
                    "type": 1,  # 多行文本
                    "property": {}
                },
                {
                    "field_name": "能够提供",
                    "type": 1,  # 多行文本
                    "property": {}
                },
                {
                    "field_name": "寻求帮助",
                    "type": 1,  # 多行文本
                    "property": {}
                },
                {
                    "field_name": "行业领域",
                    "type": 1,  # 多行文本
                    "property": {}
                },
                {
                    "field_name": "置信度",
                    "type": 2,  # 数字
                    "property": {}
                },
                {
                    "field_name": "消息ID",
                    "type": 1,  # 多行文本
                    "property": {}
                },
                {
                    "field_name": "群组ID",
                    "type": 1,  # 多行文本
                    "property": {}
                },
                {
                    "field_name": "创建时间",
                    "type": 5,  # 日期时间
                    "property": {}
                }
            ]
            
            data = {
                "table": {
                    "name": table_name,
                    "default_view_name": "网格视图",
                    "fields": fields
                }
            }
            
            async with self.session.post(url, json=data, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get('code') == 0:
                        table_id = result['data']['table_id']
                        logger.info(f"创建表格成功: {table_id}")
                        return table_id
                    else:
                        logger.error(f"创建表格失败: {result}")
                else:
                    logger.error(f"创建表格失败: HTTP {response.status}")
        except Exception as e:
            logger.error(f"创建表格异常: {e}")
        
        return None
    
    async def get_table_fields(self, app_token: str, table_id: str) -> Dict[str, str]:
        """获取表格字段映射"""
        try:
            token = await self._get_access_token()
            if not token:
                return {}
            
            url = f"{self.config.base_url}/open-apis/bitable/v1/apps/{app_token}/tables/{table_id}/fields"
            headers = {"Authorization": f"Bearer {token}"}
            
            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get('code') == 0:
                        field_mapping = {}
                        for field in result['data']['items']:
                            field_name = field['field_name']
                            field_id = field['field_id']
                            
                            # 映射字段名到ProfileField
                            name_mapping = {
                                '昵称': ProfileField.NICKNAME.value,
                                '行业': ProfileField.INDUSTRY.value,
                                '个人介绍': ProfileField.PERSONAL_INTRO.value,
                                '地区': ProfileField.LOCATION.value,
                                '能提供的服务': ProfileField.CAN_PROVIDE.value,
                                '需要的帮助': ProfileField.NEED_HELP.value,
                                '标签': ProfileField.TAGS.value
                            }
                            
                            if field_name in name_mapping:
                                field_mapping[name_mapping[field_name]] = field_id
                        
                        return field_mapping
                    else:
                        logger.error(f"获取表格字段失败: {result}")
                else:
                    logger.error(f"获取表格字段失败: HTTP {response.status}")
        except Exception as e:
            logger.error(f"获取表格字段异常: {e}")
        
        return {}
    
    async def add_record(
        self, 
        app_token: str, 
        table_id: str, 
        profile: UserProfile,
        field_mapping: Dict[str, str]
    ) -> Optional[str]:
        """添加记录到多维表格"""
        try:
            token = await self._get_access_token()
            if not token:
                return None
            
            url = f"{self.config.base_url}/open-apis/bitable/v1/apps/{app_token}/tables/{table_id}/records"
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            # 构建记录数据
            fields = {}
            
            # 映射用户画像字段到飞书字段
            profile_data = {
                ProfileField.NICKNAME.value: profile.nickname,
                ProfileField.INDUSTRY.value: profile.industry,
                ProfileField.PERSONAL_INTRO.value: profile.personal_intro,
                ProfileField.LOCATION.value: profile.location,
                ProfileField.CAN_PROVIDE.value: ", ".join(profile.can_provide) if profile.can_provide else "",
                ProfileField.NEED_HELP.value: ", ".join(profile.need_help) if profile.need_help else "",
                ProfileField.TAGS.value: ", ".join(profile.tags) if profile.tags else ""
            }
            
            for profile_field, value in profile_data.items():
                if value and profile_field in field_mapping:
                    field_id = field_mapping[profile_field]
                    fields[field_id] = {"text": str(value)}
            
            # 添加其他字段 - 使用直接字段名映射
            other_fields = {
                "昵称": profile.nickname or "",
                "微信发送者": profile.sender_name or "",
                "行业": profile.industry or "",
                "个人介绍": profile.personal_intro or "",
                "地区": profile.location or "",
                "能提供的服务": ", ".join(profile.can_provide) if profile.can_provide else "",
                "需要的帮助": ", ".join(profile.need_help) if profile.need_help else "",
                "标签": ", ".join(profile.tags) if profile.tags else "",
                "原始内容": profile.original_content[:500] + "..." if len(profile.original_content) > 500 else profile.original_content,
                "置信度": profile.confidence_score,
                "提取时间": profile.extraction_timestamp.strftime("%Y-%m-%d %H:%M:%S"),
                "AI请求ID": profile.ai_request_id or ""
            }
            
            # 如果没有字段映射，直接使用字段名
            if not field_mapping:
                for field_name, value in other_fields.items():
                    if value:
                        if field_name == "置信度":
                            fields[field_name] = value
                        else:
                            fields[field_name] = {"text": str(value)}
            else:
                # 获取其他字段的ID
                all_fields = await self.get_all_fields(app_token, table_id)
                for field_name, value in other_fields.items():
                    if not value:  # 跳过空值
                        continue
                        
                    field_id = None
                    for field in all_fields:
                        if field['field_name'] == field_name:
                            field_id = field['field_id']
                            break
                    
                    if field_id:
                        if field_name == "置信度":
                            fields[field_id] = value
                        else:
                            fields[field_id] = {"text": str(value)}
            
            data = {
                "records": [
                    {
                        "fields": fields
                    }
                ]
            }
            
            async with self.session.post(url, json=data, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get('code') == 0:
                        record_id = result['data']['records'][0]['record_id']
                        logger.info(f"添加记录成功: {record_id}")
                        return record_id
                    else:
                        logger.error(f"添加记录失败: {result}")
                else:
                    logger.error(f"添加记录失败: HTTP {response.status}")
        except Exception as e:
            logger.error(f"添加记录异常: {e}")
        
        return None
    
    async def get_all_fields(self, app_token: str, table_id: str) -> List[Dict[str, Any]]:
        """获取表格所有字段信息"""
        try:
            token = await self._get_access_token()
            if not token:
                return []
            
            url = f"{self.config.base_url}/open-apis/bitable/v1/apps/{app_token}/tables/{table_id}/fields"
            headers = {"Authorization": f"Bearer {token}"}
            
            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get('code') == 0:
                        return result['data']['items']
                    else:
                        logger.error(f"获取字段信息失败: {result}")
                else:
                    logger.error(f"获取字段信息失败: HTTP {response.status}")
        except Exception as e:
            logger.error(f"获取字段信息异常: {e}")
        
        return []
    
    async def update_record(
        self, 
        app_token: str, 
        table_id: str, 
        record_id: str,
        profile: UserProfile,
        field_mapping: Dict[str, str]
    ) -> bool:
        """更新记录"""
        try:
            token = await self._get_access_token()
            if not token:
                return False
            
            url = f"{self.config.base_url}/open-apis/bitable/v1/apps/{app_token}/tables/{table_id}/records/{record_id}"
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            # 构建更新数据
            fields = {}
            
            profile_data = {
                ProfileField.NICKNAME.value: profile.nickname,
                ProfileField.INDUSTRY.value: profile.industry,
                ProfileField.PERSONAL_INTRO.value: profile.personal_intro,
                ProfileField.LOCATION.value: profile.location,
                ProfileField.CAN_PROVIDE.value: ", ".join(profile.can_provide) if profile.can_provide else "",
                ProfileField.NEED_HELP.value: ", ".join(profile.need_help) if profile.need_help else "",
                ProfileField.TAGS.value: ", ".join(profile.tags) if profile.tags else ""
            }
            
            for profile_field, value in profile_data.items():
                if value and profile_field in field_mapping:
                    field_id = field_mapping[profile_field]
                    fields[field_id] = {"text": str(value)}
            
            data = {"fields": fields}
            
            async with self.session.put(url, json=data, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get('code') == 0:
                        logger.info(f"更新记录成功: {record_id}")
                        return True
                    else:
                        logger.error(f"更新记录失败: {result}")
                else:
                    logger.error(f"更新记录失败: HTTP {response.status}")
        except Exception as e:
            logger.error(f"更新记录异常: {e}")
        
        return False

# 同步版本的客户端
class SyncFeishuClient:
    """同步版本的飞书客户端"""
    
    def __init__(self):
        self.config = get_config().feishu
        self.session = requests.Session()
        self.session.headers.update({'Content-Type': 'application/json'})
        self.access_token: Optional[str] = None
        self.token_expires_at: Optional[datetime] = None
    
    def _get_access_token(self) -> Optional[str]:
        """获取访问令牌"""
        if self.access_token and self.token_expires_at and datetime.now() < self.token_expires_at:
            return self.access_token
        
        try:
            url = f"{self.config.base_url}/open-apis/auth/v3/tenant_access_token/internal"
            data = {
                "app_id": self.config.app_id,
                "app_secret": self.config.app_secret
            }
            
            response = self.session.post(url, json=data, timeout=self.config.timeout)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    self.access_token = result['tenant_access_token']
                    expires_in = result.get('expire', 7200) - 60
                    self.token_expires_at = datetime.now() + timedelta(seconds=expires_in)
                    return self.access_token
                else:
                    logger.error(f"获取飞书访问令牌失败: {result}")
            else:
                logger.error(f"获取飞书访问令牌失败: HTTP {response.status_code}")
        except Exception as e:
            logger.error(f"获取飞书访问令牌异常: {e}")
        
        return None
    
    def test_connection(self) -> bool:
        """测试API连接"""
        try:
            token = self._get_access_token()
            return token is not None
        except Exception as e:
            logger.error(f"测试飞书连接失败: {e}")
            return False 