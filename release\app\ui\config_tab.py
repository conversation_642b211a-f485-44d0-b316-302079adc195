"""
配置设置标签页
用于设置API配置和分析参数
"""

import tkinter as tk
from tkinter import messagebox
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import logging
from typing import Dict, Any

from ..config import get_config, save_config, update_config

logger = logging.getLogger(__name__)

class ConfigTab:
    """配置设置标签页"""
    
    def __init__(self, parent, analysis_service):
        self.parent = parent
        self.analysis_service = analysis_service
        
        # 创建主框架
        self.frame = ttk.Frame(parent)
        
        # 配置变量
        self.config_vars = {}
        
        # 创建界面
        self._create_widgets()
        self._load_config()
        
        logger.debug("配置标签页初始化完成")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 创建滚动容器
        canvas = tk.Canvas(self.frame)
        scrollbar = ttk.Scrollbar(self.frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 布局滚动组件
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 创建配置分组
        self._create_chatlog_config(scrollable_frame)
        self._create_deepseek_config(scrollable_frame)
        self._create_feishu_config(scrollable_frame)
        self._create_analysis_config(scrollable_frame)
        self._create_ui_config(scrollable_frame)
        self._create_buttons(scrollable_frame)
    
    def _create_chatlog_config(self, parent):
        """创建Chatlog配置组"""
        group = ttk.LabelFrame(parent, text="📡 Chatlog API配置", padding=10)
        group.pack(fill=X, padx=10, pady=5)
        
        # API地址
        ttk.Label(group, text="API基础地址:").grid(row=0, column=0, sticky=W, pady=2)
        self.config_vars['chatlog_base_url'] = tk.StringVar()
        ttk.Entry(
            group, 
            textvariable=self.config_vars['chatlog_base_url'],
            width=50
        ).grid(row=0, column=1, sticky=W+E, padx=(10, 0), pady=2)
        
        # 超时时间
        ttk.Label(group, text="请求超时(秒):").grid(row=1, column=0, sticky=W, pady=2)
        self.config_vars['chatlog_timeout'] = tk.IntVar()
        ttk.Spinbox(
            group,
            from_=10,
            to=120,
            textvariable=self.config_vars['chatlog_timeout'],
            width=10
        ).grid(row=1, column=1, sticky=W, padx=(10, 0), pady=2)
        
        # 最大重试次数
        ttk.Label(group, text="最大重试次数:").grid(row=2, column=0, sticky=W, pady=2)
        self.config_vars['chatlog_max_retries'] = tk.IntVar()
        ttk.Spinbox(
            group,
            from_=1,
            to=10,
            textvariable=self.config_vars['chatlog_max_retries'],
            width=10
        ).grid(row=2, column=1, sticky=W, padx=(10, 0), pady=2)
        
        # 配置第2列宽度
        group.columnconfigure(1, weight=1)
    
    def _create_deepseek_config(self, parent):
        """创建DeepSeek配置组"""
        group = ttk.LabelFrame(parent, text="🤖 DeepSeek API配置", padding=10)
        group.pack(fill=X, padx=10, pady=5)
        
        # API密钥
        ttk.Label(group, text="API密钥:").grid(row=0, column=0, sticky=W, pady=2)
        self.config_vars['deepseek_api_key'] = tk.StringVar()
        api_key_entry = ttk.Entry(
            group, 
            textvariable=self.config_vars['deepseek_api_key'],
            show="*",
            width=50
        )
        api_key_entry.grid(row=0, column=1, sticky=W+E, padx=(10, 0), pady=2)
        
        # 显示/隐藏密钥按钮
        def toggle_api_key():
            if api_key_entry.cget("show") == "*":
                api_key_entry.config(show="")
                toggle_btn.config(text="隐藏")
            else:
                api_key_entry.config(show="*")
                toggle_btn.config(text="显示")
        
        toggle_btn = ttk.Button(group, text="显示", command=toggle_api_key, width=8)
        toggle_btn.grid(row=0, column=2, padx=(5, 0), pady=2)
        
        # API基础地址
        ttk.Label(group, text="API基础地址:").grid(row=1, column=0, sticky=W, pady=2)
        self.config_vars['deepseek_base_url'] = tk.StringVar()
        ttk.Entry(
            group, 
            textvariable=self.config_vars['deepseek_base_url'],
            width=50
        ).grid(row=1, column=1, columnspan=2, sticky=W+E, padx=(10, 0), pady=2)
        
        # 模型名称
        ttk.Label(group, text="模型名称:").grid(row=2, column=0, sticky=W, pady=2)
        self.config_vars['deepseek_model'] = tk.StringVar()
        ttk.Combobox(
            group,
            textvariable=self.config_vars['deepseek_model'],
            values=["deepseek-chat", "deepseek-coder"],
            width=20
        ).grid(row=2, column=1, sticky=W, padx=(10, 0), pady=2)
        
        # 最大tokens
        ttk.Label(group, text="最大Tokens:").grid(row=3, column=0, sticky=W, pady=2)
        self.config_vars['deepseek_max_tokens'] = tk.IntVar()
        ttk.Spinbox(
            group,
            from_=1000,
            to=8000,
            increment=500,
            textvariable=self.config_vars['deepseek_max_tokens'],
            width=10
        ).grid(row=3, column=1, sticky=W, padx=(10, 0), pady=2)
        
        # 温度参数
        ttk.Label(group, text="生成温度:").grid(row=4, column=0, sticky=W, pady=2)
        self.config_vars['deepseek_temperature'] = tk.DoubleVar()
        ttk.Scale(
            group,
            from_=0.0,
            to=1.0,
            variable=self.config_vars['deepseek_temperature'],
            orient=HORIZONTAL,
            length=200
        ).grid(row=4, column=1, sticky=W, padx=(10, 0), pady=2)
        
        temp_label = ttk.Label(group, text="0.1")
        temp_label.grid(row=4, column=2, padx=(5, 0), pady=2)
        
        def update_temp_label(*args):
            temp_label.config(text=f"{self.config_vars['deepseek_temperature'].get():.1f}")
        
        self.config_vars['deepseek_temperature'].trace('w', update_temp_label)
        
        group.columnconfigure(1, weight=1)
    
    def _create_feishu_config(self, parent):
        """创建飞书配置组"""
        group = ttk.LabelFrame(parent, text="📋 飞书多维表格配置", padding=10)
        group.pack(fill=X, padx=10, pady=5)
        
        # 应用ID
        ttk.Label(group, text="应用ID:").grid(row=0, column=0, sticky=W, pady=2)
        self.config_vars['feishu_app_id'] = tk.StringVar()
        ttk.Entry(
            group, 
            textvariable=self.config_vars['feishu_app_id'],
            width=50
        ).grid(row=0, column=1, sticky=W+E, padx=(10, 0), pady=2)
        
        # 应用密钥
        ttk.Label(group, text="应用密钥:").grid(row=1, column=0, sticky=W, pady=2)
        self.config_vars['feishu_app_secret'] = tk.StringVar()
        secret_entry = ttk.Entry(
            group, 
            textvariable=self.config_vars['feishu_app_secret'],
            show="*",
            width=50
        )
        secret_entry.grid(row=1, column=1, sticky=W+E, padx=(10, 0), pady=2)
        
        # 显示/隐藏密钥按钮
        def toggle_secret():
            if secret_entry.cget("show") == "*":
                secret_entry.config(show="")
                toggle_secret_btn.config(text="隐藏")
            else:
                secret_entry.config(show="*")
                toggle_secret_btn.config(text="显示")
        
        toggle_secret_btn = ttk.Button(group, text="显示", command=toggle_secret, width=8)
        toggle_secret_btn.grid(row=1, column=2, padx=(5, 0), pady=2)
        
        # API基础地址
        ttk.Label(group, text="API基础地址:").grid(row=2, column=0, sticky=W, pady=2)
        self.config_vars['feishu_base_url'] = tk.StringVar()
        ttk.Entry(
            group, 
            textvariable=self.config_vars['feishu_base_url'],
            width=50
        ).grid(row=2, column=1, columnspan=2, sticky=W+E, padx=(10, 0), pady=2)
        
        group.columnconfigure(1, weight=1)
    
    def _create_analysis_config(self, parent):
        """创建分析配置组"""
        group = ttk.LabelFrame(parent, text="🔧 分析参数配置", padding=10)
        group.pack(fill=X, padx=10, pady=5)
        
        # 批处理大小
        ttk.Label(group, text="批处理大小:").grid(row=0, column=0, sticky=W, pady=2)
        self.config_vars['analysis_batch_size'] = tk.IntVar()
        ttk.Spinbox(
            group,
            from_=1,
            to=50,
            textvariable=self.config_vars['analysis_batch_size'],
            width=10
        ).grid(row=0, column=1, sticky=W, padx=(10, 0), pady=2)
        
        # 最大工作线程数
        ttk.Label(group, text="最大工作线程:").grid(row=1, column=0, sticky=W, pady=2)
        self.config_vars['analysis_max_workers'] = tk.IntVar()
        ttk.Spinbox(
            group,
            from_=1,
            to=20,
            textvariable=self.config_vars['analysis_max_workers'],
            width=10
        ).grid(row=1, column=1, sticky=W, padx=(10, 0), pady=2)
        
        # 置信度阈值
        ttk.Label(group, text="置信度阈值:").grid(row=2, column=0, sticky=W, pady=2)
        self.config_vars['analysis_confidence_threshold'] = tk.DoubleVar()
        ttk.Scale(
            group,
            from_=0.5,
            to=0.95,
            variable=self.config_vars['analysis_confidence_threshold'],
            orient=HORIZONTAL,
            length=200
        ).grid(row=2, column=1, sticky=W, padx=(10, 0), pady=2)
        
        conf_label = ttk.Label(group, text="0.8")
        conf_label.grid(row=2, column=2, padx=(5, 0), pady=2)
        
        def update_conf_label(*args):
            conf_label.config(text=f"{self.config_vars['analysis_confidence_threshold'].get():.2f}")
        
        self.config_vars['analysis_confidence_threshold'].trace('w', update_conf_label)
        
        # 自动检测开关
        self.config_vars['analysis_auto_detect'] = tk.BooleanVar()
        ttk.Checkbutton(
            group,
            text="启用AI自动检测自我介绍",
            variable=self.config_vars['analysis_auto_detect']
        ).grid(row=3, column=0, columnspan=3, sticky=W, pady=5)
        
        group.columnconfigure(1, weight=1)
    
    def _create_ui_config(self, parent):
        """创建界面配置组"""
        group = ttk.LabelFrame(parent, text="🎨 界面配置", padding=10)
        group.pack(fill=X, padx=10, pady=5)
        
        # 主题选择
        ttk.Label(group, text="界面主题:").grid(row=0, column=0, sticky=W, pady=2)
        self.config_vars['ui_theme'] = tk.StringVar()
        theme_combo = ttk.Combobox(
            group,
            textvariable=self.config_vars['ui_theme'],
            values=["cosmo", "flatly", "journal", "litera", "lumen", "minty", "pulse", "sandstone", "united", "yeti"],
            width=15
        )
        theme_combo.grid(row=0, column=1, sticky=W, padx=(10, 0), pady=2)
        
        # 窗口大小
        ttk.Label(group, text="窗口宽度:").grid(row=1, column=0, sticky=W, pady=2)
        self.config_vars['ui_window_width'] = tk.IntVar()
        ttk.Spinbox(
            group,
            from_=800,
            to=1920,
            increment=50,
            textvariable=self.config_vars['ui_window_width'],
            width=10
        ).grid(row=1, column=1, sticky=W, padx=(10, 0), pady=2)
        
        ttk.Label(group, text="窗口高度:").grid(row=2, column=0, sticky=W, pady=2)
        self.config_vars['ui_window_height'] = tk.IntVar()
        ttk.Spinbox(
            group,
            from_=600,
            to=1080,
            increment=50,
            textvariable=self.config_vars['ui_window_height'],
            width=10
        ).grid(row=2, column=1, sticky=W, padx=(10, 0), pady=2)
        
        group.columnconfigure(1, weight=1)
    
    def _create_buttons(self, parent):
        """创建操作按钮"""
        btn_frame = ttk.Frame(parent)
        btn_frame.pack(fill=X, padx=10, pady=20)
        
        # 测试连接按钮
        ttk.Button(
            btn_frame,
            text="🔗 测试API连接",
            command=self._test_connections,
            bootstyle="info"
        ).pack(side=LEFT, padx=(0, 10))
        
        # 保存配置按钮
        ttk.Button(
            btn_frame,
            text="💾 保存配置",
            command=self._save_config,
            bootstyle="success"
        ).pack(side=LEFT, padx=(0, 10))
        
        # 重置配置按钮
        ttk.Button(
            btn_frame,
            text="🔄 重置配置",
            command=self._reset_config,
            bootstyle="warning"
        ).pack(side=LEFT, padx=(0, 10))
        
        # 应用配置按钮
        ttk.Button(
            btn_frame,
            text="✅ 应用配置",
            command=self._apply_config,
            bootstyle="primary"
        ).pack(side=RIGHT)
    
    def _load_config(self):
        """加载配置到界面"""
        try:
            config = get_config()
            
            # Chatlog配置
            self.config_vars['chatlog_base_url'].set(config.chatlog.base_url)
            self.config_vars['chatlog_timeout'].set(config.chatlog.timeout)
            self.config_vars['chatlog_max_retries'].set(config.chatlog.max_retries)
            
            # DeepSeek配置
            self.config_vars['deepseek_api_key'].set(config.deepseek.api_key)
            self.config_vars['deepseek_base_url'].set(config.deepseek.base_url)
            self.config_vars['deepseek_model'].set(config.deepseek.model)
            self.config_vars['deepseek_max_tokens'].set(config.deepseek.max_tokens)
            self.config_vars['deepseek_temperature'].set(config.deepseek.temperature)
            
            # Feishu配置
            self.config_vars['feishu_app_id'].set(config.feishu.app_id)
            self.config_vars['feishu_app_secret'].set(config.feishu.app_secret)
            self.config_vars['feishu_base_url'].set(config.feishu.base_url)
            
            # 分析配置
            self.config_vars['analysis_batch_size'].set(config.analysis.batch_size)
            self.config_vars['analysis_max_workers'].set(config.analysis.max_workers)
            self.config_vars['analysis_confidence_threshold'].set(config.analysis.confidence_threshold)
            self.config_vars['analysis_auto_detect'].set(config.analysis.auto_detect)
            
            # UI配置
            self.config_vars['ui_theme'].set(config.ui.theme)
            self.config_vars['ui_window_width'].set(config.ui.window_width)
            self.config_vars['ui_window_height'].set(config.ui.window_height)
            
            logger.debug("配置加载完成")
            
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            messagebox.showerror("错误", f"加载配置失败:\n{str(e)}")
    
    def _save_config(self):
        """保存配置"""
        try:
            config = get_config()
            
            # 更新配置
            config.chatlog.base_url = self.config_vars['chatlog_base_url'].get()
            config.chatlog.timeout = self.config_vars['chatlog_timeout'].get()
            config.chatlog.max_retries = self.config_vars['chatlog_max_retries'].get()
            
            config.deepseek.api_key = self.config_vars['deepseek_api_key'].get()
            config.deepseek.base_url = self.config_vars['deepseek_base_url'].get()
            config.deepseek.model = self.config_vars['deepseek_model'].get()
            config.deepseek.max_tokens = self.config_vars['deepseek_max_tokens'].get()
            config.deepseek.temperature = self.config_vars['deepseek_temperature'].get()
            
            config.feishu.app_id = self.config_vars['feishu_app_id'].get()
            config.feishu.app_secret = self.config_vars['feishu_app_secret'].get()
            config.feishu.base_url = self.config_vars['feishu_base_url'].get()
            
            config.analysis.batch_size = self.config_vars['analysis_batch_size'].get()
            config.analysis.max_workers = self.config_vars['analysis_max_workers'].get()
            config.analysis.confidence_threshold = self.config_vars['analysis_confidence_threshold'].get()
            config.analysis.auto_detect = self.config_vars['analysis_auto_detect'].get()
            
            config.ui.theme = self.config_vars['ui_theme'].get()
            config.ui.window_width = self.config_vars['ui_window_width'].get()
            config.ui.window_height = self.config_vars['ui_window_height'].get()
            
            # 保存配置
            if save_config(config):
                messagebox.showinfo("成功", "配置保存成功！")
                logger.info("配置保存成功")
            else:
                messagebox.showerror("错误", "配置保存失败")
                
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            messagebox.showerror("错误", f"保存配置失败:\n{str(e)}")
    
    def _reset_config(self):
        """重置配置为默认值"""
        try:
            result = messagebox.askyesno("确认重置", "确定要重置所有配置为默认值吗？")
            if result:
                # 重新加载默认配置
                self._load_config()
                messagebox.showinfo("成功", "配置已重置为默认值")
                logger.info("配置重置完成")
        except Exception as e:
            logger.error(f"重置配置失败: {e}")
            messagebox.showerror("错误", f"重置配置失败:\n{str(e)}")
    
    def _apply_config(self):
        """应用配置（无需重启）"""
        try:
            # 先保存配置
            self._save_config()
            
            # 更新分析服务的配置
            self.analysis_service.config = get_config()
            
            messagebox.showinfo("成功", "配置已应用")
            logger.info("配置应用成功")
            
        except Exception as e:
            logger.error(f"应用配置失败: {e}")
            messagebox.showerror("错误", f"应用配置失败:\n{str(e)}")
    
    def _test_connections(self):
        """测试API连接"""
        try:
            # 先应用当前配置
            self._apply_config()
            
            # 显示测试提示
            progress_window = tk.Toplevel(self.frame)
            progress_window.title("测试连接")
            progress_window.geometry("300x150")
            progress_window.transient(self.frame)
            progress_window.grab_set()
            
            ttk.Label(progress_window, text="正在测试API连接...").pack(pady=20)
            progress_bar = ttk.Progressbar(progress_window, mode='indeterminate')
            progress_bar.pack(pady=10, padx=20, fill=X)
            progress_bar.start()
            
            # 异步测试连接
            def test_async():
                try:
                    # 这里需要在主窗口的异步循环中运行
                    if hasattr(self.analysis_service, 'test_api_connections'):
                        # 由于是同步调用，这里简化处理
                        progress_window.destroy()
                        messagebox.showinfo("测试完成", "请查看状态栏中的连接状态")
                except Exception as e:
                    progress_window.destroy()
                    messagebox.showerror("测试失败", f"连接测试失败:\n{str(e)}")
            
            # 延迟执行测试
            progress_window.after(1000, test_async)
            
        except Exception as e:
            logger.error(f"测试连接失败: {e}")
            messagebox.showerror("错误", f"测试连接失败:\n{str(e)}") 