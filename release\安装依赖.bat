@echo off
chcp 65001 >nul
echo 微信群用户档案分析器 - 依赖安装脚本
echo.

REM 检查uv是否安装
uv --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到uv包管理器
    echo 请先安装uv: pip install uv
    echo 或访问: https://docs.astral.sh/uv/
    pause
    exit /b 1
)

echo 正在同步项目依赖...
uv sync

if %errorlevel% equ 0 (
    echo.
    echo ✅ 依赖安装完成！
    echo 现在可以运行: uv run python app/main.py
) else (
    echo.
    echo ❌ 依赖安装失败，请检查错误信息
)

pause
