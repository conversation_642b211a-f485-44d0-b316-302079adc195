{"mode": "development", "debug": true, "log_level": "INFO", "chatlog": {"base_url": "http://127.0.0.1:5030", "timeout": 30, "max_retries": 3}, "deepseek": {"api_key": "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", "base_url": "https://openrouter.ai/api/v1", "model": "google/gemini-2.5-flash-preview-05-20", "max_tokens": 100000, "temperature": 0.33714285714285713, "timeout": 60, "use_openai_like": true}, "feishu": {"app_id": "cli_a8caf0f6c423d00b", "app_secret": "5o8LOn4gA0PpRUVGePHgGfE8PLJmSJOk", "app_token": "", "table_id": "", "base_url": "https://open.feishu.cn", "timeout": 30}, "ui": {"theme": "lumen", "window_width": 1500, "window_height": 1080, "min_width": 1000, "min_height": 600}, "analysis": {"batch_size": 10, "max_workers": 5, "sync_interval": 300, "auto_detect": true, "confidence_threshold": 0.8}}