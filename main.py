#!/usr/bin/env python3
"""
微信群用户档案自动分析器
主程序入口文件

使用DeepSeek AI和LlamaIndex技术栈，自动检测和提取微信群中的用户自我介绍信息
"""

import sys
import os
import logging
import asyncio
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ.setdefault('PYTHONPATH', str(project_root))

def setup_logging():
    """配置日志系统"""
    # 创建logs目录
    logs_dir = project_root / "logs"
    logs_dir.mkdir(exist_ok=True)
    
    # 配置日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 配置根日志器
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.FileHandler(logs_dir / "app.log", encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # 设置第三方库日志级别
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('aiohttp').setLevel(logging.WARNING)
    logging.getLogger('llama_index').setLevel(logging.INFO)

def check_environment():
    """检查运行环境"""
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        sys.exit(1)
    
    # 检查必要的环境变量
    required_env_vars = [
        'DEEPSEEK_API_KEY'
    ]
    
    missing_vars = []
    for var in required_env_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("警告: 以下环境变量未设置，部分功能可能无法使用:")
        for var in missing_vars:
            print(f"  - {var}")
        print("\n请在.env文件中配置这些变量，或通过环境变量设置")
        print("示例: export DEEPSEEK_API_KEY=your_api_key")

def main():
    """主函数"""
    print("🚀 启动微信群用户档案自动分析器...")
    
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)
    
    # 检查环境
    check_environment()
    
    try:
        # 导入应用模块
        from app.ui.main_window import MainWindow
        from app.services.analysis_service import AnalysisService
        
        logger.info("正在初始化应用组件...")
        
        # 创建分析服务
        analysis_service = AnalysisService()
        
        # 设置Windows事件循环策略
        if sys.platform.startswith('win'):
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        
        # 创建并启动主窗口
        logger.info("正在启动图形界面...")
        app = MainWindow(analysis_service)
        
        logger.info("✅ 应用启动成功")
        print("✅ 应用已启动，请在图形界面中操作")
        
        # 启动GUI主循环
        app.run()
        
    except ImportError as e:
        error_msg = f"导入错误: {e}"
        logger.error(error_msg)
        print(f"❌ {error_msg}")
        print("请确保已安装所有必要的依赖包:")
        print("pip install -r requirements.txt")
        sys.exit(1)
        
    except Exception as e:
        error_msg = f"启动失败: {e}"
        logger.error(error_msg, exc_info=True)
        print(f"❌ {error_msg}")
        print("请查看logs/app.log获取详细错误信息")
        sys.exit(1)
    
    logger.info("👋 应用已退出")

if __name__ == "__main__":
    main()
