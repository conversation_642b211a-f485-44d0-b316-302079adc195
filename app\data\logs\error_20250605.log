2025-06-05 00:08:24,236 - app.services.deepseek_client - ERROR - deepseek_client.py:208 -   错误: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:08:24,236 - app.services.deepseek_client - ERROR - deepseek_client.py:581 - 批次 2 失败 [ID: b6785065]: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:08:24,245 - app.services.deepseek_client - ERROR - deepseek_client.py:208 -   错误: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:08:24,245 - app.services.deepseek_client - ERROR - deepseek_client.py:581 - 批次 3 失败 [ID: fe281f30]: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:08:24,251 - app.services.deepseek_client - ERROR - deepseek_client.py:208 -   错误: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:08:24,251 - app.services.deepseek_client - ERROR - deepseek_client.py:581 - 批次 1 失败 [ID: 3157442f]: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:08:24,303 - app.services.deepseek_client - ERROR - deepseek_client.py:208 -   错误: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:08:24,303 - app.services.deepseek_client - ERROR - deepseek_client.py:581 - 批次 5 失败 [ID: 5e1b1219]: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:08:24,313 - app.services.deepseek_client - ERROR - deepseek_client.py:208 -   错误: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:08:24,313 - app.services.deepseek_client - ERROR - deepseek_client.py:581 - 批次 6 失败 [ID: daedf7e9]: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:08:24,343 - app.services.deepseek_client - ERROR - deepseek_client.py:208 -   错误: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:08:24,343 - app.services.deepseek_client - ERROR - deepseek_client.py:581 - 批次 4 失败 [ID: 849e0c05]: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:08:24,359 - app.services.deepseek_client - ERROR - deepseek_client.py:208 -   错误: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:08:24,359 - app.services.deepseek_client - ERROR - deepseek_client.py:581 - 批次 7 失败 [ID: 4d933651]: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:09:02,122 - app.services.deepseek_client - ERROR - deepseek_client.py:169 - 测试DeepSeek连接失败: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:09:58,736 - app.services.deepseek_client - ERROR - deepseek_client.py:208 -   错误: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:09:58,736 - app.services.deepseek_client - ERROR - deepseek_client.py:581 - 批次 2 失败 [ID: fc3cb6e1]: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:09:58,749 - app.services.deepseek_client - ERROR - deepseek_client.py:208 -   错误: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:09:58,749 - app.services.deepseek_client - ERROR - deepseek_client.py:581 - 批次 1 失败 [ID: 7d7f821f]: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:09:58,757 - app.services.deepseek_client - ERROR - deepseek_client.py:208 -   错误: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:09:58,758 - app.services.deepseek_client - ERROR - deepseek_client.py:581 - 批次 3 失败 [ID: 813ef0e8]: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:09:58,790 - app.services.deepseek_client - ERROR - deepseek_client.py:208 -   错误: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:09:58,791 - app.services.deepseek_client - ERROR - deepseek_client.py:581 - 批次 4 失败 [ID: fc5dddf4]: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:09:58,807 - app.services.deepseek_client - ERROR - deepseek_client.py:208 -   错误: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:09:58,807 - app.services.deepseek_client - ERROR - deepseek_client.py:581 - 批次 5 失败 [ID: 2aa5f75d]: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:09:58,820 - app.services.deepseek_client - ERROR - deepseek_client.py:208 -   错误: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:09:58,821 - app.services.deepseek_client - ERROR - deepseek_client.py:581 - 批次 6 失败 [ID: 3627010e]: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:09:58,845 - app.services.deepseek_client - ERROR - deepseek_client.py:208 -   错误: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:09:58,845 - app.services.deepseek_client - ERROR - deepseek_client.py:581 - 批次 7 失败 [ID: 3ac86b7a]: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:11:17,310 - app.services.deepseek_client - ERROR - deepseek_client.py:208 -   错误: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:11:17,310 - app.services.deepseek_client - ERROR - deepseek_client.py:581 - 批次 2 失败 [ID: de3f4530]: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:11:17,369 - app.services.deepseek_client - ERROR - deepseek_client.py:208 -   错误: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:11:17,369 - app.services.deepseek_client - ERROR - deepseek_client.py:581 - 批次 4 失败 [ID: 0e10876f]: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:11:17,377 - app.services.deepseek_client - ERROR - deepseek_client.py:208 -   错误: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:11:17,378 - app.services.deepseek_client - ERROR - deepseek_client.py:581 - 批次 3 失败 [ID: 8645a5a8]: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:11:17,425 - app.services.deepseek_client - ERROR - deepseek_client.py:208 -   错误: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:11:17,425 - app.services.deepseek_client - ERROR - deepseek_client.py:581 - 批次 5 失败 [ID: 318f10be]: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:11:17,438 - app.services.deepseek_client - ERROR - deepseek_client.py:208 -   错误: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:11:17,438 - app.services.deepseek_client - ERROR - deepseek_client.py:581 - 批次 6 失败 [ID: de8fc0ec]: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:11:17,481 - app.services.deepseek_client - ERROR - deepseek_client.py:208 -   错误: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:11:17,481 - app.services.deepseek_client - ERROR - deepseek_client.py:581 - 批次 7 失败 [ID: 74cabecc]: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:11:17,669 - app.services.deepseek_client - ERROR - deepseek_client.py:208 -   错误: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:11:17,670 - app.services.deepseek_client - ERROR - deepseek_client.py:581 - 批次 1 失败 [ID: 0e786c39]: Error code: 401 - {'error': {'message': 'Authentication Fails, Your api key: ****2bd3 is invalid', 'type': 'authentication_error', 'param': None, 'code': 'invalid_request_error'}}
2025-06-05 00:12:26,099 - app.services.analysis_service - ERROR - analysis_service.py:387 - 处理检测结果失败 [消息48]: 'Message' object has no attribute 'sender_id'
2025-06-05 00:12:26,099 - app.services.analysis_service - ERROR - analysis_service.py:387 - 处理检测结果失败 [消息65]: 'Message' object has no attribute 'sender_id'
2025-06-05 00:17:24,256 - app.services.analysis_service - ERROR - analysis_service.py:387 - 处理检测结果失败 [消息47]: type object 'IntroductionStatus' has no attribute 'DETECTED'
2025-06-05 00:17:24,257 - app.services.analysis_service - ERROR - analysis_service.py:387 - 处理检测结果失败 [消息64]: type object 'IntroductionStatus' has no attribute 'DETECTED'
2025-06-05 00:18:22,004 - app.services.analysis_service - ERROR - analysis_service.py:387 - 处理检测结果失败 [消息66]: type object 'IntroductionStatus' has no attribute 'DETECTED'
2025-06-05 00:18:22,004 - app.services.analysis_service - ERROR - analysis_service.py:387 - 处理检测结果失败 [消息138]: type object 'IntroductionStatus' has no attribute 'DETECTED'
2025-06-05 00:18:22,005 - app.services.analysis_service - ERROR - analysis_service.py:387 - 处理检测结果失败 [消息191]: type object 'IntroductionStatus' has no attribute 'DETECTED'
2025-06-05 00:18:22,006 - app.services.analysis_service - ERROR - analysis_service.py:387 - 处理检测结果失败 [消息327]: type object 'IntroductionStatus' has no attribute 'DETECTED'
2025-06-05 00:18:22,007 - app.services.analysis_service - ERROR - analysis_service.py:387 - 处理检测结果失败 [消息344]: type object 'IntroductionStatus' has no attribute 'DETECTED'
2025-06-05 00:18:22,007 - app.services.analysis_service - ERROR - analysis_service.py:387 - 处理检测结果失败 [消息345]: type object 'IntroductionStatus' has no attribute 'DETECTED'
2025-06-05 00:23:27,841 - app.services.analysis_service - ERROR - analysis_service.py:157 - 分析任务失败: 'FeishuConfig' object has no attribute 'app_token'
2025-06-05 00:28:20,246 - app.ui.results_tab - ERROR - results_tab.py:278 - 刷新结果数据失败: 'UserProfile' object has no attribute 'status'
2025-06-05 00:28:35,274 - app.ui.results_tab - ERROR - results_tab.py:278 - 刷新结果数据失败: 'UserProfile' object has no attribute 'status'
2025-06-05 00:43:26,985 - __main__ - ERROR - main.py:68 - 应用程序启动失败: unknown option "-placeholder_text"
2025-06-05 21:55:31,343 - app.services.deepseek_client - ERROR - deepseek_client.py:207 -   错误: Expected at least one tool call, but got 0 tool calls.
2025-06-05 21:55:31,343 - app.services.deepseek_client - ERROR - deepseek_client.py:283 - 检测自我介绍失败 [ID: 6afd4384]: Expected at least one tool call, but got 0 tool calls.
2025-06-05 21:55:48,810 - app.services.deepseek_client - ERROR - deepseek_client.py:207 -   错误: Expected at least one tool call, but got 0 tool calls.
2025-06-05 21:55:48,811 - app.services.deepseek_client - ERROR - deepseek_client.py:283 - 检测自我介绍失败 [ID: 831385f0]: Expected at least one tool call, but got 0 tool calls.
2025-06-05 21:56:14,807 - app.services.deepseek_client - ERROR - deepseek_client.py:207 -   错误: Expected at least one tool call, but got 0 tool calls.
2025-06-05 21:56:14,807 - app.services.deepseek_client - ERROR - deepseek_client.py:283 - 检测自我介绍失败 [ID: 46507e46]: Expected at least one tool call, but got 0 tool calls.
2025-06-05 21:56:24,697 - app.services.deepseek_client - ERROR - deepseek_client.py:207 -   错误: Expected at least one tool call, but got 0 tool calls.
2025-06-05 21:56:24,697 - app.services.deepseek_client - ERROR - deepseek_client.py:283 - 检测自我介绍失败 [ID: aa503d10]: Expected at least one tool call, but got 0 tool calls.
2025-06-05 22:02:08,769 - app.services.deepseek_client - ERROR - deepseek_client.py:219 -   错误: Error code: 400 - {'error': {'message': 'This endpoint\'s maximum context length is 163840 tokens. However, you requested about 174752 tokens (74661 of text input, 91 of tool input, 100000 in the output). Please reduce the length of either one, or use the "middle-out" transform to compress your prompt automatically.', 'code': 400, 'metadata': {'provider_name': None}}}
2025-06-05 22:02:08,770 - app.services.deepseek_client - ERROR - deepseek_client.py:668 - 批次 1 失败 [ID: 5d79401a]: Error code: 400 - {'error': {'message': 'This endpoint\'s maximum context length is 163840 tokens. However, you requested about 174752 tokens (74661 of text input, 91 of tool input, 100000 in the output). Please reduce the length of either one, or use the "middle-out" transform to compress your prompt automatically.', 'code': 400, 'metadata': {'provider_name': None}}}
2025-06-05 22:02:39,114 - app.services.deepseek_client - ERROR - deepseek_client.py:219 -   错误: Expected at least one tool call, but got 0 tool calls.
2025-06-05 22:02:39,114 - app.services.deepseek_client - ERROR - deepseek_client.py:668 - 批次 2 失败 [ID: 0d8295c6]: Expected at least one tool call, but got 0 tool calls.
2025-06-05 22:03:48,129 - app.services.deepseek_client - ERROR - deepseek_client.py:219 -   错误: 'str' object has no attribute 'model_dump_json'
2025-06-05 22:03:48,129 - app.services.deepseek_client - ERROR - deepseek_client.py:668 - 批次 1 失败 [ID: 680d352c]: 'str' object has no attribute 'model_dump_json'
2025-06-05 22:09:18,197 - app.services.deepseek_client - ERROR - deepseek_client.py:219 -   错误: 'NoneType' object is not subscriptable
2025-06-05 22:09:18,197 - app.services.deepseek_client - ERROR - deepseek_client.py:717 - 批次 2 失败 [ID: 98be9ae6]: 'NoneType' object is not subscriptable
2025-06-05 22:23:17,874 - app.services.deepseek_client - ERROR - deepseek_client.py:724 - AI批量处理失败，回退到单条处理: 'str' object has no attribute 'model_dump_json'
2025-06-05 22:23:56,517 - app.services.deepseek_client - ERROR - deepseek_client.py:724 - AI批量处理失败，回退到单条处理: 'str' object has no attribute 'model_dump_json'
2025-06-05 22:24:41,568 - app.services.deepseek_client - ERROR - deepseek_client.py:724 - AI批量处理失败，回退到单条处理: 'str' object has no attribute 'model_dump_json'
2025-06-05 22:27:18,141 - app.services.deepseek_client - ERROR - deepseek_client.py:777 - AI批量处理失败，回退到单条处理: 'str' object has no attribute 'model_dump_json'
2025-06-05 22:27:58,400 - app.services.deepseek_client - ERROR - deepseek_client.py:777 - AI批量处理失败，回退到单条处理: 'str' object has no attribute 'model_dump_json'
2025-06-05 22:28:45,653 - app.services.deepseek_client - ERROR - deepseek_client.py:777 - AI批量处理失败，回退到单条处理: 'str' object has no attribute 'model_dump_json'
2025-06-05 22:29:30,756 - app.services.deepseek_client - ERROR - deepseek_client.py:777 - AI批量处理失败，回退到单条处理: 'str' object has no attribute 'model_dump_json'
2025-06-05 22:30:31,281 - app.services.deepseek_client - ERROR - deepseek_client.py:777 - AI批量处理失败，回退到单条处理: 'str' object has no attribute 'model_dump_json'
2025-06-05 22:34:06,441 - app.services.deepseek_client - ERROR - deepseek_client.py:777 - AI批量处理失败，回退到单条处理: 'str' object has no attribute 'model_dump_json'
2025-06-05 22:43:47,704 - app.services.deepseek_client - ERROR - deepseek_client.py:1169 - AI批量提取失败，回退到单条处理: 'str' object has no attribute 'model_dump_json'
2025-06-05 22:44:32,197 - app.services.deepseek_client - ERROR - deepseek_client.py:1169 - AI批量提取失败，回退到单条处理: 'str' object has no attribute 'model_dump_json'
2025-06-05 22:45:10,790 - app.services.deepseek_client - ERROR - deepseek_client.py:1169 - AI批量提取失败，回退到单条处理: 'str' object has no attribute 'model_dump_json'
2025-06-05 22:45:51,267 - app.services.deepseek_client - ERROR - deepseek_client.py:1169 - AI批量提取失败，回退到单条处理: 'str' object has no attribute 'model_dump_json'
2025-06-05 22:46:37,545 - app.services.deepseek_client - ERROR - deepseek_client.py:1169 - AI批量提取失败，回退到单条处理: 'str' object has no attribute 'model_dump_json'
2025-06-05 22:47:19,231 - app.services.deepseek_client - ERROR - deepseek_client.py:1169 - AI批量提取失败，回退到单条处理: 'str' object has no attribute 'model_dump_json'
2025-06-05 22:48:00,319 - app.services.deepseek_client - ERROR - deepseek_client.py:1169 - AI批量提取失败，回退到单条处理: 'str' object has no attribute 'model_dump_json'
2025-06-05 22:48:41,724 - app.services.deepseek_client - ERROR - deepseek_client.py:1169 - AI批量提取失败，回退到单条处理: 'str' object has no attribute 'model_dump_json'
2025-06-05 22:49:23,299 - app.services.deepseek_client - ERROR - deepseek_client.py:1169 - AI批量提取失败，回退到单条处理: 'str' object has no attribute 'model_dump_json'
2025-06-05 22:50:02,841 - app.services.deepseek_client - ERROR - deepseek_client.py:1169 - AI批量提取失败，回退到单条处理: 'str' object has no attribute 'model_dump_json'
2025-06-05 22:50:50,174 - app.services.deepseek_client - ERROR - deepseek_client.py:1169 - AI批量提取失败，回退到单条处理: 'str' object has no attribute 'model_dump_json'
2025-06-05 22:51:35,183 - app.services.deepseek_client - ERROR - deepseek_client.py:1169 - AI批量提取失败，回退到单条处理: 'str' object has no attribute 'model_dump_json'
2025-06-05 22:52:23,767 - app.services.deepseek_client - ERROR - deepseek_client.py:1169 - AI批量提取失败，回退到单条处理: 'str' object has no attribute 'model_dump_json'
2025-06-05 22:53:02,189 - app.services.deepseek_client - ERROR - deepseek_client.py:1169 - AI批量提取失败，回退到单条处理: 'str' object has no attribute 'model_dump_json'
2025-06-05 22:53:41,102 - app.services.deepseek_client - ERROR - deepseek_client.py:1169 - AI批量提取失败，回退到单条处理: 'str' object has no attribute 'model_dump_json'
