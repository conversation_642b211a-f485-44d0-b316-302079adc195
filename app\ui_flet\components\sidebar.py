"""
侧边栏组件
提供导航菜单和快捷操作
"""

import flet as ft
import asyncio
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ..main_app import FletMainApp

class Sidebar:
    """侧边栏组件"""
    
    def __init__(self, app: 'FletMainApp'):
        self.app = app
        self.selected_item = "dashboard"
        self.menu_items = []
    
    def build(self) -> ft.Container:
        """构建侧边栏"""
        # 菜单项配置
        menu_config = [
            {"key": "dashboard", "icon": ft.icons.DASHBOARD, "title": "仪表盘", "color": ft.colors.BLUE},
            {"key": "groups", "icon": ft.icons.GROUP, "title": "群组管理", "color": ft.colors.GREEN},
            {"key": "analysis", "icon": ft.icons.ANALYTICS, "title": "分析任务", "color": ft.colors.ORANGE},
            {"key": "results", "icon": ft.icons.ASSESSMENT, "title": "分析结果", "color": ft.colors.PURPLE},
            {"key": "settings", "icon": ft.icons.SETTINGS, "title": "配置设置", "color": ft.colors.GREY_700},
        ]
        
        # 创建菜单项
        self.menu_items = []
        for item in menu_config:
            menu_item = self._create_menu_item(
                key=item["key"],
                icon=item["icon"],
                title=item["title"],
                color=item["color"],
                selected=(item["key"] == self.selected_item)
            )
            self.menu_items.append(menu_item)
        
        # 侧边栏内容
        sidebar_content = ft.Column(
            controls=[
                # Logo区域
                ft.Container(
                    content=ft.Column(
                        controls=[
                            ft.Icon(
                                ft.icons.SMART_TOY,
                                size=40,
                                color=ft.colors.WHITE
                            ),
                            ft.Text(
                                "AI分析工具",
                                size=16,
                                weight=ft.FontWeight.BOLD,
                                color=ft.colors.WHITE,
                                text_align=ft.TextAlign.CENTER
                            ),
                            ft.Text(
                                "v2.0",
                                size=12,
                                color=ft.colors.WHITE70,
                                text_align=ft.TextAlign.CENTER
                            )
                        ],
                        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                        spacing=5
                    ),
                    padding=ft.padding.all(20),
                    alignment=ft.alignment.center
                ),
                
                # 分隔线
                ft.Divider(color=ft.colors.WHITE24, height=1),
                
                # 菜单项
                ft.Container(
                    content=ft.Column(
                        controls=self.menu_items,
                        spacing=5
                    ),
                    padding=ft.padding.symmetric(horizontal=10, vertical=15),
                    expand=True
                ),
                
                # 底部信息
                ft.Container(
                    content=ft.Column(
                        controls=[
                            ft.Divider(color=ft.colors.WHITE24, height=1),
                            ft.Container(
                                content=ft.Row(
                                    controls=[
                                        ft.Icon(ft.icons.INFO_OUTLINE, size=16, color=ft.colors.WHITE70),
                                        ft.Text("状态: 就绪", size=12, color=ft.colors.WHITE70)
                                    ],
                                    spacing=8
                                ),
                                padding=ft.padding.symmetric(horizontal=15, vertical=10)
                            )
                        ],
                        spacing=0
                    )
                )
            ],
            spacing=0,
            expand=True
        )
        
        return ft.Container(
            content=sidebar_content,
            width=250,
            bgcolor=ft.colors.BLUE_GREY_800,
            border_radius=ft.border_radius.only(top_right=10, bottom_right=10),
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=10,
                color=ft.colors.BLACK26,
                offset=ft.Offset(2, 0)
            )
        )
    
    def _create_menu_item(self, key: str, icon: ft.icons, title: str, color: str, selected: bool = False) -> ft.Container:
        """创建菜单项"""
        
        async def on_click(e):
            await self.app.navigate_to(key)
        
        # 选中状态样式
        if selected:
            bg_color = ft.colors.WHITE12
            text_color = ft.colors.WHITE
            icon_color = color
        else:
            bg_color = ft.colors.TRANSPARENT
            text_color = ft.colors.WHITE70
            icon_color = ft.colors.WHITE70
        
        return ft.Container(
            content=ft.Row(
                controls=[
                    ft.Icon(icon, size=20, color=icon_color),
                    ft.Text(
                        title,
                        size=14,
                        color=text_color,
                        weight=ft.FontWeight.W500 if selected else ft.FontWeight.NORMAL
                    )
                ],
                spacing=15
            ),
            padding=ft.padding.symmetric(horizontal=15, vertical=12),
            border_radius=8,
            bgcolor=bg_color,
            on_click=on_click,
            ink=True,
            animate=ft.animation.Animation(200, ft.AnimationCurve.EASE_OUT)
        )
    
    async def update_selection(self, selected_key: str):
        """更新选中状态"""
        self.selected_item = selected_key
        
        # 重新构建菜单项
        menu_config = [
            {"key": "dashboard", "icon": ft.icons.DASHBOARD, "title": "仪表盘", "color": ft.colors.BLUE},
            {"key": "groups", "icon": ft.icons.GROUP, "title": "群组管理", "color": ft.colors.GREEN},
            {"key": "analysis", "icon": ft.icons.ANALYTICS, "title": "分析任务", "color": ft.colors.ORANGE},
            {"key": "results", "icon": ft.icons.ASSESSMENT, "title": "分析结果", "color": ft.colors.PURPLE},
            {"key": "settings", "icon": ft.icons.SETTINGS, "title": "配置设置", "color": ft.colors.GREY_700},
        ]
        
        # 更新菜单项
        for i, item in enumerate(menu_config):
            selected = (item["key"] == selected_key)
            
            # 更新样式
            if selected:
                self.menu_items[i].bgcolor = ft.colors.WHITE12
                self.menu_items[i].content.controls[0].color = item["color"]  # 图标
                self.menu_items[i].content.controls[1].color = ft.colors.WHITE  # 文字
                self.menu_items[i].content.controls[1].weight = ft.FontWeight.W500
            else:
                self.menu_items[i].bgcolor = ft.colors.TRANSPARENT
                self.menu_items[i].content.controls[0].color = ft.colors.WHITE70
                self.menu_items[i].content.controls[1].color = ft.colors.WHITE70
                self.menu_items[i].content.controls[1].weight = ft.FontWeight.NORMAL
