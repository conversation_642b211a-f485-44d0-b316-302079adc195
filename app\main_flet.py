"""
Flet应用主入口
启动现代化的Flet UI界面
"""

import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

# 配置日志
def setup_logging():
    """配置日志系统"""
    # 确保日志目录存在
    log_dir = Path("app/data/logs")
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / 'app_flet.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # 设置第三方库日志级别
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('flet').setLevel(logging.WARNING)

def main():
    """主函数"""
    try:
        # 设置日志
        setup_logging()
        logger = logging.getLogger(__name__)
        
        logger.info("启动微信群自我介绍分析工具 (Flet版本)")
        
        # 导入Flet应用
        from app.ui_flet.main_app import run_flet_app
        
        # 运行Flet应用
        run_flet_app()
        
        logger.info("应用正常退出")
        
    except ImportError as e:
        print(f"导入模块失败: {e}")
        print("请确保已安装所有依赖包：pip install -r requirements.txt")
        print("特别是Flet库：pip install flet>=0.21.0")
        sys.exit(1)
    except Exception as e:
        print(f"应用启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
