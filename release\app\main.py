"""
微信群用户自我介绍分析器 - 主程序入口
"""

import asyncio
import tkinter as tk
from tkinter import messagebox
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import logging
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.config import get_config, save_config
from app.ui.main_window import MainWindow
from app.utils.logger import setup_logger

class Application:
    """应用程序主类"""
    
    def __init__(self):
        self.config = get_config()
        self.root = None
        self.main_window = None
        
        # 设置日志
        setup_logger(self.config.log_level, self.config.debug)
        self.logger = logging.getLogger(__name__)
        
    def run(self):
        """运行应用程序"""
        try:
            # 创建主窗口
            self.root = ttk.Window(
                title="微信群用户自我介绍分析器",
                themename=self.config.ui.theme,
                size=(self.config.ui.window_width, self.config.ui.window_height),
                minsize=(self.config.ui.min_width, self.config.ui.min_height),
                resizable=(True, True)
            )
            
            # 设置窗口图标（如果有的话）
            try:
                icon_path = Path("app/assets/icon.ico")
                if icon_path.exists():
                    self.root.iconbitmap(str(icon_path))
            except Exception:
                pass  # 忽略图标设置错误
            
            # 创建主界面
            self.main_window = MainWindow(self.root)
            
            # 设置窗口关闭处理
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            
            # 居中显示窗口
            self._center_window()
            
            self.logger.info("应用程序启动成功")
            
            # 启动主循环
            self.root.mainloop()
            
        except Exception as e:
            self.logger.error(f"应用程序启动失败: {e}")
            messagebox.showerror("启动失败", f"应用程序启动失败:\n{str(e)}")
            
    def _center_window(self):
        """窗口居中显示"""
        try:
            self.root.update_idletasks()
            
            # 获取屏幕尺寸
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()
            
            # 获取窗口尺寸
            window_width = self.root.winfo_reqwidth()
            window_height = self.root.winfo_reqheight()
            
            # 计算居中位置
            x = (screen_width - window_width) // 2
            y = (screen_height - window_height) // 2
            
            # 设置窗口位置
            self.root.geometry(f"+{x}+{y}")
            
        except Exception as e:
            self.logger.warning(f"窗口居中失败: {e}")
    
    def on_closing(self):
        """窗口关闭处理"""
        try:
            # 检查是否有任务在运行
            if self.main_window and self.main_window.analysis_service.app_state.is_analyzing:
                result = messagebox.askyesno(
                    "确认退出",
                    "有分析任务正在运行，确定要退出吗？\n退出将取消当前任务。"
                )
                if result:
                    # 取消当前任务
                    self.main_window.analysis_service.cancel_analysis()
                else:
                    return
            
            # 保存配置
            save_config(self.config)
            
            self.logger.info("应用程序正常退出")
            
            # 销毁窗口
            self.root.destroy()
            
        except Exception as e:
            self.logger.error(f"退出时发生错误: {e}")
            self.root.destroy()

def main():
    """主函数"""
    try:
        # 检查Python版本
        if sys.version_info < (3, 8):
            messagebox.showerror("版本错误", "需要Python 3.8或更高版本")
            return
        
        # 创建并运行应用程序
        app = Application()
        app.run()
        
    except KeyboardInterrupt:
        print("\n用户中断程序运行")
    except Exception as e:
        error_msg = f"程序运行时发生严重错误: {str(e)}"
        print(error_msg)
        try:
            messagebox.showerror("严重错误", error_msg)
        except:
            pass

if __name__ == "__main__":
    main() 