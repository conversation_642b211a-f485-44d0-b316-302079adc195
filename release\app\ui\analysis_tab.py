"""
分析任务标签页
启动和监控分析任务
"""

import tkinter as tk
from tkinter import messagebox
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

class AnalysisTab:
    """分析任务标签页"""
    
    def __init__(self, parent, analysis_service):
        self.parent = parent
        self.analysis_service = analysis_service
        
        # 创建主框架
        self.frame = ttk.Frame(parent)
        
        # 状态变量
        self.is_running = False
        
        # 创建界面
        self._create_widgets()
        
        logger.debug("分析标签页初始化完成")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 控制面板
        control_frame = ttk.LabelFrame(self.frame, text="📊 分析控制", padding=10)
        control_frame.pack(fill=X, padx=10, pady=5)
        
        # 群组选择
        ttk.Label(control_frame, text="选择群组:").grid(row=0, column=0, sticky=W, pady=5)
        self.group_var = tk.StringVar()
        group_combo = ttk.Combobox(
            control_frame,
            textvariable=self.group_var,
            values=["测试群组1", "测试群组2", "测试群组3"],
            width=30
        )
        group_combo.grid(row=0, column=1, sticky=W, padx=(10, 0), pady=5)
        
        # 开始分析按钮
        self.start_btn = ttk.Button(
            control_frame,
            text="🚀 开始分析",
            command=self._start_analysis,
            bootstyle="success"
        )
        self.start_btn.grid(row=0, column=2, padx=(20, 0), pady=5)
        
        # 停止分析按钮
        self.stop_btn = ttk.Button(
            control_frame,
            text="⏹️ 停止分析",
            command=self._stop_analysis,
            bootstyle="danger",
            state="disabled"
        )
        self.stop_btn.grid(row=0, column=3, padx=(10, 0), pady=5)
        
        # 进度显示
        progress_frame = ttk.LabelFrame(self.frame, text="📈 分析进度", padding=10)
        progress_frame.pack(fill=X, padx=10, pady=5)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.progress_var,
            maximum=100,
            bootstyle="info"
        )
        self.progress_bar.pack(fill=X, pady=5)
        
        # 状态文本
        self.status_text = tk.Text(
            progress_frame,
            height=10,
            wrap=tk.WORD,
            state="disabled"
        )
        self.status_text.pack(fill=BOTH, expand=True, pady=5)
    
    def _start_analysis(self):
        """开始分析"""
        try:
            if not self.group_var.get():
                messagebox.showwarning("警告", "请先选择要分析的群组")
                return
            
            self.is_running = True
            self.start_btn.config(state="disabled")
            self.stop_btn.config(state="normal")
            
            self._add_status_message("开始分析任务...")
            self.progress_var.set(0)
            
            # 模拟分析进度
            self._simulate_progress()
            
        except Exception as e:
            logger.error(f"启动分析失败: {e}")
            messagebox.showerror("错误", f"启动分析失败:\n{str(e)}")
    
    def _stop_analysis(self):
        """停止分析"""
        try:
            self.is_running = False
            self.start_btn.config(state="normal")
            self.stop_btn.config(state="disabled")
            
            self._add_status_message("分析任务已停止")
            
        except Exception as e:
            logger.error(f"停止分析失败: {e}")
            messagebox.showerror("错误", f"停止分析失败:\n{str(e)}")
    
    def _simulate_progress(self):
        """模拟分析进度"""
        if self.is_running and self.progress_var.get() < 100:
            current = self.progress_var.get()
            self.progress_var.set(current + 2)
            
            if current % 20 == 0:
                self._add_status_message(f"分析进度: {current:.0f}%")
            
            self.frame.after(200, self._simulate_progress)
        elif self.progress_var.get() >= 100:
            self._add_status_message("分析任务完成！")
            self.is_running = False
            self.start_btn.config(state="normal")
            self.stop_btn.config(state="disabled")
    
    def _add_status_message(self, message: str):
        """添加状态消息"""
        self.status_text.config(state="normal")
        self.status_text.insert(tk.END, f"{message}\n")
        self.status_text.see(tk.END)
        self.status_text.config(state="disabled")
    
    def on_progress_update(self, event: str, data: Dict[str, Any]):
        """处理进度更新"""
        try:
            if event == "analysis_started":
                self._add_status_message("分析任务已启动")
            elif event == "analysis_completed":
                self._add_status_message("分析任务已完成")
                self.progress_var.set(100)
        except Exception as e:
            logger.error(f"处理进度更新失败: {e}") 