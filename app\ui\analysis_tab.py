"""
分析任务标签页
启动和监控分析任务，显示AI分析日志和统计信息
"""

import tkinter as tk
from tkinter import messagebox
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import asyncio
import threading
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class AnalysisTab:
    """分析任务标签页"""
    
    def __init__(self, parent, analysis_service, groups_tab=None):
        self.parent = parent
        self.analysis_service = analysis_service
        self.groups_tab = groups_tab  # 引用群组标签页以获取群组数据
        
        # 创建主框架
        self.frame = ttk.Frame(parent)
        
        # 状态变量
        self.is_running = False
        self.current_task = None
        
        # 群组数据
        self.group_data = {}  # 群组数据字典
        self.all_group_data = {}  # 所有群组数据（用于搜索）
        
        # 创建界面
        self._create_widgets()
        
        # 设置进度回调
        self.analysis_service.set_progress_callback(self.on_progress_update)
        
        logger.debug("分析标签页初始化完成")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 使用Notebook创建多个标签页
        self.notebook = ttk.Notebook(self.frame)
        self.notebook.pack(fill=BOTH, expand=True, padx=10, pady=5)
        
        # 创建分析控制标签页
        self._create_analysis_control_tab()
        
        # 创建AI日志标签页
        self._create_ai_log_tab()
        
        # 更新客户端信息显示
        self._update_client_info_display()
        
        # 设置搜索框占位符（需要在所有控件创建完成后调用）
        self._setup_search_placeholder()
    
    def _create_analysis_control_tab(self):
        """创建分析控制标签页"""
        control_tab = ttk.Frame(self.notebook)
        self.notebook.add(control_tab, text="📊 分析控制")
        
        # 控制面板
        control_frame = ttk.LabelFrame(control_tab, text="📊 分析控制", padding=10)
        control_frame.pack(fill=X, padx=10, pady=5)
        
        # 第一行：群组选择
        row1 = ttk.Frame(control_frame)
        row1.pack(fill=X, pady=5)
        
        ttk.Label(row1, text="选择群组:").pack(side=LEFT)
        
        # 群组搜索框
        search_frame = ttk.Frame(row1)
        search_frame.pack(side=LEFT, padx=(10, 0))
        
        self.group_search_var = tk.StringVar()
        self.group_search_entry = ttk.Entry(
            search_frame,
            textvariable=self.group_search_var,
            width=20
        )
        self.group_search_entry.pack(side=LEFT)
        self.group_search_entry.bind('<KeyRelease>', self._on_group_search)
        
        # 清空搜索按钮
        clear_search_btn = ttk.Button(
            search_frame,
            text="✕",
            command=self._clear_group_search,
            width=3
        )
        clear_search_btn.pack(side=LEFT, padx=(2, 0))
        
        # 群组下拉框
        self.group_var = tk.StringVar()
        self.group_combo = ttk.Combobox(
            search_frame,
            textvariable=self.group_var,
            values=[],
            width=40,
            state="readonly"
        )
        self.group_combo.pack(side=LEFT, padx=(5, 0))
        
        # 刷新群组按钮
        refresh_groups_btn = ttk.Button(
            row1,
            text="🔄",
            command=self.refresh_groups,
            width=3
        )
        refresh_groups_btn.pack(side=LEFT, padx=(5, 0))
        
        # 第二行：时间范围
        row2 = ttk.Frame(control_frame)
        row2.pack(fill=X, pady=5)
        
        # 时间选择模式
        ttk.Label(row2, text="时间选择:").pack(side=LEFT)
        self.time_mode_var = tk.StringVar(value="days")
        time_mode_frame = ttk.Frame(row2)
        time_mode_frame.pack(side=LEFT, padx=(10, 0))
        
        days_radio = ttk.Radiobutton(
            time_mode_frame,
            text="按天数",
            variable=self.time_mode_var,
            value="days",
            command=self._on_time_mode_changed
        )
        days_radio.pack(side=LEFT, padx=(0, 10))
        
        date_range_radio = ttk.Radiobutton(
            time_mode_frame,
            text="按日期范围",
            variable=self.time_mode_var,
            value="date_range",
            command=self._on_time_mode_changed
        )
        date_range_radio.pack(side=LEFT)
        
        # 第三行：具体时间设置
        row2_1 = ttk.Frame(control_frame)
        row2_1.pack(fill=X, pady=(5, 0))
        
        # 天数选择框架
        self.days_frame = ttk.Frame(row2_1)
        self.days_frame.pack(side=LEFT, fill=X, expand=True)
        
        ttk.Label(self.days_frame, text="分析天数:").pack(side=LEFT)
        self.days_var = tk.IntVar(value=7)
        days_spin = ttk.Spinbox(
            self.days_frame,
            from_=1,
            to=30,
            textvariable=self.days_var,
            width=10
        )
        days_spin.pack(side=LEFT, padx=(10, 0))
        ttk.Label(self.days_frame, text="天").pack(side=LEFT, padx=(5, 0))
        
        # 日期范围选择框架
        self.date_range_frame = ttk.Frame(row2_1)
        
        # 开始日期
        start_date_frame = ttk.Frame(self.date_range_frame)
        start_date_frame.pack(side=LEFT, padx=(0, 20))
        
        ttk.Label(start_date_frame, text="开始日期:").pack(anchor=W)
        self.start_date_var = tk.StringVar(value=(datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d"))
        self.start_date_entry = ttk.Entry(
            start_date_frame,
            textvariable=self.start_date_var,
            width=12
        )
        self.start_date_entry.pack(pady=(2, 0))
        self.start_date_entry.bind('<FocusOut>', self._validate_start_date)
        
        # 日期格式提示
        start_format_hint = ttk.Label(
            start_date_frame, 
            text="格式: YYYY-MM-DD", 
            font=("微软雅黑", 8), 
            foreground="gray"
        )
        start_format_hint.pack()
        
        # 结束日期
        end_date_frame = ttk.Frame(self.date_range_frame)
        end_date_frame.pack(side=LEFT)
        
        ttk.Label(end_date_frame, text="结束日期:").pack(anchor=W)
        self.end_date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        self.end_date_entry = ttk.Entry(
            end_date_frame,
            textvariable=self.end_date_var,
            width=12
        )
        self.end_date_entry.pack(pady=(2, 0))
        self.end_date_entry.bind('<FocusOut>', self._validate_end_date)
        
        # 日期格式提示
        format_hint = ttk.Label(
            end_date_frame, 
            text="格式: YYYY-MM-DD", 
            font=("微软雅黑", 8), 
            foreground="gray"
        )
        format_hint.pack()
        
        # 快速日期选择按钮
        quick_date_frame = ttk.Frame(self.date_range_frame)
        quick_date_frame.pack(side=LEFT, padx=(20, 0))
        
        ttk.Label(quick_date_frame, text="快速选择:").pack(anchor=W)
        quick_buttons_frame = ttk.Frame(quick_date_frame)
        quick_buttons_frame.pack(pady=(2, 0))
        
        # 快速选择按钮
        ttk.Button(quick_buttons_frame, text="今天", command=lambda: self._set_quick_date(0), width=6).pack(side=LEFT, padx=(0, 2))
        ttk.Button(quick_buttons_frame, text="7天", command=lambda: self._set_quick_date(7), width=6).pack(side=LEFT, padx=(0, 2))
        ttk.Button(quick_buttons_frame, text="30天", command=lambda: self._set_quick_date(30), width=6).pack(side=LEFT)
        
        # 初始状态显示天数选择
        self._on_time_mode_changed()
        
        # 第三行：控制按钮
        row3 = ttk.Frame(control_frame)
        row3.pack(fill=X, pady=10)
        
        # 开始分析按钮
        self.start_btn = ttk.Button(
            row3,
            text="🚀 开始分析",
            command=self._start_analysis,
            bootstyle="success"
        )
        self.start_btn.pack(side=LEFT)
        
        # 停止分析按钮
        self.stop_btn = ttk.Button(
            row3,
            text="⏹️ 停止分析",
            command=self._stop_analysis,
            bootstyle="danger",
            state="disabled"
        )
        self.stop_btn.pack(side=LEFT, padx=(10, 0))
        
        # 测试连接按钮
        test_btn = ttk.Button(
            row3,
            text="🔌 测试连接",
            command=self._test_connections,
            bootstyle="info"
        )
        test_btn.pack(side=LEFT, padx=(10, 0))
        
        # 第四行：客户端信息显示
        row4 = ttk.Frame(control_frame)
        row4.pack(fill=X, pady=(10, 0))
        
        ttk.Label(row4, text="AI客户端:", font=("微软雅黑", 9, "bold")).pack(side=LEFT)
        self.client_info_var = tk.StringVar(value="检测中...")
        self.client_info_label = ttk.Label(
            row4, 
            textvariable=self.client_info_var,
            font=("微软雅黑", 9),
            bootstyle="info"
        )
        self.client_info_label.pack(side=LEFT, padx=(5, 0))
        
        # 进度显示
        progress_frame = ttk.LabelFrame(control_tab, text="📈 分析进度", padding=10)
        progress_frame.pack(fill=BOTH, expand=True, padx=10, pady=5)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.progress_var,
            maximum=100,
            bootstyle="info"
        )
        self.progress_bar.pack(fill=X, pady=5)
        
        # 当前状态标签
        self.status_var = tk.StringVar(value="准备就绪")
        status_label = ttk.Label(
            progress_frame,
            textvariable=self.status_var,
            font=("微软雅黑", 10)
        )
        status_label.pack(pady=5)
        
        # 状态文本
        self.status_text = tk.Text(
            progress_frame,
            height=12,
            wrap=tk.WORD,
            state="disabled",
            font=("微软雅黑", 9)
        )
        
        # 添加滚动条
        text_scrollbar = ttk.Scrollbar(progress_frame, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=text_scrollbar.set)
        
        self.status_text.pack(side=LEFT, fill=BOTH, expand=True, pady=5)
        text_scrollbar.pack(side=RIGHT, fill=Y, pady=5)
    
    def _create_ai_log_tab(self):
        """创建AI日志标签页"""
        log_tab = ttk.Frame(self.notebook)
        self.notebook.add(log_tab, text="🤖 AI分析日志")
        
        # AI统计信息面板
        stats_frame = ttk.LabelFrame(log_tab, text="📊 AI分析统计", padding=10)
        stats_frame.pack(fill=X, padx=10, pady=5)
        
        # 创建统计信息网格
        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill=X)
        
        # 第一行统计
        row1 = ttk.Frame(stats_grid)
        row1.pack(fill=X, pady=2)
        
        ttk.Label(row1, text="总请求数:", font=("微软雅黑", 9, "bold")).pack(side=LEFT)
        self.total_requests_var = tk.StringVar(value="0")
        ttk.Label(row1, textvariable=self.total_requests_var, font=("微软雅黑", 9)).pack(side=LEFT, padx=(5, 20))
        
        ttk.Label(row1, text="成功率:", font=("微软雅黑", 9, "bold")).pack(side=LEFT)
        self.success_rate_var = tk.StringVar(value="0%")
        ttk.Label(row1, textvariable=self.success_rate_var, font=("微软雅黑", 9)).pack(side=LEFT, padx=(5, 20))
        
        ttk.Label(row1, text="平均耗时:", font=("微软雅黑", 9, "bold")).pack(side=LEFT)
        self.avg_time_var = tk.StringVar(value="0ms")
        ttk.Label(row1, textvariable=self.avg_time_var, font=("微软雅黑", 9)).pack(side=LEFT, padx=(5, 0))
        
        # 第二行统计
        row2 = ttk.Frame(stats_grid)
        row2.pack(fill=X, pady=2)
        
        ttk.Label(row2, text="检测请求:", font=("微软雅黑", 9, "bold")).pack(side=LEFT)
        self.detection_count_var = tk.StringVar(value="0")
        ttk.Label(row2, textvariable=self.detection_count_var, font=("微软雅黑", 9)).pack(side=LEFT, padx=(5, 20))
        
        ttk.Label(row2, text="提取请求:", font=("微软雅黑", 9, "bold")).pack(side=LEFT)
        self.extraction_count_var = tk.StringVar(value="0")
        ttk.Label(row2, textvariable=self.extraction_count_var, font=("微软雅黑", 9)).pack(side=LEFT, padx=(5, 20))
        
        # 刷新按钮
        refresh_stats_btn = ttk.Button(
            row2,
            text="🔄 刷新统计",
            command=self._refresh_ai_stats,
            bootstyle="info-outline"
        )
        refresh_stats_btn.pack(side=RIGHT)
        
        # AI日志面板
        log_frame = ttk.LabelFrame(log_tab, text="📋 AI分析日志", padding=10)
        log_frame.pack(fill=BOTH, expand=True, padx=10, pady=5)
        
        # 日志控制栏
        log_control = ttk.Frame(log_frame)
        log_control.pack(fill=X, pady=(0, 5))
        
        # 筛选选项
        ttk.Label(log_control, text="筛选类型:").pack(side=LEFT)
        self.log_filter_var = tk.StringVar(value="全部")
        filter_combo = ttk.Combobox(
            log_control,
            textvariable=self.log_filter_var,
            values=["全部", "检测(detection)", "提取(extraction)", "成功", "失败"],
            width=15,
            state="readonly"
        )
        filter_combo.pack(side=LEFT, padx=(5, 10))
        
        # 刷新日志按钮
        refresh_log_btn = ttk.Button(
            log_control,
            text="🔄 刷新日志",
            command=self._refresh_ai_log,
            bootstyle="info-outline"
        )
        refresh_log_btn.pack(side=LEFT)
        
        # 清空日志按钮
        clear_log_btn = ttk.Button(
            log_control,
            text="🗑️ 清空日志",
            command=self._clear_ai_log,
            bootstyle="warning-outline"
        )
        clear_log_btn.pack(side=LEFT, padx=(5, 0))
        
        # 日志表格
        log_columns = ("时间", "类型", "状态", "置信度", "处理时间", "请求ID", "输入内容")
        self.log_tree = ttk.Treeview(
            log_frame,
            columns=log_columns,
            show="headings",
            height=15
        )
        
        # 配置列
        self.log_tree.heading("时间", text="时间")
        self.log_tree.column("时间", width=120, minwidth=100)
        
        self.log_tree.heading("类型", text="分析类型")
        self.log_tree.column("类型", width=80, minwidth=60)
        
        self.log_tree.heading("状态", text="状态")
        self.log_tree.column("状态", width=60, minwidth=50)
        
        self.log_tree.heading("置信度", text="置信度")
        self.log_tree.column("置信度", width=80, minwidth=60)
        
        self.log_tree.heading("处理时间", text="耗时(ms)")
        self.log_tree.column("处理时间", width=80, minwidth=60)
        
        self.log_tree.heading("请求ID", text="请求ID")
        self.log_tree.column("请求ID", width=80, minwidth=60)
        
        self.log_tree.heading("输入内容", text="输入内容")
        self.log_tree.column("输入内容", width=300, minwidth=200)
        
        # 添加滚动条
        log_scrollbar_y = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_tree.yview)
        log_scrollbar_x = ttk.Scrollbar(log_frame, orient="horizontal", command=self.log_tree.xview)
        self.log_tree.configure(yscrollcommand=log_scrollbar_y.set, xscrollcommand=log_scrollbar_x.set)
        
        # 打包组件
        self.log_tree.pack(side=LEFT, fill=BOTH, expand=True)
        log_scrollbar_y.pack(side=RIGHT, fill=Y)
        log_scrollbar_x.pack(side=BOTTOM, fill=X)
        
        # 绑定双击事件查看详情
        self.log_tree.bind("<Double-1>", self._show_log_detail)
    
    def _refresh_ai_stats(self):
        """刷新AI统计信息"""
        try:
            stats = self.analysis_service.get_ai_analysis_stats()
            
            self.total_requests_var.set(str(stats.get('total_requests', 0)))
            self.success_rate_var.set(f"{stats.get('success_rate', 0):.1%}")
            self.avg_time_var.set(f"{stats.get('avg_processing_time_ms', 0):.1f}ms")
            self.detection_count_var.set(str(stats.get('detection_count', 0)))
            self.extraction_count_var.set(str(stats.get('extraction_count', 0)))
            
        except Exception as e:
            logger.error(f"刷新AI统计信息失败: {e}")
            messagebox.showerror("错误", f"刷新AI统计信息失败: {str(e)}")
    
    def _refresh_ai_log(self):
        """刷新AI日志"""
        try:
            # 清空现有日志
            for item in self.log_tree.get_children():
                self.log_tree.delete(item)
            
            # 获取日志数据
            logs = self.analysis_service.get_ai_analysis_log()
            filter_type = self.log_filter_var.get()
            
            # 应用筛选
            filtered_logs = []
            for log in logs:
                if filter_type == "全部":
                    filtered_logs.append(log)
                elif filter_type == "检测(detection)" and log.get('analysis_type') == 'detection':
                    filtered_logs.append(log)
                elif filter_type == "提取(extraction)" and log.get('analysis_type') == 'extraction':
                    filtered_logs.append(log)
                elif filter_type == "成功" and log.get('success', False):
                    filtered_logs.append(log)
                elif filter_type == "失败" and not log.get('success', True):
                    filtered_logs.append(log)
            
            # 按时间倒序排列（最新的在前）
            filtered_logs.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
            
            # 添加到表格
            for log in filtered_logs:
                # 格式化时间
                timestamp = log.get('timestamp', '')
                if isinstance(timestamp, str):
                    try:
                        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        time_str = dt.strftime("%H:%M:%S")
                    except:
                        time_str = timestamp[:8] if len(timestamp) > 8 else timestamp
                else:
                    time_str = str(timestamp)[:8]
                
                # 分析类型
                analysis_type = log.get('analysis_type', 'unknown')
                type_display = "检测" if analysis_type == 'detection' else "提取" if analysis_type == 'extraction' else analysis_type
                
                # 状态
                status = "✅" if log.get('success', False) else "❌"
                
                # 置信度（仅检测类型有）
                confidence = ""
                if analysis_type == 'detection' and log.get('success', False):
                    structured_result = log.get('structured_result', {})
                    if isinstance(structured_result, dict) and 'confidence' in structured_result:
                        confidence = f"{structured_result['confidence']:.2f}"
                
                # 处理时间
                processing_time = str(log.get('processing_time_ms', 0))
                
                # 请求ID
                request_id = log.get('request_id', '')[:8]  # 只显示前8位
                
                # 输入内容（截断）
                input_text = log.get('input_text', '')
                if len(input_text) > 50:
                    input_text = input_text[:50] + "..."
                
                # 插入行
                item_id = self.log_tree.insert(
                    "",
                    "end",
                    values=(time_str, type_display, status, confidence, processing_time, request_id, input_text)
                )
                
                # 根据状态设置行颜色
                if not log.get('success', False):
                    self.log_tree.set(item_id, "状态", "❌")
            
            # 刷新统计信息
            self._refresh_ai_stats()
            
        except Exception as e:
            logger.error(f"刷新AI日志失败: {e}")
            messagebox.showerror("错误", f"刷新AI日志失败: {str(e)}")
    
    def _clear_ai_log(self):
        """清空AI日志"""
        if messagebox.askyesno("确认", "确定要清空AI分析日志吗？"):
            try:
                # 清空表格
                for item in self.log_tree.get_children():
                    self.log_tree.delete(item)
                
                # 重置统计信息
                self.total_requests_var.set("0")
                self.success_rate_var.set("0%")
                self.avg_time_var.set("0ms")
                self.detection_count_var.set("0")
                self.extraction_count_var.set("0")
                
                messagebox.showinfo("成功", "AI分析日志已清空")
                
            except Exception as e:
                logger.error(f"清空AI日志失败: {e}")
                messagebox.showerror("错误", f"清空AI日志失败: {str(e)}")
    
    def _show_log_detail(self, event):
        """显示日志详情"""
        selection = self.log_tree.selection()
        if not selection:
            return
        
        item = selection[0]
        values = self.log_tree.item(item, "values")
        if not values:
            return
        
        # 获取完整的日志数据
        request_id = values[5]  # 请求ID列
        
        try:
            logs = self.analysis_service.get_ai_analysis_log()
            target_log = None
            
            for log in logs:
                if log.get('request_id', '')[:8] == request_id:
                    target_log = log
                    break
            
            if target_log:
                self._show_log_detail_window(target_log)
            else:
                messagebox.showwarning("警告", "找不到对应的日志详情")
                
        except Exception as e:
            logger.error(f"显示日志详情失败: {e}")
            messagebox.showerror("错误", f"显示日志详情失败: {str(e)}")
    
    def _show_log_detail_window(self, log_data):
        """显示日志详情窗口"""
        detail_window = tk.Toplevel(self.parent)
        detail_window.title("AI分析日志详情")
        detail_window.geometry("800x600")
        detail_window.transient(self.parent)
        detail_window.grab_set()
        
        # 创建滚动文本框
        text_frame = ttk.Frame(detail_window)
        text_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        detail_text = tk.Text(
            text_frame,
            wrap=tk.WORD,
            font=("微软雅黑", 10)
        )
        
        scrollbar = ttk.Scrollbar(text_frame, command=detail_text.yview)
        detail_text.configure(yscrollcommand=scrollbar.set)
        
        detail_text.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # 添加详情内容
        content = f"""AI分析日志详情

请求ID: {log_data.get('request_id', 'N/A')}
时间: {log_data.get('timestamp', 'N/A')}
分析类型: {log_data.get('analysis_type', 'N/A')}
成功状态: {'✅ 成功' if log_data.get('success', False) else '❌ 失败'}
处理时间: {log_data.get('processing_time_ms', 0)}ms

输入文本:
{'-' * 50}
{log_data.get('input_text', 'N/A')}

结构化结果:
{'-' * 50}
{str(log_data.get('structured_result', {})) if log_data.get('success', False) else 'N/A'}

AI原始响应:
{'-' * 50}
{log_data.get('ai_response_raw', 'N/A')}

错误信息:
{'-' * 50}
{log_data.get('error_message', 'N/A') if not log_data.get('success', False) else '无错误'}
"""
        
        detail_text.insert(tk.END, content)
        detail_text.configure(state="disabled")
        
        # 关闭按钮
        close_btn = ttk.Button(
            detail_window,
            text="关闭",
            command=detail_window.destroy
        )
        close_btn.pack(pady=10)

    def refresh_groups(self):
        """刷新群组列表"""
        def _refresh_async():
            try:
                # 创建事件循环获取群组
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    groups = loop.run_until_complete(self.analysis_service.get_chat_groups())
                    
                    # 更新UI
                    self.parent.after(0, self._update_groups_ui, groups)
                    
                finally:
                    loop.close()
                    
            except Exception as e:
                logger.error(f"刷新群组失败: {e}")
                self.parent.after(0, self._show_error, f"刷新群组失败: {str(e)}")
        
        # 在后台线程执行
        thread = threading.Thread(target=_refresh_async, daemon=True)
        thread.start()
        
        self._add_status_message("正在刷新群组列表...")
    
    def _update_groups_ui(self, groups):
        """更新群组UI"""
        try:
            # 构建群组选项
            group_options = []
            self.all_group_data = {}  # 保存所有群组数据用于搜索
            self.group_data = {}  # 当前显示的群组数据
            
            for group in groups:
                display_text = f"{group.name} ({group.member_count}人)"
                group_options.append(display_text)
                self.all_group_data[display_text] = group
                self.group_data[display_text] = group
            
            # 更新下拉框
            self.group_combo['values'] = group_options
            
            if group_options:
                self.group_combo.set(group_options[0])  # 选择第一个
                self._add_status_message(f"已加载 {len(groups)} 个群组")
            else:
                self._add_status_message("未找到任何群组")
                
        except Exception as e:
            logger.error(f"更新群组UI失败: {e}")
            self._show_error(f"更新群组UI失败: {str(e)}")
    
    def _show_error(self, message):
        """显示错误消息"""
        self._add_status_message(f"❌ {message}")
        messagebox.showerror("错误", message)
    
    def _test_connections(self):
        """测试API连接"""
        def _test_async():
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    self.parent.after(0, lambda: self._add_status_message("正在测试API连接..."))
                    results = loop.run_until_complete(self.analysis_service.test_api_connections())
                    
                    # 更新UI
                    self.parent.after(0, self._show_connection_results, results)
                    
                finally:
                    loop.close()
                    
            except Exception as e:
                logger.error(f"测试连接失败: {e}")
                self.parent.after(0, self._show_error, f"测试连接失败: {str(e)}")
        
        thread = threading.Thread(target=_test_async, daemon=True)
        thread.start()
    
    def _show_connection_results(self, results):
        """显示连接测试结果"""
        message = "API连接测试结果:\n"
        for service, status in results.items():
            status_text = "✅ 连接成功" if status else "❌ 连接失败"
            message += f"  {service}: {status_text}\n"
            self._add_status_message(f"{service}: {status_text}")
        
        if all(results.values()):
            messagebox.showinfo("连接测试", "所有API连接正常！")
        else:
            messagebox.showwarning("连接测试", message)
    
    def _start_analysis(self):
        """开始分析"""
        try:
            if not self.group_var.get():
                messagebox.showwarning("警告", "请先选择要分析的群组")
                return
            
            # 获取选中的群组
            group_name = self.group_var.get()
            group = None
            
            # 从本地的group_data中查找群组
            if hasattr(self, 'group_data') and group_name in self.group_data:
                group = self.group_data[group_name]
            
            if not group:
                messagebox.showerror("错误", "未找到选中的群组，请先刷新群组列表")
                return
            
            # 显示分析确认对话框
            if not self._show_analysis_confirmation(group):
                return
            
            # 设置UI状态
            self.is_running = True
            self.current_task = None
            self.start_btn.config(state="disabled")
            self.stop_btn.config(state="normal")
            self.group_combo.config(state="disabled")
            
            # 清空状态显示
            self.status_text.config(state="normal")
            self.status_text.delete(1.0, tk.END)
            self.status_text.config(state="disabled")
            
            self._add_status_message("🚀 启动分析任务...")
            self.progress_var.set(0)
            self.status_var.set("正在初始化...")
            
            # 显示详细进度窗口
            self._show_analysis_progress_window(group)
            
            # 启动异步分析
            self._start_analysis_async(group)
            
        except Exception as e:
            logger.error(f"启动分析失败: {e}")
            messagebox.showerror("错误", f"启动分析失败:\n{str(e)}")
            self._reset_ui_state()
    
    def _show_analysis_confirmation(self, group) -> bool:
        """显示分析确认对话框"""
        message = f"""准备分析群组: {group.name}
        
分析设置:
• 群组ID: {group.id}
• 时间模式: {self.time_mode_var.get()}
• 开始日期: {self.start_date_var.get() if self.time_mode_var.get() == "date_range" else "最近" + str(self.days_var.get()) + "天"}
• 结束日期: {self.end_date_var.get() if self.time_mode_var.get() == "date_range" else "今天"}

分析流程:
1. 🔍 获取群组消息
2. 🤖 AI检测自我介绍
3. 📊 提取用户画像
4. 💾 保存结果

是否开始分析？"""
        
        return messagebox.askyesno("确认分析", message, icon="question")
    
    def _show_analysis_progress_window(self, group):
        """显示详细分析进度窗口"""
        # 创建进度窗口
        self.progress_window = tk.Toplevel(self.parent)
        self.progress_window.title("分析进度详情")
        self.progress_window.geometry("600x400")
        self.progress_window.transient(self.parent)
        self.progress_window.grab_set()
        
        # 防止用户关闭窗口
        self.progress_window.protocol("WM_DELETE_WINDOW", self._on_progress_window_close)
        
        # 创建窗口内容
        main_frame = ttk.Frame(self.progress_window, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(
            main_frame, 
            text=f"🚀 正在分析群组: {group.name}",
            font=("微软雅黑", 14, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # 当前阶段信息
        self.analysis_stage_var = tk.StringVar(value="准备中...")
        stage_label = ttk.Label(main_frame, textvariable=self.analysis_stage_var, font=("微软雅黑", 12))
        stage_label.pack(pady=(0, 10))
        
        # 总体进度条
        ttk.Label(main_frame, text="总体进度:", font=("微软雅黑", 10, "bold")).pack(anchor=W)
        self.overall_progress_var = tk.DoubleVar()
        overall_progress_bar = ttk.Progressbar(
            main_frame,
            variable=self.overall_progress_var,
            maximum=100,
            bootstyle="success",
            length=500
        )
        overall_progress_bar.pack(fill=X, pady=(5, 15))
        
        # 当前阶段进度条
        ttk.Label(main_frame, text="当前阶段:", font=("微软雅黑", 10, "bold")).pack(anchor=W)
        self.stage_progress_var = tk.DoubleVar()
        stage_progress_bar = ttk.Progressbar(
            main_frame,
            variable=self.stage_progress_var,
            maximum=100,
            bootstyle="info",
            length=500
        )
        stage_progress_bar.pack(fill=X, pady=(5, 15))
        
        # 统计信息
        stats_frame = ttk.LabelFrame(main_frame, text="实时统计", padding=10)
        stats_frame.pack(fill=X, pady=(0, 15))
        
        # 创建统计显示网格
        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill=X)
        
        # 第一行
        row1 = ttk.Frame(stats_grid)
        row1.pack(fill=X, pady=2)
        
        ttk.Label(row1, text="处理消息:", font=("微软雅黑", 9, "bold")).pack(side=LEFT)
        self.processed_messages_var = tk.StringVar(value="0/0")
        ttk.Label(row1, textvariable=self.processed_messages_var, font=("微软雅黑", 9)).pack(side=LEFT, padx=(5, 20))
        
        ttk.Label(row1, text="发现自我介绍:", font=("微软雅黑", 9, "bold")).pack(side=LEFT)
        self.found_intros_var = tk.StringVar(value="0")
        ttk.Label(row1, textvariable=self.found_intros_var, font=("微软雅黑", 9), foreground="green").pack(side=LEFT, padx=(5, 0))
        
        # 第二行
        row2 = ttk.Frame(stats_grid)
        row2.pack(fill=X, pady=2)
        
        ttk.Label(row2, text="AI请求:", font=("微软雅黑", 9, "bold")).pack(side=LEFT)
        self.ai_requests_var = tk.StringVar(value="0")
        ttk.Label(row2, textvariable=self.ai_requests_var, font=("微软雅黑", 9)).pack(side=LEFT, padx=(5, 20))
        
        ttk.Label(row2, text="平均速度:", font=("微软雅黑", 9, "bold")).pack(side=LEFT)
        self.avg_speed_var = tk.StringVar(value="0/秒")
        ttk.Label(row2, textvariable=self.avg_speed_var, font=("微软雅黑", 9)).pack(side=LEFT, padx=(5, 0))
        
        # 详细日志
        log_frame = ttk.LabelFrame(main_frame, text="分析日志", padding=10)
        log_frame.pack(fill=BOTH, expand=True, pady=(0, 15))
        
        self.analysis_log_text = tk.Text(
            log_frame,
            height=8,
            wrap=tk.WORD,
            font=("Consolas", 9),
            state="disabled"
        )
        
        analysis_scrollbar = ttk.Scrollbar(log_frame, command=self.analysis_log_text.yview)
        self.analysis_log_text.configure(yscrollcommand=analysis_scrollbar.set)
        
        self.analysis_log_text.pack(side=LEFT, fill=BOTH, expand=True)
        analysis_scrollbar.pack(side=RIGHT, fill=Y)
        
        # 按钮区域
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=X)
        
        self.analysis_cancel_btn = ttk.Button(
            btn_frame,
            text="取消分析",
            command=self._stop_analysis,
            bootstyle="danger"
        )
        self.analysis_cancel_btn.pack(side=LEFT)
        
        self.analysis_close_btn = ttk.Button(
            btn_frame,
            text="最小化到后台",
            command=self._minimize_progress_window,
            bootstyle="secondary"
        )
        self.analysis_close_btn.pack(side=RIGHT)
        
        # 添加第一条日志
        self._add_analysis_log("🚀 分析任务启动")
    
    def _add_analysis_log(self, message: str):
        """添加分析日志消息"""
        def add_message():
            if hasattr(self, 'analysis_log_text'):
                timestamp = datetime.now().strftime("%H:%M:%S")
                formatted_message = f"[{timestamp}] {message}\n"
                
                self.analysis_log_text.config(state="normal")
                self.analysis_log_text.insert(tk.END, formatted_message)
                self.analysis_log_text.see(tk.END)
                self.analysis_log_text.config(state="disabled")
        
        # 在主线程中执行UI更新
        if hasattr(self, 'parent'):
            self.parent.after(0, add_message)
    
    def _on_progress_window_close(self):
        """处理进度窗口关闭事件"""
        # 不允许直接关闭，只能最小化
        self._minimize_progress_window()
    
    def _minimize_progress_window(self):
        """最小化进度窗口"""
        if hasattr(self, 'progress_window'):
            self.progress_window.withdraw()  # 隐藏窗口
            
            # 在主界面状态栏显示提示
            self.status_var.set("分析正在后台进行（点击查看详情）")
            
            # 添加显示进度窗口的按钮（如果不存在）
            if not hasattr(self, 'show_progress_btn'):
                self.show_progress_btn = ttk.Button(
                    self.frame,
                    text="📊 显示分析详情",
                    command=self._show_progress_window,
                    bootstyle="info-outline"
                )
                # 暂时添加到状态文本上方
                self.show_progress_btn.pack(before=self.status_text, pady=(0, 5))
    
    def _show_progress_window(self):
        """显示进度窗口"""
        if hasattr(self, 'progress_window'):
            self.progress_window.deiconify()  # 显示窗口
            self.progress_window.lift()  # 提升到前台
    
    def _close_progress_window(self):
        """关闭进度窗口"""
        if hasattr(self, 'progress_window'):
            self.progress_window.destroy()
            delattr(self, 'progress_window')
        
        # 移除显示进度按钮
        if hasattr(self, 'show_progress_btn'):
            self.show_progress_btn.destroy()
            delattr(self, 'show_progress_btn')
    
    def _start_analysis_async(self, group):
        """异步启动分析任务"""
        def _analysis_worker():
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    # 根据选择的时间模式计算时间范围
                    if self.time_mode_var.get() == "days":
                        # 按天数计算
                        end_time = datetime.now()
                        start_time = end_time - timedelta(days=self.days_var.get())
                        time_desc = f"最近 {self.days_var.get()} 天"
                    else:
                        # 按日期范围
                        try:
                            start_time = datetime.strptime(self.start_date_var.get(), "%Y-%m-%d")
                            end_time = datetime.strptime(self.end_date_var.get(), "%Y-%m-%d")
                            # 将结束时间设置为当天的23:59:59
                            end_time = end_time.replace(hour=23, minute=59, second=59)
                            time_desc = f"{self.start_date_var.get()} 到 {self.end_date_var.get()}"
                            
                            # 验证日期范围
                            if start_time >= end_time:
                                self.parent.after(0, lambda: messagebox.showerror(
                                    "日期错误", "开始日期必须早于结束日期"))
                                return
                                
                            # 检查是否超过合理范围（比如1年）
                            if (end_time - start_time).days > 365:
                                result = messagebox.askyesno(
                                    "日期范围较大", 
                                    f"选择的日期范围为 {(end_time - start_time).days} 天，"
                                    "可能会处理大量消息，确定要继续吗？"
                                )
                                if not result:
                                    return
                                    
                        except ValueError as e:
                            self.parent.after(0, lambda: messagebox.showerror(
                                "日期格式错误", 
                                "请使用正确的日期格式 (YYYY-MM-DD)\n"
                                f"错误详情: {str(e)}"
                            ))
                            return
                    
                    self.parent.after(0, lambda: self._add_status_message(
                        f"开始分析群组: {group.name}\n"
                        f"时间范围: {time_desc}\n"
                        f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
                        f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}"
                    ))
                    
                    # 执行分析
                    task = loop.run_until_complete(
                        self.analysis_service.start_analysis(
                            chat_id=group.id,
                            start_time=start_time,
                            end_time=end_time
                        )
                    )
                    
                    self.current_task = task
                    
                    # 分析完成
                    self.parent.after(0, self._analysis_completed, task)
                    
                finally:
                    loop.close()
                    
            except Exception as e:
                logger.error(f"分析任务执行失败: {e}")
                self.parent.after(0, self._analysis_failed, str(e))
        
        # 在后台线程执行
        thread = threading.Thread(target=_analysis_worker, daemon=True)
        thread.start()
    
    def _analysis_completed(self, task):
        """分析任务完成"""
        try:
            self._add_status_message("分析任务完成！")
            self._add_status_message(f"任务ID: {task.id}")
            self._add_status_message(f"状态: {task.status}")
            self._add_status_message(f"总消息数: {task.total_messages}")
            self._add_status_message(f"发现自我介绍: {task.found_introductions}")
            self._add_status_message(f"提取用户画像: {task.extracted_profiles}")
            self._add_status_message(f"同步到飞书: {task.synced_to_feishu}")
            
            self.progress_var.set(100)
            self.status_var.set("分析完成")
            
            if task.extracted_profiles > 0:
                messagebox.showinfo("分析完成", 
                    f"分析任务成功完成！\n"
                    f"共处理 {task.total_messages} 条消息\n"
                    f"发现 {task.found_introductions} 条自我介绍\n"
                    f"提取 {task.extracted_profiles} 个用户画像")
            else:
                messagebox.showinfo("分析完成", 
                    f"分析任务完成，但未发现自我介绍\n"
                    f"共处理 {task.total_messages} 条消息")
            
        except Exception as e:
            logger.error(f"处理分析完成事件失败: {e}")
        finally:
            self._reset_ui_state()
    
    def _analysis_failed(self, error_message):
        """分析任务失败"""
        try:
            self._add_status_message(f"❌ 分析任务失败: {error_message}")
            self.status_var.set("分析失败")
            messagebox.showerror("分析失败", f"分析任务失败:\n{error_message}")
        finally:
            self._reset_ui_state()
    
    def _stop_analysis(self):
        """停止分析"""
        try:
            if self.analysis_service.cancel_analysis():
                self._add_status_message("分析任务已取消")
                self.status_var.set("已取消")
            else:
                self._add_status_message("无法取消分析任务")
            
        except Exception as e:
            logger.error(f"停止分析失败: {e}")
            messagebox.showerror("错误", f"停止分析失败:\n{str(e)}")
        finally:
            self._reset_ui_state()
    
    def _reset_ui_state(self):
        """重置UI状态"""
        self.is_running = False
        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.group_combo.config(state="readonly")
        
        # 关闭进度窗口
        if hasattr(self, 'progress_window'):
            try:
                self.progress_window.destroy()
                delattr(self, 'progress_window')
            except:
                pass
        
        # 移除显示进度按钮
        if hasattr(self, 'show_progress_btn'):
            try:
                self.show_progress_btn.destroy()
                delattr(self, 'show_progress_btn')
            except:
                pass
    
    def _add_status_message(self, message: str):
        """添加状态消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"
        
        self.status_text.config(state="normal")
        self.status_text.insert(tk.END, formatted_message)
        self.status_text.see(tk.END)
        self.status_text.config(state="disabled")
    
    def on_progress_update(self, event: str, data: Dict[str, Any]):
        """处理进度更新事件"""
        try:
            # 更新主界面进度
            if event == "step_started":
                step_name = data.get('step', '')
                self.status_var.set(f"🔄 {step_name}")
                self._add_status_message(f"开始阶段: {step_name}")
                
                # 更新详细进度窗口
                if hasattr(self, 'analysis_stage_var'):
                    self.analysis_stage_var.set(f"🔄 {step_name}")
                    self._add_analysis_log(f"开始阶段: {step_name}")
                
            elif event == "messages_loaded":
                count = data.get('count', 0)
                self.status_var.set(f"📨 已加载 {count} 条消息")
                self._add_status_message(f"消息加载完成: {count} 条")
                
                # 更新详细进度窗口
                if hasattr(self, 'processed_messages_var'):
                    self.processed_messages_var.set(f"0/{count}")
                    self._add_analysis_log(f"消息加载完成: {count} 条")
                
            elif event == "detection_progress":
                processed = data.get('processed', 0)
                total = data.get('total', 0)
                found = data.get('found', 0)
                speed = data.get('speed', 0)
                
                progress = (processed / total) * 40 if total > 0 else 0  # 检测阶段占40%
                self.progress_var.set(progress)
                self.status_var.set(f"🔍 检测进度: {processed}/{total} (发现 {found} 个)")
                
                # 更新详细进度窗口
                if hasattr(self, 'processed_messages_var'):
                    self.processed_messages_var.set(f"{processed}/{total}")
                    self.found_intros_var.set(str(found))
                    self.avg_speed_var.set(f"{speed:.1f}/秒")
                    self.overall_progress_var.set(progress)
                    self.stage_progress_var.set((processed / total) * 100 if total > 0 else 0)
                    
                    if processed % 10 == 0 or processed == total:  # 每10个或完成时记录日志
                        self._add_analysis_log(f"检测进度: {processed}/{total}, 发现 {found} 个自我介绍")
                
            elif event == "detection_completed":
                found = data.get('found', 0)
                total = data.get('total', 0)
                processing_time = data.get('processing_time', 0)
                
                self.progress_var.set(40)
                self.status_var.set(f"✅ 检测完成: {found}/{total}")
                self._add_status_message(f"检测阶段完成: 发现 {found} 个自我介绍，耗时 {processing_time:.1f}秒")
                
                # 更新详细进度窗口
                if hasattr(self, 'analysis_stage_var'):
                    self.analysis_stage_var.set("✅ 检测完成，开始提取...")
                    self.overall_progress_var.set(40)
                    self.stage_progress_var.set(100)
                    self._add_analysis_log(f"✅ 检测完成: 发现 {found}/{total} 个自我介绍，耗时 {processing_time:.1f}秒")
                
            elif event == "extraction_progress":
                processed = data.get('processed', 0)
                total = data.get('total', 0)
                speed = data.get('speed', 0)
                
                progress = 40 + (processed / total) * 40 if total > 0 else 40  # 提取阶段占40%
                self.progress_var.set(progress)
                self.status_var.set(f"📊 提取进度: {processed}/{total}")
                
                # 更新详细进度窗口
                if hasattr(self, 'analysis_stage_var'):
                    self.analysis_stage_var.set(f"📊 正在提取用户画像: {processed}/{total}")
                    self.overall_progress_var.set(progress)
                    self.stage_progress_var.set((processed / total) * 100 if total > 0 else 0)
                    self.avg_speed_var.set(f"{speed:.1f}/秒")
                    
                    if processed % 5 == 0 or processed == total:  # 每5个或完成时记录日志
                        self._add_analysis_log(f"提取进度: {processed}/{total}")
                
            elif event == "extraction_completed":
                extracted = data.get('extracted', 0)
                processing_time = data.get('processing_time', 0)
                
                self.progress_var.set(80)
                self.status_var.set(f"✅ 提取完成: {extracted} 个")
                self._add_status_message(f"提取阶段完成: {extracted} 个用户画像，耗时 {processing_time:.1f}秒")
                
                # 更新详细进度窗口
                if hasattr(self, 'analysis_stage_var'):
                    self.analysis_stage_var.set("✅ 提取完成，准备保存...")
                    self.overall_progress_var.set(80)
                    self.stage_progress_var.set(100)
                    self._add_analysis_log(f"✅ 提取完成: {extracted} 个用户画像，耗时 {processing_time:.1f}秒")
                
            elif event == "sync_progress":
                processed = data.get('processed', 0)
                total = data.get('total', 0)
                synced = data.get('synced', 0)
                progress = 80 + (processed / total) * 20 if total > 0 else 80  # 同步阶段占20%
                self.progress_var.set(progress)
                self.status_var.set(f"📊 同步进度: {processed}/{total} (已同步 {synced} 个)")
                
                # 更新详细进度窗口
                if hasattr(self, 'analysis_stage_var'):
                    self.analysis_stage_var.set(f"📊 正在同步到飞书: {processed}/{total}")
                    self.overall_progress_var.set(progress)
                    self.stage_progress_var.set((processed / total) * 100 if total > 0 else 0)
                    
                    if processed % 5 == 0 or processed == total:
                        self._add_analysis_log(f"同步进度: {processed}/{total}, 已同步 {synced} 个")
                
            elif event == "analysis_completed":
                self.progress_var.set(100)
                results = data.get('results', {})
                ai_stats = data.get('ai_stats', {})
                
                status_text = f"""✅ 分析完成！
总消息数: {results.get('total_messages', 0)}
发现自我介绍: {results.get('found_introductions', 0)}
提取用户画像: {results.get('extracted_profiles', 0)}
同步到飞书: {results.get('synced_to_feishu', 0)}

AI分析统计:
总请求: {ai_stats.get('total_requests', 0)}
成功率: {ai_stats.get('success_rate', 0):.1%}
平均耗时: {ai_stats.get('avg_processing_time_ms', 0):.1f}ms"""
                
                self.status_var.set("✅ 分析完成")
                self._add_status_message(status_text)
                
                # 更新详细进度窗口
                if hasattr(self, 'analysis_stage_var'):
                    self.analysis_stage_var.set("✅ 分析任务全部完成")
                    self.overall_progress_var.set(100)
                    self.stage_progress_var.set(100)
                    self._add_analysis_log("✅ 分析任务全部完成")
                    
                    # 更新按钮状态
                    if hasattr(self, 'analysis_cancel_btn'):
                        self.analysis_cancel_btn.config(text="关闭", command=self._close_progress_window)
                        self.analysis_close_btn.config(text="查看结果", command=self._view_results)
                
                # 刷新AI统计信息
                self._refresh_ai_stats()
                
            elif event == "analysis_cancelled":
                self.status_var.set("❌ 分析已取消")
                self._add_status_message("❌ 用户取消了分析任务")
                ai_stats = data.get('ai_stats', {})
                if ai_stats.get('total_requests', 0) > 0:
                    self._add_status_message(f"AI请求统计: {ai_stats.get('total_requests', 0)} 次, 成功率: {ai_stats.get('success_rate', 0):.1%}")
                
                # 更新详细进度窗口
                if hasattr(self, 'analysis_stage_var'):
                    self.analysis_stage_var.set("❌ 分析已取消")
                    self._add_analysis_log("❌ 用户取消了分析任务")
                    
                    # 更新按钮状态
                    if hasattr(self, 'analysis_cancel_btn'):
                        self.analysis_cancel_btn.config(text="关闭", command=self._close_progress_window)
            
            # 更新AI统计显示
            if event in ["detection_progress", "extraction_progress"]:
                ai_stats = data.get('ai_stats', {})
                if hasattr(self, 'ai_requests_var') and ai_stats:
                    self.ai_requests_var.set(str(ai_stats.get('total_requests', 0)))
            
            # 在重要事件时更新客户端信息显示
            if event in ["step_started", "analysis_completed", "analysis_cancelled"]:
                self._update_client_info_display()
                
        except Exception as e:
            logger.error(f"处理进度更新失败: {e}")
    
    def _view_results(self):
        """查看分析结果"""
        # 关闭进度窗口
        self._close_progress_window()
        
        # 切换到结果标签页
        if hasattr(self.parent, 'select'):
            # 假设结果标签页是第3个标签页（索引为2）
            self.parent.select(2)
        
        # 发送事件通知结果页面刷新
        if hasattr(self, 'analysis_service'):
            try:
                self.analysis_service.notify_progress("view_results", {"source": "analysis_completed"})
            except:
                pass

    def get_current_task(self):
        """获取当前任务"""
        return self.current_task 

    def _update_client_info_display(self):
        """更新客户端信息显示"""
        try:
            client_info = self.analysis_service.get_deepseek_client_info()
            client_type = client_info['type']
            status = "✅" if client_info['is_available'] else "❌"
            display_text = f"{status} {client_type}"
            self.client_info_var.set(display_text)
            
            # 根据客户端类型设置不同的样式
            if client_info['use_openai_like']:
                self.client_info_label.config(bootstyle="warning")
            else:
                self.client_info_label.config(bootstyle="info")
                
        except Exception as e:
            logger.warning(f"更新客户端信息显示失败: {e}")
            self.client_info_var.set("❓ 状态未知")

    def _on_time_mode_changed(self):
        """处理时间模式变化"""
        if self.time_mode_var.get() == "days":
            self.days_frame.pack(fill=X, expand=True)
            self.date_range_frame.pack_forget()
        elif self.time_mode_var.get() == "date_range":
            self.days_frame.pack_forget()
            self.date_range_frame.pack(fill=X, expand=True)

    def _set_quick_date(self, days):
        """设置快速日期"""
        if self.time_mode_var.get() == "date_range":
            end_date = datetime.now()
            if days == 0:
                # 今天：从今天开始到今天结束
                start_date = end_date
            else:
                # 指定天数：从今天往前推指定天数
                start_date = end_date - timedelta(days=days)
            
            self.start_date_var.set(start_date.strftime("%Y-%m-%d"))
            self.end_date_var.set(end_date.strftime("%Y-%m-%d"))
        else:
            # 天数模式
            if days == 0:
                self.days_var.set(1)  # 今天设置为1天
            else:
                self.days_var.set(days) 

    def _validate_start_date(self, event):
        """验证开始日期"""
        try:
            start_date = datetime.strptime(self.start_date_var.get(), "%Y-%m-%d")
            end_date = datetime.strptime(self.end_date_var.get(), "%Y-%m-%d")
            if start_date > end_date:
                messagebox.showerror("日期错误", "开始日期必须早于结束日期")
                self.start_date_var.set(start_date.strftime("%Y-%m-%d"))
        except ValueError:
            messagebox.showerror("日期格式错误", "请使用正确的日期格式 (YYYY-MM-DD)")

    def _validate_end_date(self, event):
        """验证结束日期"""
        try:
            start_date = datetime.strptime(self.start_date_var.get(), "%Y-%m-%d")
            end_date = datetime.strptime(self.end_date_var.get(), "%Y-%m-%d")
            if start_date > end_date:
                messagebox.showerror("日期错误", "开始日期必须早于结束日期")
                self.end_date_var.set(end_date.strftime("%Y-%m-%d"))
        except ValueError:
            messagebox.showerror("日期格式错误", "请使用正确的日期格式 (YYYY-MM-DD)")

    def _on_group_search(self, event):
        """处理群组搜索"""
        search_text = self.group_search_var.get().lower()
        
        # 如果是占位符文本，忽略搜索
        if search_text == "搜索群组...":
            return
            
        if hasattr(self, 'all_group_data'):
            # 筛选群组
            filtered_options = []
            filtered_data = {}
            
            for display_text, group in self.all_group_data.items():
                if search_text in group.name.lower():
                    filtered_options.append(display_text)
                    filtered_data[display_text] = group
            
            # 更新下拉框选项
            self.group_combo['values'] = filtered_options
            self.group_data = filtered_data
            
            # 如果有匹配项，自动选择第一个
            if filtered_options:
                self.group_combo.set(filtered_options[0])
            else:
                self.group_combo.set("")

    def _on_group_select(self, event):
        """处理群组选择"""
        selected_group_text = self.group_combo.get()
        if selected_group_text:
            selected_group = self.group_data[selected_group_text]
            self.group_var.set(selected_group_text)
            self.group_search_var.set(selected_group.name)

    def _on_group_refresh(self):
        """处理刷新群组按钮"""
        self.refresh_groups() 

    def _clear_group_search(self):
        """处理清空搜索按钮"""
        # 清空搜索并设置占位符
        self.group_search_var.set("搜索群组...")
        self.group_search_entry.config(foreground='gray')
        
        # 恢复显示所有群组
        if hasattr(self, 'all_group_data'):
            self.group_data = self.all_group_data.copy()
            group_options = list(self.all_group_data.keys())
            self.group_combo['values'] = group_options
            
            # 如果有群组，选择第一个
            if group_options:
                self.group_combo.set(group_options[0])
            else:
                self.group_combo.set("")
        
        # 设置焦点到搜索框
        self.group_search_entry.focus()
    
    def _setup_search_placeholder(self):
        """设置搜索框占位符"""
        placeholder_text = "搜索群组..."
        
        # 设置初始占位符
        self.group_search_var.set(placeholder_text)
        self.group_search_entry.config(foreground='gray')
        
        def on_focus_in(event):
            if self.group_search_var.get() == placeholder_text:
                self.group_search_var.set("")
                self.group_search_entry.config(foreground='black')
        
        def on_focus_out(event):
            if not self.group_search_var.get():
                self.group_search_var.set(placeholder_text)
                self.group_search_entry.config(foreground='gray')
        
        self.group_search_entry.bind('<FocusIn>', on_focus_in)
        self.group_search_entry.bind('<FocusOut>', on_focus_out) 