"""
配置模块测试
"""

import pytest
import tempfile
import os
from pathlib import Path

# 导入要测试的模块
import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.config import AppConfig


class TestAppConfig:
    """测试AppConfig类"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = AppConfig()
        
        assert config.deepseek_api_key == ""
        assert config.chatlog_url == "http://127.0.0.1:5030"
        assert config.llm_temperature == 0.1
        assert config.default_analysis_days == 7
        assert config.min_confidence_threshold == 0.7
    
    def test_config_validation(self):
        """测试配置验证"""
        config = AppConfig()
        
        # 测试空配置的验证错误
        errors = config.validate()
        assert "DeepSeek API密钥不能为空" in errors
        assert "Chatlog API地址不能为空" not in errors  # 有默认值
        
        # 测试温度参数验证
        config.llm_temperature = 3.0  # 超出范围
        errors = config.validate()
        assert any("温度参数" in error for error in errors)
        
        # 测试置信度参数验证
        config.llm_temperature = 0.5  # 恢复正常
        config.min_confidence_threshold = 1.5  # 超出范围
        errors = config.validate()
        assert any("置信度阈值" in error for error in errors)
    
    def test_config_save_load(self):
        """测试配置保存和加载"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config_file = f.name
        
        try:
            # 创建配置并保存
            config1 = AppConfig(
                deepseek_api_key="test_key",
                chatlog_url="http://test.com",
                llm_temperature=0.3,
                default_analysis_days=14
            )
            config1.save_to_file(config_file)
            
            # 加载配置
            config2 = AppConfig.load_from_file(config_file)
            
            # 验证加载的配置
            assert config2.deepseek_api_key == "test_key"
            assert config2.chatlog_url == "http://test.com"
            assert config2.llm_temperature == 0.3
            assert config2.default_analysis_days == 14
            
        finally:
            # 清理临时文件
            if os.path.exists(config_file):
                os.unlink(config_file)
    
    def test_config_load_nonexistent_file(self):
        """测试加载不存在的配置文件"""
        config = AppConfig.load_from_file("nonexistent.json")
        
        # 应该返回默认配置
        assert isinstance(config, AppConfig)
        assert config.deepseek_api_key == ""
        assert config.chatlog_url == "http://127.0.0.1:5030"
    
    def test_llm_config(self):
        """测试LLM配置获取"""
        config = AppConfig(
            deepseek_api_key="test_key",
            llm_model="test_model",
            llm_temperature=0.2,
            llm_max_tokens=1000
        )
        
        llm_config = config.get_llm_config()
        
        assert llm_config["api_key"] == "test_key"
        assert llm_config["model"] == "test_model"
        assert llm_config["temperature"] == 0.2
        assert llm_config["max_tokens"] == 1000


if __name__ == "__main__":
    pytest.main([__file__]) 