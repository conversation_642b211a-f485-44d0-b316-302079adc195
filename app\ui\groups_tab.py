"""
群组管理标签页
显示和管理微信群组
"""

import tkinter as tk
from tkinter import messagebox
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import asyncio
import threading
import logging
from typing import List

logger = logging.getLogger(__name__)

class GroupsTab:
    """群组管理标签页"""
    
    def __init__(self, parent, analysis_service):
        self.parent = parent
        self.analysis_service = analysis_service
        
        # 创建主框架
        self.frame = ttk.Frame(parent)
        
        # 群组列表
        self.groups = []
        
        # 状态标签
        self.status_var = tk.StringVar(value="准备就绪")
        
        # 创建界面
        self._create_widgets()
        
        logger.debug("群组标签页初始化完成")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 工具栏
        toolbar = ttk.Frame(self.frame)
        toolbar.pack(fill=X, padx=10, pady=5)
        
        self.refresh_button = ttk.Button(
            toolbar,
            text="🔄 刷新群组",
            command=self.refresh_groups,
            bootstyle="primary"
        )
        self.refresh_button.pack(side=LEFT, padx=(0, 10))
        
        # 状态标签
        self.status_label = ttk.Label(
            toolbar, 
            textvariable=self.status_var,
            bootstyle="info"
        )
        self.status_label.pack(side=LEFT, padx=(10, 0))
        
        ttk.Label(toolbar, text="群组列表:").pack(side=LEFT, padx=(20, 0))
        
        # 群组列表
        list_frame = ttk.Frame(self.frame)
        list_frame.pack(fill=BOTH, expand=True, padx=10, pady=5)
        
        # 创建Treeview
        columns = ("ID", "名称", "成员数", "状态", "描述")
        self.tree = ttk.Treeview(list_frame, columns=columns, show="tree headings")
        
        # 设置列
        self.tree.heading("#0", text="")
        self.tree.column("#0", width=0, stretch=False)
        
        # 设置列宽和标题
        column_configs = {
            "ID": {"width": 200, "anchor": "w"},
            "名称": {"width": 180, "anchor": "w"}, 
            "成员数": {"width": 80, "anchor": "center"},
            "状态": {"width": 80, "anchor": "center"},
            "描述": {"width": 200, "anchor": "w"}
        }
        
        for col in columns:
            config = column_configs.get(col, {"width": 150, "anchor": "center"})
            self.tree.heading(col, text=col)
            self.tree.column(col, width=config["width"], anchor=config["anchor"])
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # 双击事件
        self.tree.bind("<Double-1>", self.on_group_double_click)
    
    def refresh_groups(self):
        """刷新群组列表"""
        # 禁用刷新按钮，防止重复点击
        self.refresh_button.config(state="disabled")
        self.status_var.set("正在获取群组列表...")
        
        # 在后台线程中执行异步操作
        thread = threading.Thread(target=self._refresh_groups_async, daemon=True)
        thread.start()
    
    def _refresh_groups_async(self):
        """在后台线程中刷新群组列表"""
        try:
            # 创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # 异步获取群组列表
                groups = loop.run_until_complete(self.analysis_service.get_chat_groups())
                
                # 在主线程中更新UI
                self.parent.after(0, self._update_groups_ui, groups)
                
            finally:
                loop.close()
                
        except Exception as e:
            logger.error(f"获取群组列表失败: {e}")
            # 在主线程中显示错误
            self.parent.after(0, self._show_error, f"获取群组列表失败: {str(e)}")
    
    def _update_groups_ui(self, groups):
        """在主线程中更新群组列表UI"""
        try:
            # 清空现有列表
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # 添加群组数据
            self.groups = groups
            for group in groups:
                # 确定状态
                status = "活跃" if group.is_active else "不活跃"
                
                # 插入到树形控件
                self.tree.insert("", "end", values=(
                    group.id,
                    group.name,
                    group.member_count,
                    status,
                    group.description or "无描述"
                ))
            
            # 更新状态
            self.status_var.set(f"已加载 {len(groups)} 个群组")
            logger.info(f"群组列表刷新完成，共 {len(groups)} 个群组")
            
        except Exception as e:
            logger.error(f"更新群组UI失败: {e}")
            self.status_var.set("更新界面失败")
        
        finally:
            # 重新启用刷新按钮
            self.refresh_button.config(state="normal")
    
    def _show_error(self, error_message):
        """在主线程中显示错误消息"""
        self.status_var.set("获取群组失败")
        self.refresh_button.config(state="normal")
        messagebox.showerror("错误", error_message)
    
    def on_group_double_click(self, event):
        """群组双击事件处理"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            group_id = item['values'][0]
            group_name = item['values'][1]
            
            # 可以在这里添加群组详情查看或选择逻辑
            messagebox.showinfo("群组信息", f"群组ID: {group_id}\n群组名称: {group_name}")
    
    def get_selected_group(self):
        """获取当前选中的群组"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            group_id = item['values'][0]
            
            # 从群组列表中找到对应的群组对象
            for group in self.groups:
                if group.id == group_id:
                    return group
        return None
    
    def get_all_groups(self):
        """获取所有群组"""
        return self.groups 