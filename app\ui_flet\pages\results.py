"""
分析结果页面
显示和管理分析结果
"""

import flet as ft
from typing import TYPE_CHECKING, List, Dict, Any
import asyncio

if TYPE_CHECKING:
    from ..main_app import FletMainApp

class ResultsPage:
    """分析结果页面"""
    
    def __init__(self, app: 'FletMainApp'):
        self.app = app
        self.results_data = []
        self.filtered_results = []
        self.search_query = ""
        self.filter_confidence = 0.0
        
        # UI组件引用
        self.search_field = None
        self.confidence_filter = None
        self.results_table = None
        self.stats_cards = None
    
    def build(self) -> ft.Column:
        """构建分析结果页面"""
        
        # 顶部统计卡片
        stats_section = self._create_stats_section()
        
        # 过滤和搜索栏
        filter_section = self._create_filter_section()
        
        # 结果表格
        table_section = self._create_table_section()
        
        # 操作按钮栏
        action_section = self._create_action_section()
        
        # 加载模拟数据
        asyncio.create_task(self._load_results())
        
        return ft.Column(
            controls=[
                stats_section,
                ft.Container(height=20),
                filter_section,
                ft.Container(height=20),
                table_section,
                ft.Container(height=20),
                action_section
            ],
            spacing=0,
            expand=True
        )
    
    def _create_stats_section(self) -> ft.Row:
        """创建统计区域"""
        
        self.stats_cards = ft.Row(
            controls=[
                self._create_stat_card("总档案数", "24", "个用户", ft.icons.PEOPLE, ft.colors.BLUE),
                self._create_stat_card("高置信度", "18", "≥0.8", ft.icons.VERIFIED, ft.colors.GREEN),
                self._create_stat_card("平均置信度", "0.85", "准确率", ft.icons.ANALYTICS, ft.colors.ORANGE),
                self._create_stat_card("同步状态", "100%", "已同步", ft.icons.CLOUD_DONE, ft.colors.PURPLE)
            ],
            spacing=20,
            wrap=True
        )
        
        return self.stats_cards
    
    def _create_stat_card(self, title: str, value: str, subtitle: str, icon: ft.icons, color: str) -> ft.Container:
        """创建统计卡片"""
        return ft.Container(
            content=ft.Column(
                controls=[
                    ft.Row(
                        controls=[
                            ft.Icon(icon, size=24, color=color),
                            ft.Container(expand=True)
                        ]
                    ),
                    ft.Container(height=10),
                    ft.Text(
                        value,
                        size=24,
                        weight=ft.FontWeight.BOLD,
                        color=ft.colors.GREY_800
                    ),
                    ft.Text(
                        title,
                        size=14,
                        color=ft.colors.GREY_600,
                        weight=ft.FontWeight.W500
                    ),
                    ft.Text(
                        subtitle,
                        size=12,
                        color=ft.colors.GREY_500
                    )
                ],
                spacing=5,
                horizontal_alignment=ft.CrossAxisAlignment.START
            ),
            padding=ft.padding.all(20),
            bgcolor=ft.colors.WHITE,
            border_radius=12,
            width=250,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.colors.BLACK12,
                offset=ft.Offset(0, 4)
            )
        )
    
    def _create_filter_section(self) -> ft.Container:
        """创建过滤区域"""
        
        # 搜索框
        self.search_field = ft.TextField(
            hint_text="搜索用户昵称、职业或技能...",
            prefix_icon=ft.icons.SEARCH,
            border_radius=25,
            bgcolor=ft.colors.GREY_100,
            border_color=ft.colors.TRANSPARENT,
            focused_border_color=ft.colors.BLUE,
            width=400,
            height=45,
            on_change=self._on_search_change
        )
        
        # 置信度过滤器
        self.confidence_filter = ft.Slider(
            min=0.0,
            max=1.0,
            value=self.filter_confidence,
            divisions=10,
            label="≥{value}",
            on_change=self._on_confidence_filter_change
        )
        
        # 排序选择
        sort_dropdown = ft.Dropdown(
            hint_text="排序方式",
            options=[
                ft.dropdown.Option("confidence_desc", "置信度 (高到低)"),
                ft.dropdown.Option("confidence_asc", "置信度 (低到高)"),
                ft.dropdown.Option("name_asc", "姓名 (A-Z)"),
                ft.dropdown.Option("profession_asc", "职业 (A-Z)")
            ],
            value="confidence_desc",
            width=200,
            on_change=self._on_sort_change
        )
        
        return ft.Container(
            content=ft.Row(
                controls=[
                    self.search_field,
                    ft.Column(
                        controls=[
                            ft.Text(f"置信度过滤: ≥{self.filter_confidence:.1f}", size=12, color=ft.colors.GREY_600),
                            ft.Container(
                                content=self.confidence_filter,
                                width=200
                            )
                        ],
                        spacing=5
                    ),
                    sort_dropdown,
                    ft.ElevatedButton(
                        text="重置过滤",
                        icon=ft.icons.CLEAR,
                        on_click=self._on_reset_filters,
                        style=ft.ButtonStyle(
                            bgcolor=ft.colors.GREY_400,
                            color=ft.colors.WHITE
                        )
                    )
                ],
                alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                vertical_alignment=ft.CrossAxisAlignment.CENTER
            ),
            padding=ft.padding.all(20),
            bgcolor=ft.colors.WHITE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.colors.BLACK12,
                offset=ft.Offset(0, 4)
            )
        )
    
    def _create_table_section(self) -> ft.Container:
        """创建表格区域"""
        
        # 表格标题行
        header_row = ft.Row(
            controls=[
                ft.Container(ft.Text("昵称", weight=ft.FontWeight.BOLD), width=120),
                ft.Container(ft.Text("职业", weight=ft.FontWeight.BOLD), width=150),
                ft.Container(ft.Text("个人介绍", weight=ft.FontWeight.BOLD), expand=True),
                ft.Container(ft.Text("能提供", weight=ft.FontWeight.BOLD), width=120),
                ft.Container(ft.Text("希望得到", weight=ft.FontWeight.BOLD), width=120),
                ft.Container(ft.Text("置信度", weight=ft.FontWeight.BOLD), width=80),
                ft.Container(ft.Text("操作", weight=ft.FontWeight.BOLD), width=100)
            ],
            spacing=10
        )
        
        # 表格内容
        self.results_table = ft.Column(
            controls=[],
            spacing=5,
            scroll=ft.ScrollMode.AUTO
        )
        
        return ft.Container(
            content=ft.Column(
                controls=[
                    header_row,
                    ft.Divider(height=1, color=ft.colors.GREY_300),
                    ft.Container(
                        content=self.results_table,
                        height=400,
                        padding=ft.padding.symmetric(vertical=10)
                    )
                ],
                spacing=10
            ),
            padding=ft.padding.all(20),
            bgcolor=ft.colors.WHITE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.colors.BLACK12,
                offset=ft.Offset(0, 4)
            )
        )
    
    def _create_action_section(self) -> ft.Container:
        """创建操作区域"""
        
        return ft.Container(
            content=ft.Row(
                controls=[
                    ft.Text(
                        f"显示 {len(self.filtered_results)} / {len(self.results_data)} 条记录",
                        size=14,
                        color=ft.colors.GREY_600
                    ),
                    ft.Row(
                        controls=[
                            ft.ElevatedButton(
                                text="导出Excel",
                                icon=ft.icons.TABLE_VIEW,
                                on_click=self._on_export_excel,
                                style=ft.ButtonStyle(
                                    bgcolor=ft.colors.GREEN,
                                    color=ft.colors.WHITE
                                )
                            ),
                            ft.ElevatedButton(
                                text="导出JSON",
                                icon=ft.icons.CODE,
                                on_click=self._on_export_json,
                                style=ft.ButtonStyle(
                                    bgcolor=ft.colors.BLUE,
                                    color=ft.colors.WHITE
                                )
                            ),
                            ft.ElevatedButton(
                                text="同步飞书",
                                icon=ft.icons.CLOUD_UPLOAD,
                                on_click=self._on_sync_feishu,
                                style=ft.ButtonStyle(
                                    bgcolor=ft.colors.PURPLE,
                                    color=ft.colors.WHITE
                                )
                            ),
                            ft.OutlinedButton(
                                text="刷新数据",
                                icon=ft.icons.REFRESH,
                                on_click=self._on_refresh_data
                            )
                        ],
                        spacing=10
                    )
                ],
                alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                vertical_alignment=ft.CrossAxisAlignment.CENTER
            ),
            padding=ft.padding.all(20),
            bgcolor=ft.colors.WHITE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.colors.BLACK12,
                offset=ft.Offset(0, 4)
            )
        )

    def _create_result_row(self, result: Dict[str, Any]) -> ft.Container:
        """创建结果行"""

        # 置信度颜色
        confidence = result.get('confidence', 0.0)
        if confidence >= 0.8:
            confidence_color = ft.colors.GREEN
        elif confidence >= 0.6:
            confidence_color = ft.colors.ORANGE
        else:
            confidence_color = ft.colors.RED

        def on_view_detail(e):
            self._show_detail_dialog(result)

        def on_edit_result(e):
            self._show_edit_dialog(result)

        return ft.Container(
            content=ft.Row(
                controls=[
                    ft.Container(
                        ft.Text(result.get('nickname', ''), size=12, weight=ft.FontWeight.W500),
                        width=120
                    ),
                    ft.Container(
                        ft.Text(result.get('profession', ''), size=12),
                        width=150
                    ),
                    ft.Container(
                        ft.Text(
                            result.get('personal_intro', '')[:50] + ('...' if len(result.get('personal_intro', '')) > 50 else ''),
                            size=12,
                            color=ft.colors.GREY_700
                        ),
                        expand=True
                    ),
                    ft.Container(
                        ft.Text(result.get('can_provide', '')[:20] + ('...' if len(result.get('can_provide', '')) > 20 else ''), size=12),
                        width=120
                    ),
                    ft.Container(
                        ft.Text(result.get('looking_for', '')[:20] + ('...' if len(result.get('looking_for', '')) > 20 else ''), size=12),
                        width=120
                    ),
                    ft.Container(
                        ft.Container(
                            content=ft.Text(f"{confidence:.2f}", size=11, color=ft.colors.WHITE, weight=ft.FontWeight.BOLD),
                            bgcolor=confidence_color,
                            padding=ft.padding.symmetric(horizontal=8, vertical=4),
                            border_radius=12
                        ),
                        width=80
                    ),
                    ft.Container(
                        ft.Row(
                            controls=[
                                ft.IconButton(
                                    icon=ft.icons.VISIBILITY,
                                    tooltip="查看详情",
                                    on_click=on_view_detail,
                                    icon_size=16,
                                    icon_color=ft.colors.BLUE
                                ),
                                ft.IconButton(
                                    icon=ft.icons.EDIT,
                                    tooltip="编辑",
                                    on_click=on_edit_result,
                                    icon_size=16,
                                    icon_color=ft.colors.ORANGE
                                )
                            ],
                            spacing=0
                        ),
                        width=100
                    )
                ],
                spacing=10,
                alignment=ft.MainAxisAlignment.START,
                vertical_alignment=ft.CrossAxisAlignment.CENTER
            ),
            padding=ft.padding.symmetric(horizontal=10, vertical=8),
            bgcolor=ft.colors.GREY_50,
            border_radius=8,
            border=ft.border.all(1, ft.colors.GREY_200)
        )

    async def _load_results(self):
        """加载结果数据"""
        # 模拟结果数据
        self.results_data = [
            {
                "id": "1",
                "nickname": "张三",
                "profession": "产品经理",
                "personal_intro": "5年互联网产品经验，专注于用户体验设计和产品策略",
                "can_provide": "产品设计咨询",
                "looking_for": "技术合作",
                "industry": "互联网",
                "confidence": 0.92
            },
            {
                "id": "2",
                "nickname": "李四",
                "profession": "前端工程师",
                "personal_intro": "React和Vue专家，有丰富的移动端开发经验",
                "can_provide": "前端技术支持",
                "looking_for": "项目合作机会",
                "industry": "软件开发",
                "confidence": 0.88
            },
            {
                "id": "3",
                "nickname": "王五",
                "profession": "UI设计师",
                "personal_intro": "专业UI/UX设计师，擅长移动应用和网页设计",
                "can_provide": "设计服务",
                "looking_for": "设计项目",
                "industry": "设计",
                "confidence": 0.85
            }
        ]

        await self._apply_filters()

    async def _apply_filters(self):
        """应用过滤条件"""
        self.filtered_results = []

        for result in self.results_data:
            # 置信度过滤
            if result.get('confidence', 0.0) < self.filter_confidence:
                continue

            # 搜索过滤
            if self.search_query:
                search_text = f"{result.get('nickname', '')} {result.get('profession', '')} {result.get('personal_intro', '')}".lower()
                if self.search_query.lower() not in search_text:
                    continue

            self.filtered_results.append(result)

        await self._update_table()

    async def _update_table(self):
        """更新表格显示"""
        self.results_table.controls.clear()

        if not self.filtered_results:
            # 显示空状态
            empty_state = ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Icon(ft.icons.SEARCH_OFF, size=64, color=ft.colors.GREY_400),
                        ft.Text("暂无匹配的结果", size=16, color=ft.colors.GREY_600),
                        ft.Text("尝试调整过滤条件或搜索关键词", size=14, color=ft.colors.GREY_500)
                    ],
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    spacing=10
                ),
                alignment=ft.alignment.center,
                height=200
            )
            self.results_table.controls.append(empty_state)
        else:
            # 添加结果行
            for result in self.filtered_results:
                self.results_table.controls.append(self._create_result_row(result))

        await self.app.page.update_async()

    def _show_detail_dialog(self, result: Dict[str, Any]):
        """显示详情对话框"""
        detail_content = f"""
昵称：{result.get('nickname', '')}
职业：{result.get('profession', '')}
个人介绍：{result.get('personal_intro', '')}
能够提供：{result.get('can_provide', '')}
希望得到：{result.get('looking_for', '')}
所在行业：{result.get('industry', '')}
置信度：{result.get('confidence', 0.0):.2f}
        """

        asyncio.create_task(
            self.app.show_message("用户档案详情", detail_content.strip(), "info")
        )

    def _show_edit_dialog(self, result: Dict[str, Any]):
        """显示编辑对话框"""
        asyncio.create_task(
            self.app.show_message("编辑功能", "编辑功能开发中...", "info")
        )

    async def _on_search_change(self, e):
        """搜索变化事件"""
        self.search_query = e.control.value
        await self._apply_filters()

    async def _on_confidence_filter_change(self, e):
        """置信度过滤变化事件"""
        self.filter_confidence = e.control.value
        await self._apply_filters()

    async def _on_sort_change(self, e):
        """排序变化事件"""
        sort_key = e.control.value

        if sort_key == "confidence_desc":
            self.filtered_results.sort(key=lambda x: x.get('confidence', 0.0), reverse=True)
        elif sort_key == "confidence_asc":
            self.filtered_results.sort(key=lambda x: x.get('confidence', 0.0))
        elif sort_key == "name_asc":
            self.filtered_results.sort(key=lambda x: x.get('nickname', ''))
        elif sort_key == "profession_asc":
            self.filtered_results.sort(key=lambda x: x.get('profession', ''))

        await self._update_table()

    async def _on_reset_filters(self, e):
        """重置过滤条件"""
        self.search_query = ""
        self.filter_confidence = 0.0

        if self.search_field:
            self.search_field.value = ""
        if self.confidence_filter:
            self.confidence_filter.value = 0.0

        await self._apply_filters()

    async def _on_export_excel(self, e):
        """导出Excel"""
        await self.app.show_message("导出Excel", "Excel导出功能开发中...", "info")

    async def _on_export_json(self, e):
        """导出JSON"""
        await self.app.show_message("导出JSON", "JSON导出功能开发中...", "info")

    async def _on_sync_feishu(self, e):
        """同步飞书"""
        await self.app.show_message("同步飞书", "飞书同步功能开发中...", "info")

    async def _on_refresh_data(self, e):
        """刷新数据"""
        await self._load_results()
        await self.app.show_message("刷新完成", "数据已刷新", "success")
