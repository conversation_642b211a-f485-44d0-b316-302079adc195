"""
UI启动选择器
让用户选择使用哪种UI界面
"""

import sys
import os
from pathlib import Path

def show_ui_selector():
    """显示UI选择器"""
    print("=" * 60)
    print("🤖 微信群自我介绍分析工具")
    print("=" * 60)
    print()
    print("请选择要使用的界面版本：")
    print()
    print("1. 🎨 现代化界面 (Flet) - 推荐")
    print("   • 现代化Material Design设计")
    print("   • 响应式布局，美观易用")
    print("   • 流畅的动画和交互效果")
    print()
    print("2. 🖥️ 经典界面 (tkinter)")
    print("   • 传统桌面应用风格")
    print("   • 稳定可靠，兼容性好")
    print("   • 资源占用较少")
    print()
    print("3. 🚪 退出程序")
    print()
    
    while True:
        try:
            choice = input("请输入选择 (1-3): ").strip()
            
            if choice == "1":
                print("\n🚀 启动现代化界面...")
                return "flet"
            elif choice == "2":
                print("\n🚀 启动经典界面...")
                return "tkinter"
            elif choice == "3":
                print("\n👋 再见！")
                sys.exit(0)
            else:
                print("❌ 无效选择，请输入 1、2 或 3")
                
        except KeyboardInterrupt:
            print("\n\n👋 再见！")
            sys.exit(0)
        except Exception as e:
            print(f"❌ 输入错误: {e}")

def check_dependencies(ui_type):
    """检查依赖"""
    print(f"🔍 检查 {ui_type} 界面依赖...")
    
    try:
        if ui_type == "flet":
            import flet
            print("✅ Flet 依赖检查通过")
        elif ui_type == "tkinter":
            import tkinter
            import ttkbootstrap
            print("✅ tkinter 依赖检查通过")
        
        # 检查通用依赖
        import requests
        print("✅ 通用依赖检查通过")
        
        return True
        
    except ImportError as e:
        print(f"❌ 依赖检查失败: {e}")
        print("\n📦 请安装缺失的依赖包：")
        print("pip install -r requirements.txt")
        return False

def launch_ui(ui_type):
    """启动指定的UI"""
    try:
        if ui_type == "flet":
            # 启动Flet界面
            from app.main_flet import main
            main()
        elif ui_type == "tkinter":
            # 启动tkinter界面
            from app.main import main
            main()
            
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n🔧 可能的解决方案：")
        print("1. 检查是否安装了所有依赖包")
        print("2. 确保Python版本 >= 3.8")
        print("3. 查看日志文件获取详细错误信息")
        sys.exit(1)

def main():
    """主函数"""
    try:
        # 显示选择器
        ui_type = show_ui_selector()
        
        # 检查依赖
        if not check_dependencies(ui_type):
            sys.exit(1)
        
        # 启动UI
        launch_ui(ui_type)
        
    except KeyboardInterrupt:
        print("\n\n👋 再见！")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
