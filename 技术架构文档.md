# 微信群用户档案自动分析器 - 技术架构文档

## 🏗️ 系统概述

### 1.1 架构原则
- **轻量化**：单文件部署，最小化依赖
- **模块化**：功能解耦，便于维护和扩展
- **用户友好**：图形化界面，零技术门槛
- **数据安全**：本地处理，保护隐私
- **可扩展性**：支持功能迭代和平台扩展

### 1.2 技术栈选型

| 组件 | 技术选择 | 选择理由 |
|------|----------|----------|
| **编程语言** | Python 3.8+ | 丰富的AI库，跨平台支持 |
| **图形界面** | tkinter + ttkbootstrap | 内置库，现代化外观 |
| **AI服务** | LlamaIndex + DeepSeek | 强大的文档处理，结构化输出，成本效益高 |
| **数据存储** | 飞书多维表格 | 用户友好，协作便利 |
| **数据源** | Chatlog HTTP API | 微信数据解密，成熟稳定 |
| **打包工具** | PyInstaller | 单文件打包，部署简便 |
| **HTTP客户端** | requests | 轻量稳定的HTTP库 |
| **异步处理** | threading | 防止界面卡顿 |

## 🏛️ 系统架构

### 2.1 整体架构图

```mermaid
graph TB
    subgraph "用户界面层"
        A[图形界面 - tkinter]
        B[配置管理界面]
        C[分析任务界面]
        D[结果展示界面]
    end
    
    subgraph "业务逻辑层"
        E[自我介绍识别器]
        F[信息提取器]
        G[任务调度器]
        H[数据处理器]
    end
    
    subgraph "数据访问层"
        I[Chatlog API客户端]
        J[DeepSeek API客户端]
        K[飞书API客户端]
        L[本地配置存储]
    end
    
    subgraph "外部系统"
        M[Chatlog服务]
        N[DeepSeek AI服务]
        O[飞书多维表格]
        P[微信本地数据]
    end
    
    A --> E
    B --> L
    C --> G
    D --> H
    
    E --> I
    F --> J
    G --> H
    H --> K
    
    I --> M
    J --> N
    K --> O
    M --> P
```

### 2.2 数据流架构

```mermaid
sequenceDiagram
    participant U as 用户界面
    participant S as 业务逻辑
    participant C as Chatlog API
    participant D as DeepSeek API
    participant F as 飞书API
    
    U->>S: 触发分析任务
    S->>C: 获取群聊消息
    C-->>S: 返回消息列表
    
    S->>S: 预筛选自我介绍
    
    loop 处理每条自我介绍
        S->>D: 识别确认
        D-->>S: 返回识别结果
        S->>D: 提取结构化信息
        D-->>S: 返回用户档案
        S->>F: 同步到飞书表格
        F-->>S: 返回同步状态
    end
    
    S-->>U: 返回分析结果
```

## 🧩 核心模块设计

### 3.1 用户界面模块 (UI Layer)

#### 3.1.1 主界面控制器
```python
class WeChatProfileAnalyzer:
    """主应用程序控制器"""
    
    def __init__(self):
        self.root = ttkb.Window()
        self.config_manager = ConfigManager()
        self.task_manager = TaskManager()
        
    def create_widgets(self):
        """创建界面组件"""
        pass
        
    def run(self):
        """启动应用程序"""
        pass
```

#### 3.1.2 标签页组件
- **配置标签页**：API密钥、飞书配置、Chatlog设置
- **群组标签页**：群组列表、选择和管理
- **分析标签页**：参数设置、任务执行、进度监控
- **结果标签页**：数据展示、导出功能

#### 3.1.3 界面特性
- 响应式布局，适配不同屏幕大小
- 现代化主题，支持深浅色切换
- 实时状态反馈和进度指示
- 友好的错误提示和帮助信息

### 3.2 业务逻辑模块 (Business Layer)

#### 3.2.1 自我介绍识别器
```python
from llama_index.core.llms import LLM
from llama_index.llms.deepseek import DeepSeek
from llama_index.core import PromptTemplate
from llama_index.core.output_parsers import PydanticOutputParser
from pydantic import BaseModel
from typing import List
import re

class IntroductionDetector:
    """自我介绍识别器"""
    
    def __init__(self, deepseek_api_key: str):
        # 初始化LlamaIndex LLM
        self.llm = DeepSeek(
            api_key=deepseek_api_key,
            model="deepseek-chat",
            temperature=0.1
        )
        
        # 定义识别结果模型
        class DetectionResult(BaseModel):
            is_introduction: bool
            confidence: float
            reason: str
        
        self.output_parser = PydanticOutputParser(DetectionResult)
        
        # 构建识别提示模板
        self.detection_template = PromptTemplate(
            """你是一个专业的文本分析专家，需要判断以下消息是否为群聊中的自我介绍。

消息内容：{message_content}
发送者昵称：{sender_nickname}

判断标准：
1. 包含个人基本信息（姓名、职业、背景等）
2. 有主动向群友介绍自己的意图
3. 通常包含"大家好"、"我是"、"来自"等表达
4. 可能包含希望得到什么或能提供什么

请分析并返回结构化结果：
{format_instructions}
"""
        )
        
        self.pattern_rules = [
            r'大家好.*我是',
            r'自我介绍.*一下',
            r'新人报到',
            r'初来乍到',
            r'认识.*大家',
            r'我叫.*做',
            r'从事.*行业',
            r'可以.*提供',
            r'希望.*得到'
        ]
    
    def detect_introductions(self, messages: List[Message]) -> List[Message]:
        """识别自我介绍消息"""
        candidates = self._rule_based_filter(messages)
        
        confirmed = []
        for msg in candidates:
            if self._ai_confirm_introduction(msg):
                confirmed.append(msg)
        
        return confirmed
    
    def _rule_based_filter(self, messages: List[Message]) -> List[Message]:
        """基于规则的预筛选"""
        filtered = []
        for msg in messages:
            # 长度检查
            if len(msg.content.strip()) < 20:
                continue
                
            # 模式匹配
            if any(re.search(pattern, msg.content) for pattern in self.pattern_rules):
                filtered.append(msg)
                
        return filtered
    
    def _ai_confirm_introduction(self, message: Message) -> bool:
        """AI确认是否为自我介绍"""
        try:
            prompt = self.detection_template.format(
                message_content=message.content,
                sender_nickname=message.sender_nickname,
                format_instructions=self.output_parser.format_instructions
            )
            
            response = self.llm.complete(prompt)
            result = self.output_parser.parse(response.text)
            
            # 置信度阈值判断
            return result.is_introduction and result.confidence >= 0.7
            
        except Exception as e:
            print(f"AI识别失败: {e}")
            return False

#### 3.2.2 信息提取器
```python
from llama_index.core.output_parsers import PydanticOutputParser
from pydantic import BaseModel, Field
from typing import Optional

class ProfileExtractor:
    """用户档案信息提取器"""
    
    def __init__(self, deepseek_api_key: str):
        # 初始化LlamaIndex LLM
        self.llm = DeepSeek(
            api_key=deepseek_api_key,
            model="deepseek-chat",
            temperature=0.1
        )
        
        # 定义提取结果模型
        class ExtractedProfile(BaseModel):
            nickname: Optional[str] = Field(description="用户昵称或真实姓名")
            profession: Optional[str] = Field(description="职业或职位")
            personal_intro: Optional[str] = Field(description="个人介绍或标签")
            can_provide: Optional[str] = Field(description="能够提供的资源或服务")
            looking_for: Optional[str] = Field(description="希望得到的帮助或资源")
            industry: Optional[str] = Field(description="所在行业")
            confidence: float = Field(description="提取置信度，0-1之间")
        
        self.output_parser = PydanticOutputParser(ExtractedProfile)
        
        # 构建提取提示模板
        self.extraction_template = PromptTemplate(
            """你是一个专业的信息提取专家，需要从以下自我介绍中提取结构化的用户档案信息。

自我介绍内容：
{introduction_content}

发送者昵称：{sender_nickname}

提取要求：
1. 只提取明确表达的信息，不要推测或补充
2. 如果某个字段无法确定，请设为null
3. 能提供和希望得到的内容要具体，避免泛泛而谈
4. 置信度基于信息的明确程度和完整性

请提取以下字段并返回结构化结果：
{format_instructions}

示例输入："大家好，我是张三，目前在腾讯做产品经理，有5年互联网产品经验。可以为大家提供产品设计和用户体验方面的咨询，希望能找到一些技术合作的机会。"

示例输出：
{{
    "nickname": "张三",
    "profession": "产品经理",
    "personal_intro": "5年互联网产品经验",
    "can_provide": "产品设计和用户体验咨询",
    "looking_for": "技术合作机会",
    "industry": "互联网",
    "confidence": 0.9
}}
"""
        )
        
    def extract_profile(self, introduction: Message) -> UserProfile:
        """提取用户档案信息"""
        try:
            prompt = self.extraction_template.format(
                introduction_content=introduction.content,
                sender_nickname=introduction.sender_nickname,
                format_instructions=self.output_parser.format_instructions
            )
            
            response = self.llm.complete(prompt)
            extracted = self.output_parser.parse(response.text)
            
            # 转换为UserProfile对象
            return UserProfile(
                wxid=introduction.sender,
                nickname=extracted.nickname or introduction.sender_nickname,
                profession=extracted.profession,
                personal_intro=extracted.personal_intro,
                can_provide=extracted.can_provide,
                looking_for=extracted.looking_for,
                industry=extracted.industry,
                raw_message=introduction.content,
                confidence=extracted.confidence,
                extracted_at=datetime.now(),
                group_id=getattr(introduction, 'group_id', '')
            )
            
        except Exception as e:
            print(f"信息提取失败: {e}")
            return self._create_fallback_profile(introduction)
    
    def _create_fallback_profile(self, introduction: Message) -> UserProfile:
        """创建降级档案"""
        return UserProfile(
            wxid=introduction.sender,
            nickname=introduction.sender_nickname,
            raw_message=introduction.content,
            confidence=0.0,
            extracted_at=datetime.now(),
            group_id=getattr(introduction, 'group_id', '')
        )

    def batch_extract_profiles(self, introductions: List[Message]) -> List[UserProfile]:
        """批量提取用户档案"""
        profiles = []
        
        for intro in introductions:
            profile = self.extract_profile(intro)
            
            # 只保留置信度达标的档案
            if profile.confidence >= 0.7:
                profiles.append(profile)
            else:
                print(f"置信度过低，跳过用户 {profile.nickname}: {profile.confidence}")
        
        return profiles
```

#### 3.2.3 任务调度器
```python
class TaskScheduler:
    """任务调度和管理器"""
    
    def __init__(self, deepseek_api_key: str, chatlog_client: ChatlogClient, feishu_client: FeishuClient):
        self.running_tasks = {}
        self.task_history = []
        
        # 初始化LlamaIndex组件
        self.intro_detector = IntroductionDetector(deepseek_api_key)
        self.profile_extractor = ProfileExtractor(deepseek_api_key)
        self.chatlog_client = chatlog_client
        self.feishu_client = feishu_client
        
    def schedule_analysis(self, task_config: AnalysisTask) -> str:
        """调度分析任务"""
        task_id = self._generate_task_id()
        
        task_thread = threading.Thread(
            target=self._execute_analysis,
            args=(task_id, task_config),
            daemon=True
        )
        
        self.running_tasks[task_id] = {
            'thread': task_thread,
            'config': task_config,
            'status': 'pending',
            'progress': 0,
            'start_time': datetime.now(),
            'processed_messages': 0,
            'found_introductions': 0,
            'extracted_profiles': 0
        }
        
        task_thread.start()
        return task_id
    
    def _execute_analysis(self, task_id: str, config: AnalysisTask):
        """执行分析任务"""
        try:
            self._update_task_status(task_id, 'running', "开始分析任务...")
            
            # 1. 获取消息数据
            self._update_progress(task_id, 10, "获取群聊消息...")
            messages = self._fetch_messages(config)
            self.running_tasks[task_id]['processed_messages'] = len(messages)
            self._update_progress(task_id, 25, f"获取到 {len(messages)} 条消息")
            
            # 2. 识别自我介绍
            self._update_progress(task_id, 30, "识别自我介绍消息...")
            introductions = self.intro_detector.detect_introductions(messages)
            self.running_tasks[task_id]['found_introductions'] = len(introductions)
            self._update_progress(task_id, 50, f"识别到 {len(introductions)} 条自我介绍")
            
            # 3. 批量提取用户档案
            self._update_progress(task_id, 60, "提取用户档案信息...")
            profiles = self.profile_extractor.batch_extract_profiles(introductions)
            self.running_tasks[task_id]['extracted_profiles'] = len(profiles)
            self._update_progress(task_id, 80, f"提取到 {len(profiles)} 个有效档案")
            
            # 4. 同步到飞书
            self._update_progress(task_id, 85, "同步数据到飞书...")
            synced_count = self._sync_to_feishu(profiles, task_id)
            self._update_progress(task_id, 100, f"同步完成，共处理 {synced_count} 个档案")
            
            # 更新任务状态
            self._update_task_status(task_id, 'completed', f"分析完成！处理了 {len(messages)} 条消息，提取了 {len(profiles)} 个档案")
            
            # 记录到历史
            self.task_history.append({
                'task_id': task_id,
                'config': config,
                'result': {
                    'processed_messages': len(messages),
                    'found_introductions': len(introductions),
                    'extracted_profiles': len(profiles),
                    'synced_profiles': synced_count
                },
                'completed_at': datetime.now()
            })
            
        except Exception as e:
            error_msg = f"分析失败: {str(e)}"
            self._update_task_status(task_id, 'failed', error_msg)
            print(f"Task {task_id} failed: {e}")
    
    def _fetch_messages(self, config: AnalysisTask) -> List[Message]:
        """获取消息数据"""
        raw_messages = self.chatlog_client.get_group_messages(
            group_id=config.group_id,
            days=config.days
        )
        
        # 转换为Message对象
        messages = []
        for raw_msg in raw_messages:
            msg = Message(
                sender=raw_msg.get('sender', ''),
                sender_nickname=raw_msg.get('sender_nickname', ''),
                content=raw_msg.get('content', ''),
                timestamp=datetime.fromisoformat(raw_msg.get('timestamp', '')),
                message_type=raw_msg.get('message_type', 'text')
            )
            msg.group_id = config.group_id  # 添加群组ID
            messages.append(msg)
        
        return messages
    
    def _sync_to_feishu(self, profiles: List[UserProfile], task_id: str) -> int:
        """同步到飞书多维表格"""
        synced_count = 0
        total_profiles = len(profiles)
        
        for i, profile in enumerate(profiles):
            try:
                # 检查是否已存在
                existing = self.feishu_client.find_record_by_wxid(profile.wxid)
                
                if existing:
                    # 更新现有记录
                    success = self.feishu_client.update_record(existing['record_id'], profile)
                else:
                    # 创建新记录
                    success = self.feishu_client.create_record(profile)
                
                if success:
                    synced_count += 1
                
                # 更新进度
                progress = 85 + int((i + 1) / total_profiles * 15)
                self._update_progress(task_id, progress, f"同步进度: {i + 1}/{total_profiles}")
                
            except Exception as e:
                print(f"同步用户 {profile.nickname} 失败: {e}")
        
        return synced_count
    
    def _update_task_status(self, task_id: str, status: str, message: str = ""):
        """更新任务状态"""
        if task_id in self.running_tasks:
            self.running_tasks[task_id]['status'] = status
            self.running_tasks[task_id]['message'] = message
            if status in ['completed', 'failed']:
                self.running_tasks[task_id]['end_time'] = datetime.now()
    
    def _update_progress(self, task_id: str, progress: int, message: str = ""):
        """更新任务进度"""
        if task_id in self.running_tasks:
            self.running_tasks[task_id]['progress'] = progress
            if message:
                self.running_tasks[task_id]['message'] = message
    
    def _generate_task_id(self) -> str:
        """生成任务ID"""
        import uuid
        return str(uuid.uuid4())[:8]
    
    def get_task_status(self, task_id: str) -> Optional[Dict]:
        """获取任务状态"""
        return self.running_tasks.get(task_id)
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id in self.running_tasks:
            task = self.running_tasks[task_id]
            if task['status'] == 'running':
                # 注意：Python threading不支持强制停止，这里只是标记状态
                task['status'] = 'cancelled'
                task['end_time'] = datetime.now()
                return True
        return False
```

### 3.3 数据访问模块 (Data Access Layer)

#### 3.3.1 Chatlog API客户端
```python
class ChatlogClient:
    """Chatlog API客户端"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:5030"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def get_group_messages(self, group_id: str, days: int = 7) -> List[Dict]:
        """获取群组消息"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        params = {
            'talker': group_id,
            'time': f"{start_date.strftime('%Y-%m-%d')}~{end_date.strftime('%Y-%m-%d')}",
            'limit': 1000
        }
        
        response = self.session.get(f"{self.base_url}/api/v1/chatlog", params=params)
        response.raise_for_status()
        
        return response.json().get('data', [])
    
    def get_group_list(self) -> List[Dict]:
        """获取群组列表"""
        response = self.session.get(f"{self.base_url}/api/v1/chatroom")
        response.raise_for_status()
        
        return response.json().get('data', [])
    
    def test_connection(self) -> bool:
        """测试连接"""
        try:
            response = self.session.get(f"{self.base_url}/api/v1/session", timeout=5)
            return response.status_code == 200
        except:
            return False
```

#### 3.3.2 飞书API客户端
```python
class FeishuClient:
    """飞书多维表格客户端"""
    
    def __init__(self, app_id: str, app_secret: str, app_token: str, table_id: str):
        self.app_id = app_id
        self.app_secret = app_secret
        self.app_token = app_token
        self.table_id = table_id
        self.access_token = self._get_access_token()
    
    def _get_access_token(self) -> str:
        """获取访问令牌"""
        url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
        data = {
            "app_id": self.app_id,
            "app_secret": self.app_secret
        }
        
        response = requests.post(url, json=data)
        return response.json()["tenant_access_token"]
    
    def create_record(self, profile: UserProfile) -> bool:
        """创建记录"""
        url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.app_token}/tables/{self.table_id}/records"
        
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
        
        data = {
            "records": [{
                "fields": {
                    "微信ID": profile.wxid,
                    "昵称": profile.nickname,
                    "职业": profile.profession,
                    "个人介绍": profile.personal_intro,
                    "能够提供": profile.can_provide,
                    "希望得到": profile.looking_for,
                    "所在行业": profile.industry,
                    "原始消息": profile.raw_message,
                    "置信度": profile.confidence,
                    "提取时间": profile.extracted_at.isoformat()
                }
            }]
        }
        
        response = requests.post(url, headers=headers, json=data)
        return response.status_code == 200
    
    def update_record(self, record_id: str, profile: UserProfile) -> bool:
        """更新记录"""
        url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.app_token}/tables/{self.table_id}/records/{record_id}"
        
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
        
        data = {
            "fields": {
                "微信ID": profile.wxid,
                "昵称": profile.nickname,
                "职业": profile.profession,
                "个人介绍": profile.personal_intro,
                "能够提供": profile.can_provide,
                "希望得到": profile.looking_for,
                "所在行业": profile.industry,
                "原始消息": profile.raw_message,
                "置信度": profile.confidence,
                "提取时间": profile.extracted_at.isoformat()
            }
        }
        
        response = requests.put(url, headers=headers, json=data)
        return response.status_code == 200
    
    def find_record_by_wxid(self, wxid: str) -> Optional[Dict]:
        """通过微信ID查找记录"""
        url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.app_token}/tables/{self.table_id}/records/search"
        
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
        
        data = {
            "filter": {
                "conditions": [
                    {
                        "field_name": "微信ID",
                        "operator": "is",
                        "value": [wxid]
                    }
                ]
            }
        }
        
        response = requests.post(url, headers=headers, json=data)
        result = response.json()
        
        if result.get("data", {}).get("items"):
            return result["data"]["items"][0]
        return None
```

## 📦 数据模型设计

### 4.1 核心数据模型

```python
from dataclasses import dataclass
from datetime import datetime
from typing import Optional

@dataclass
class UserProfile:
    """用户档案数据模型"""
    wxid: str
    nickname: str
    profession: Optional[str] = None
    personal_intro: Optional[str] = None
    can_provide: Optional[str] = None
    looking_for: Optional[str] = None
    industry: Optional[str] = None
    raw_message: str = ""
    confidence: float = 0.0
    extracted_at: datetime = None
    group_id: str = ""
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            "微信ID": self.wxid,
            "昵称": self.nickname,
            "职业": self.profession,
            "个人介绍": self.personal_intro,
            "能够提供": self.can_provide,
            "希望得到": self.looking_for,
            "所在行业": self.industry,
            "原始消息": self.raw_message,
            "置信度": self.confidence,
            "提取时间": self.extracted_at.isoformat() if self.extracted_at else ""
        }

@dataclass
class Message:
    """消息数据模型"""
    sender: str
    sender_nickname: str
    content: str
    timestamp: datetime
    message_type: str = "text"
    
@dataclass
class AnalysisTask:
    """分析任务配置"""
    group_id: str
    group_name: str
    days: int = 7
    confidence_threshold: float = 0.7
    auto_sync: bool = True
    
@dataclass
class TaskStatus:
    """任务状态"""
    task_id: str
    status: str  # pending, running, completed, failed
    progress: int  # 0-100
    message: str = ""
    start_time: datetime = None
    end_time: Optional[datetime] = None
```

### 4.2 配置数据模型

```python
@dataclass
class AppConfig:
    """应用配置"""
    deepseek_api_key: str = ""
    chatlog_url: str = "http://127.0.0.1:5030"
    
    # 飞书配置
    feishu_app_id: str = ""
    feishu_app_secret: str = ""
    feishu_app_token: str = ""
    feishu_table_id: str = ""
    
    # LlamaIndex & AI分析参数
    llm_model: str = "deepseek-chat"
    llm_temperature: float = 0.1
    llm_max_tokens: int = 500
    
    # 分析参数
    default_analysis_days: int = 7
    min_confidence_threshold: float = 0.7
    max_messages_per_batch: int = 1000
    introduction_min_length: int = 20
    
    def save_to_file(self, filepath: str):
        """保存到文件"""
        # 敏感数据加密处理
        config_data = asdict(self)
        
        if self.deepseek_api_key:
            security = ConfigSecurity()
            config_data['deepseek_api_key'] = security.encrypt_sensitive_data(self.deepseek_api_key)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
    
    @classmethod
    def load_from_file(cls, filepath: str) -> 'AppConfig':
        """从文件加载"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 解密敏感数据
            if data.get('deepseek_api_key'):
                try:
                    security = ConfigSecurity()
                    data['deepseek_api_key'] = security.decrypt_sensitive_data(data['deepseek_api_key'])
                except:
                    # 如果解密失败，可能是明文存储，保持原值
                    pass
            
            return cls(**data)
        except:
            return cls()
    
    def validate(self) -> List[str]:
        """验证配置完整性"""
        errors = []
        
        if not self.deepseek_api_key:
            errors.append("DeepSeek API密钥不能为空")
        
        if not self.chatlog_url:
            errors.append("Chatlog API地址不能为空")
        
        # 验证飞书配置
        feishu_fields = [self.feishu_app_id, self.feishu_app_secret, 
                        self.feishu_app_token, self.feishu_table_id]
        if any(feishu_fields) and not all(feishu_fields):
            errors.append("飞书配置不完整，请填写所有字段")
        
        # 验证参数范围
        if not 0.0 <= self.llm_temperature <= 2.0:
            errors.append("LLM温度参数应在0-2之间")
        
        if not 0.1 <= self.min_confidence_threshold <= 1.0:
            errors.append("置信度阈值应在0.1-1.0之间")
        
        return errors
    
    def get_llm_config(self) -> Dict:
        """获取LLM配置"""
        return {
            "api_key": self.deepseek_api_key,
            "model": self.llm_model,
            "temperature": self.llm_temperature,
            "max_tokens": self.llm_max_tokens
        }
```

## 🔧 部署架构

### 5.1 开发环境搭建

```bash
# 1. 环境准备
python --version  # 确保Python 3.8+
pip install --upgrade pip

# 2. 依赖安装
pip install -r requirements.txt

# 3. 开发工具
pip install pytest black flake8  # 可选的开发工具
```

### 5.2 构建流程

```python
# build_config.py - 构建配置
BUILD_CONFIG = {
    "app_name": "微信群档案分析器",
    "version": "1.0.0",
    "icon": "assets/icon.ico",
    "exclude_modules": [
        "matplotlib", "numpy", "pandas", "jupyter",
        "IPython", "tornado", "zmq", "scipy"
    ],
    "hidden_imports": [
        "tkinter", "ttkbootstrap", "requests", 
        "json", "threading", "datetime",
        # LlamaIndex相关
        "llama_index.core",
        "llama_index.llms.deepseek", 
        "llama_index.core.output_parsers",
        "llama_index.core.llms",
        "pydantic",
        "openai"  # LlamaIndex依赖
    ],
    "data_files": [
        ("config.json", "."),
        ("README.md", "."),
        ("assets/*", "assets")
    ]
}

# requirements.txt 更新
REQUIREMENTS = """
# 图形界面
ttkbootstrap==1.10.1

# HTTP客户端
requests==2.31.0

# LlamaIndex相关
llama-index==0.9.48
llama-index-llms-deepseek==0.1.3
pydantic==2.5.0

# 飞书API
requests==2.31.0

# 打包工具
PyInstaller==6.2.0

# 数据处理
openpyxl==3.1.2

# 加密
cryptography==41.0.8
"""
```

### 5.3 打包脚本

```python
# build.py
import PyInstaller.__main__
import os
import shutil
from pathlib import Path

def build_application():
    """构建应用程序"""
    
    # 清理之前的构建
    if os.path.exists("dist"):
        shutil.rmtree("dist")
    if os.path.exists("build"):
        shutil.rmtree("build")
    
    # PyInstaller参数
    args = [
        "main.py",
        "--onefile",
        "--windowed",
        f"--name={BUILD_CONFIG['app_name']}",
        f"--icon={BUILD_CONFIG['icon']}",
        "--clean",
        "--noconfirm",
        
        # 排除模块
        *[f"--exclude-module={module}" for module in BUILD_CONFIG['exclude_modules']],
        
        # 隐藏导入
        *[f"--hidden-import={module}" for module in BUILD_CONFIG['hidden_imports']],
        
        # 数据文件
        *[f"--add-data={src};{dst}" for src, dst in BUILD_CONFIG['data_files']]
    ]
    
    # 执行构建
    PyInstaller.__main__.run(args)
    
    print(f"构建完成！输出文件：dist/{BUILD_CONFIG['app_name']}.exe")

if __name__ == "__main__":
    build_application()
```

### 5.4 分发策略

```bash
# 分发目录结构
release/
├── 微信群档案分析器.exe       # 主程序
├── README.md                   # 使用说明
├── 配置指南.pdf               # 详细配置指南
├── 示例配置.json              # 配置文件模板
└── 更新日志.md                # 版本更新记录
```

## 🛡️ 安全设计

### 6.1 数据安全
- **本地处理**：所有聊天数据在本地处理，不上传到云端
- **API密钥加密**：使用简单加密存储API密钥
- **访问控制**：只处理用户授权的群组数据

### 6.2 错误处理
```python
class ErrorHandler:
    """统一错误处理器"""
    
    @staticmethod
    def handle_api_error(func):
        """API调用错误装饰器"""
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except requests.exceptions.ConnectionError:
                raise APIConnectionError("网络连接失败，请检查网络设置")
            except requests.exceptions.Timeout:
                raise APITimeoutError("请求超时，请稍后重试")
            except requests.exceptions.HTTPError as e:
                raise APIError(f"API调用失败：{e.response.status_code}")
        return wrapper
    
    @staticmethod
    def handle_ui_error(func):
        """UI错误装饰器"""
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                messagebox.showerror("错误", f"操作失败：{str(e)}")
        return wrapper
```

### 6.3 配置安全
```python
import base64
from cryptography.fernet import Fernet

class ConfigSecurity:
    """配置安全管理"""
    
    def __init__(self):
        self.key = self._get_or_create_key()
        self.cipher = Fernet(self.key)
    
    def encrypt_sensitive_data(self, data: str) -> str:
        """加密敏感数据"""
        return self.cipher.encrypt(data.encode()).decode()
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """解密敏感数据"""
        return self.cipher.decrypt(encrypted_data.encode()).decode()
    
    def _get_or_create_key(self) -> bytes:
        """获取或创建加密密钥"""
        key_file = "app.key"
        if os.path.exists(key_file):
            with open(key_file, "rb") as f:
                return f.read()
        else:
            key = Fernet.generate_key()
            with open(key_file, "wb") as f:
                f.write(key)
            return key
```

## 📊 性能优化

### 7.1 响应性优化
- **异步处理**：长时间任务使用线程处理，避免界面卡顿
- **进度反馈**：实时显示处理进度和状态
- **批量处理**：支持批量API调用，提高效率

### 7.2 内存优化
- **流式处理**：大批量数据分批处理
- **对象复用**：重用HTTP连接和客户端对象
- **垃圾回收**：及时释放不需要的对象

### 7.3 网络优化
- **连接池**：使用Session维护连接池
- **重试机制**：实现指数退避重试
- **超时控制**：合理设置网络超时时间

## 🔍 监控和日志

### 8.1 日志设计
```python
import logging
from datetime import datetime

class AppLogger:
    """应用日志管理器"""
    
    def __init__(self, log_file: str = "app.log"):
        self.logger = logging.getLogger("WeChatProfileAnalyzer")
        self.logger.setLevel(logging.INFO)
        
        # 文件处理器
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        self.logger.addHandler(file_handler)
        
        # 控制台处理器（开发环境）
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter('%(levelname)s - %(message)s')
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)
    
    def info(self, message: str):
        self.logger.info(message)
    
    def error(self, message: str):
        self.logger.error(message)
    
    def warning(self, message: str):
        self.logger.warning(message)
```

### 8.2 性能监控
```python
class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = {}
    
    def measure_time(self, operation_name: str):
        """时间测量装饰器"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                start_time = time.time()
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                self.record_metric(operation_name, duration)
                return result
            return wrapper
        return decorator
    
    def record_metric(self, name: str, value: float):
        """记录性能指标"""
        if name not in self.metrics:
            self.metrics[name] = []
        self.metrics[name].append({
            'value': value,
            'timestamp': datetime.now()
        })
```

## 🚀 扩展设计

### 9.1 插件架构
```python
class PluginManager:
    """插件管理器"""
    
    def __init__(self):
        self.plugins = {}
    
    def register_plugin(self, name: str, plugin_class):
        """注册插件"""
        self.plugins[name] = plugin_class
    
    def load_plugins(self):
        """加载插件"""
        plugin_dir = "plugins"
        if os.path.exists(plugin_dir):
            for file in os.listdir(plugin_dir):
                if file.endswith(".py"):
                    self._load_plugin_file(os.path.join(plugin_dir, file))
```

### 9.2 API扩展接口
```python
class APIExtension:
    """API扩展接口"""
    
    def extract_profile(self, content: str) -> UserProfile:
        """扩展的信息提取接口"""
        raise NotImplementedError
    
    def detect_introduction(self, message: str) -> bool:
        """扩展的识别接口"""
        raise NotImplementedError
```

---

**文档版本**：v1.0  
**最后更新**：2024年12月  
**架构师**：项目团队  
**审核状态**：待审核 