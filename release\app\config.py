"""
配置管理模块
管理应用程序的所有配置信息，包括API配置、界面配置等
"""

import os
import json
from pathlib import Path
from dataclasses import dataclass, asdict
from typing import Dict, Any, Optional
from enum import Enum

class AppMode(Enum):
    """应用运行模式"""
    DEVELOPMENT = "development"
    PRODUCTION = "production"

@dataclass
class ChatlogConfig:
    """Chatlog API 配置"""
    base_url: str = "http://localhost:8080"  # Chatlog API基础URL
    timeout: int = 30  # 请求超时时间（秒）
    max_retries: int = 3  # 最大重试次数
    
@dataclass 
class DeepSeekConfig:
    """DeepSeek API 配置"""
    api_key: str = ""  # DeepSeek API密钥
    base_url: str = "https://api.deepseek.com"  # DeepSeek API基础URL
    model: str = "deepseek-chat"  # 使用的模型名称
    max_tokens: int = 4000  # 最大输出tokens
    temperature: float = 0.1  # 生成温度
    timeout: int = 60  # 请求超时时间（秒）

@dataclass
class FeishuConfig:
    """飞书多维表格 API 配置"""
    app_id: str = ""  # 飞书应用ID
    app_secret: str = ""  # 飞书应用密钥
    base_url: str = "https://open.feishu.cn"  # 飞书API基础URL
    timeout: int = 30  # 请求超时时间（秒）

@dataclass
class UIConfig:
    """界面配置"""
    theme: str = "cosmo"  # ttkbootstrap主题
    window_width: int = 1200  # 窗口宽度
    window_height: int = 800  # 窗口高度
    min_width: int = 1000  # 最小宽度
    min_height: int = 600  # 最小高度

@dataclass
class AnalysisConfig:
    """分析配置"""
    batch_size: int = 10  # 批处理大小
    max_workers: int = 5  # 最大工作线程数
    sync_interval: int = 300  # 同步间隔（秒）
    auto_detect: bool = True  # 是否自动检测自我介绍
    confidence_threshold: float = 0.8  # 置信度阈值

@dataclass
class AppConfig:
    """应用程序总配置"""
    mode: AppMode = AppMode.DEVELOPMENT
    debug: bool = True
    log_level: str = "INFO"
    chatlog: ChatlogConfig = None
    deepseek: DeepSeekConfig = None  
    feishu: FeishuConfig = None
    ui: UIConfig = None
    analysis: AnalysisConfig = None
    
    def __post_init__(self):
        if self.chatlog is None:
            self.chatlog = ChatlogConfig()
        if self.deepseek is None:
            self.deepseek = DeepSeekConfig()
        if self.feishu is None:
            self.feishu = FeishuConfig()
        if self.ui is None:
            self.ui = UIConfig()
        if self.analysis is None:
            self.analysis = AnalysisConfig()

class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.config_file = Path("app/data/config.json")
        self.env_file = Path(".env")
        self._config: Optional[AppConfig] = None
        
    def load_config(self) -> AppConfig:
        """加载配置"""
        if self._config is None:
            self._config = self._load_from_files()
            self._load_from_env()
        return self._config
    
    def save_config(self, config: AppConfig) -> bool:
        """保存配置到文件"""
        try:
            # 确保目录存在
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存到JSON文件
            config_dict = asdict(config)
            # 转换枚举值
            config_dict['mode'] = config.mode.value
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, ensure_ascii=False, indent=2)
            
            self._config = config
            return True
        except Exception as e:
            print(f"保存配置失败: {e}")
            return False
    
    def _load_from_files(self) -> AppConfig:
        """从文件加载配置"""
        config = AppConfig()
        
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 转换模式枚举
                if 'mode' in data:
                    data['mode'] = AppMode(data['mode'])
                
                # 创建配置对象
                config = AppConfig(
                    mode=data.get('mode', AppMode.DEVELOPMENT),
                    debug=data.get('debug', True),
                    log_level=data.get('log_level', 'INFO'),
                    chatlog=ChatlogConfig(**data.get('chatlog', {})),
                    deepseek=DeepSeekConfig(**data.get('deepseek', {})),
                    feishu=FeishuConfig(**data.get('feishu', {})),
                    ui=UIConfig(**data.get('ui', {})),
                    analysis=AnalysisConfig(**data.get('analysis', {}))
                )
            except Exception as e:
                print(f"加载配置文件失败: {e}, 使用默认配置")
        
        return config
    
    def _load_from_env(self):
        """从环境变量加载敏感配置"""
        if self.env_file.exists():
            from dotenv import load_dotenv
            load_dotenv(self.env_file)
        
        # 从环境变量覆盖敏感配置
        if os.getenv('DEEPSEEK_API_KEY'):
            self._config.deepseek.api_key = os.getenv('DEEPSEEK_API_KEY')
        if os.getenv('FEISHU_APP_ID'):
            self._config.feishu.app_id = os.getenv('FEISHU_APP_ID')
        if os.getenv('FEISHU_APP_SECRET'):
            self._config.feishu.app_secret = os.getenv('FEISHU_APP_SECRET')
    
    def update_config(self, **kwargs) -> bool:
        """更新配置"""
        if self._config is None:
            self.load_config()
        
        try:
            for key, value in kwargs.items():
                if hasattr(self._config, key):
                    setattr(self._config, key, value)
            return self.save_config(self._config)
        except Exception as e:
            print(f"更新配置失败: {e}")
            return False

# 全局配置管理器实例
config_manager = ConfigManager()

def get_config() -> AppConfig:
    """获取应用配置"""
    return config_manager.load_config()

def save_config(config: AppConfig) -> bool:
    """保存应用配置"""
    return config_manager.save_config(config)

def update_config(**kwargs) -> bool:
    """更新应用配置"""
    return config_manager.update_config(**kwargs) 