"""
分析任务页面
配置和执行分析任务
"""

import flet as ft
from typing import TYPE_CHECKING, Dict, Any
import asyncio
from datetime import datetime

if TYPE_CHECKING:
    from ..main_app import FletMainApp

class AnalysisPage:
    """分析任务页面"""
    
    def __init__(self, app: 'FletMainApp'):
        self.app = app
        self.is_analyzing = False
        self.current_progress = 0
        self.analysis_status = "就绪"
        
        # UI组件引用
        self.progress_bar = None
        self.progress_text = None
        self.status_text = None
        self.start_button = None
        self.stop_button = None
        self.log_container = None
        
        # 配置参数
        self.analysis_days = 7
        self.confidence_threshold = 0.8
        self.auto_sync = True
    
    def build(self) -> ft.Column:
        """构建分析任务页面"""
        
        # 配置区域
        config_section = self._create_config_section()
        
        # 进度区域
        progress_section = self._create_progress_section()
        
        # 控制区域
        control_section = self._create_control_section()
        
        # 日志区域
        log_section = self._create_log_section()
        
        return ft.Column(
            controls=[
                # 配置和进度并排
                ft.Row(
                    controls=[
                        config_section,
                        progress_section
                    ],
                    spacing=20,
                    expand=True
                ),
                
                ft.Container(height=20),
                
                # 控制区域
                control_section,
                
                ft.Container(height=20),
                
                # 日志区域
                log_section
            ],
            spacing=0,
            expand=True
        )
    
    def _create_config_section(self) -> ft.Container:
        """创建配置区域"""
        
        # 分析天数滑块
        days_slider = ft.Slider(
            min=1,
            max=30,
            value=self.analysis_days,
            divisions=29,
            label="{value}天",
            on_change=self._on_days_change
        )
        
        # 置信度阈值滑块
        confidence_slider = ft.Slider(
            min=0.1,
            max=1.0,
            value=self.confidence_threshold,
            divisions=9,
            label="{value}",
            on_change=self._on_confidence_change
        )
        
        # 自动同步开关
        auto_sync_switch = ft.Switch(
            value=self.auto_sync,
            on_change=self._on_auto_sync_change
        )
        
        return ft.Container(
            content=ft.Column(
                controls=[
                    ft.Text(
                        "⚙️ 分析配置",
                        size=18,
                        weight=ft.FontWeight.BOLD,
                        color=ft.colors.GREY_800
                    ),
                    ft.Container(height=15),
                    
                    # 分析天数
                    ft.Column(
                        controls=[
                            ft.Text(f"分析天数: {self.analysis_days} 天", size=14, weight=ft.FontWeight.W500),
                            days_slider,
                            ft.Text("分析最近N天的群聊消息", size=12, color=ft.colors.GREY_600)
                        ],
                        spacing=5
                    ),
                    
                    ft.Container(height=15),
                    
                    # 置信度阈值
                    ft.Column(
                        controls=[
                            ft.Text(f"置信度阈值: {self.confidence_threshold:.1f}", size=14, weight=ft.FontWeight.W500),
                            confidence_slider,
                            ft.Text("只保留置信度高于此值的结果", size=12, color=ft.colors.GREY_600)
                        ],
                        spacing=5
                    ),
                    
                    ft.Container(height=15),
                    
                    # 自动同步
                    ft.Row(
                        controls=[
                            ft.Column(
                                controls=[
                                    ft.Text("自动同步到飞书", size=14, weight=ft.FontWeight.W500),
                                    ft.Text("完成后自动同步结果到飞书表格", size=12, color=ft.colors.GREY_600)
                                ],
                                expand=True
                            ),
                            auto_sync_switch
                        ],
                        alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        vertical_alignment=ft.CrossAxisAlignment.CENTER
                    ),
                    
                    ft.Container(height=20),
                    
                    # 预估信息
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                ft.Text("📊 预估信息", size=14, weight=ft.FontWeight.BOLD, color=ft.colors.BLUE),
                                ft.Text("• 预计处理消息: ~500条", size=12, color=ft.colors.GREY_600),
                                ft.Text("• 预计用时: 2-5分钟", size=12, color=ft.colors.GREY_600),
                                ft.Text("• API调用成本: ~0.2元", size=12, color=ft.colors.GREY_600)
                            ],
                            spacing=5
                        ),
                        padding=ft.padding.all(15),
                        bgcolor=ft.colors.BLUE_50,
                        border_radius=8,
                        border=ft.border.all(1, ft.colors.BLUE_200)
                    )
                ],
                spacing=10
            ),
            padding=ft.padding.all(25),
            bgcolor=ft.colors.WHITE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.colors.BLACK12,
                offset=ft.Offset(0, 4)
            ),
            expand=1
        )
    
    def _create_progress_section(self) -> ft.Container:
        """创建进度区域"""
        
        # 进度条
        self.progress_bar = ft.ProgressBar(
            value=self.current_progress / 100,
            bgcolor=ft.colors.GREY_200,
            color=ft.colors.BLUE
        )
        
        # 进度文本
        self.progress_text = ft.Text(
            f"{self.current_progress}%",
            size=24,
            weight=ft.FontWeight.BOLD,
            color=ft.colors.BLUE,
            text_align=ft.TextAlign.CENTER
        )
        
        # 状态文本
        self.status_text = ft.Text(
            self.analysis_status,
            size=14,
            color=ft.colors.GREY_600,
            text_align=ft.TextAlign.CENTER
        )
        
        # 统计信息
        stats_info = ft.Column(
            controls=[
                self._create_stat_item("处理消息", "0", ft.icons.MESSAGE),
                self._create_stat_item("发现介绍", "0", ft.icons.PERSON_SEARCH),
                self._create_stat_item("提取档案", "0", ft.icons.PERSON_ADD),
                self._create_stat_item("同步记录", "0", ft.icons.CLOUD_UPLOAD)
            ],
            spacing=12
        )
        
        return ft.Container(
            content=ft.Column(
                controls=[
                    ft.Text(
                        "📈 分析进度",
                        size=18,
                        weight=ft.FontWeight.BOLD,
                        color=ft.colors.GREY_800
                    ),
                    ft.Container(height=15),
                    
                    # 圆形进度指示器
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                self.progress_text,
                                self.status_text
                            ],
                            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                            spacing=5
                        ),
                        width=120,
                        height=120,
                        border_radius=60,
                        bgcolor=ft.colors.BLUE_50,
                        border=ft.border.all(4, ft.colors.BLUE_200),
                        alignment=ft.alignment.center
                    ),
                    
                    ft.Container(height=15),
                    
                    # 进度条
                    self.progress_bar,
                    
                    ft.Container(height=20),
                    
                    # 统计信息
                    ft.Text("📊 实时统计", size=14, weight=ft.FontWeight.BOLD, color=ft.colors.GREY_700),
                    ft.Container(height=10),
                    stats_info
                ],
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                spacing=10
            ),
            padding=ft.padding.all(25),
            bgcolor=ft.colors.WHITE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.colors.BLACK12,
                offset=ft.Offset(0, 4)
            ),
            expand=1
        )
    
    def _create_stat_item(self, label: str, value: str, icon: ft.icons) -> ft.Row:
        """创建统计项"""
        return ft.Row(
            controls=[
                ft.Icon(icon, size=16, color=ft.colors.GREY_500),
                ft.Text(label, size=12, color=ft.colors.GREY_600, expand=True),
                ft.Text(value, size=12, weight=ft.FontWeight.BOLD, color=ft.colors.GREY_800)
            ],
            spacing=8,
            alignment=ft.MainAxisAlignment.START
        )
    
    def _create_control_section(self) -> ft.Container:
        """创建控制区域"""
        
        # 开始按钮
        self.start_button = ft.ElevatedButton(
            text="🚀 开始分析",
            icon=ft.icons.PLAY_ARROW,
            on_click=self._on_start_analysis,
            style=ft.ButtonStyle(
                bgcolor=ft.colors.GREEN,
                color=ft.colors.WHITE,
                padding=ft.padding.symmetric(horizontal=30, vertical=15)
            ),
            disabled=self.is_analyzing
        )
        
        # 停止按钮
        self.stop_button = ft.ElevatedButton(
            text="⏹️ 停止分析",
            icon=ft.icons.STOP,
            on_click=self._on_stop_analysis,
            style=ft.ButtonStyle(
                bgcolor=ft.colors.RED,
                color=ft.colors.WHITE,
                padding=ft.padding.symmetric(horizontal=30, vertical=15)
            ),
            disabled=not self.is_analyzing
        )
        
        # 其他操作按钮
        other_buttons = ft.Row(
            controls=[
                ft.OutlinedButton(
                    text="查看结果",
                    icon=ft.icons.ASSESSMENT,
                    on_click=lambda e: self.app.navigate_to("results")
                ),
                ft.OutlinedButton(
                    text="导出数据",
                    icon=ft.icons.DOWNLOAD,
                    on_click=self._on_export_data
                ),
                ft.OutlinedButton(
                    text="清空日志",
                    icon=ft.icons.CLEAR_ALL,
                    on_click=self._on_clear_log
                )
            ],
            spacing=10
        )
        
        return ft.Container(
            content=ft.Row(
                controls=[
                    # 主要控制按钮
                    ft.Row(
                        controls=[self.start_button, self.stop_button],
                        spacing=15
                    ),

                    # 其他操作按钮
                    other_buttons
                ],
                alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                vertical_alignment=ft.CrossAxisAlignment.CENTER
            ),
            padding=ft.padding.all(25),
            bgcolor=ft.colors.WHITE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.colors.BLACK12,
                offset=ft.Offset(0, 4)
            )
        )

    def _create_log_section(self) -> ft.Container:
        """创建日志区域"""

        # 日志容器
        self.log_container = ft.Column(
            controls=[
                ft.Text(
                    f"[{datetime.now().strftime('%H:%M:%S')}] 系统就绪，等待开始分析...",
                    size=12,
                    color=ft.colors.GREY_600,
                    font_family="Courier New"
                )
            ],
            spacing=2,
            scroll=ft.ScrollMode.AUTO
        )

        return ft.Container(
            content=ft.Column(
                controls=[
                    ft.Row(
                        controls=[
                            ft.Text(
                                "📋 分析日志",
                                size=18,
                                weight=ft.FontWeight.BOLD,
                                color=ft.colors.GREY_800
                            ),
                            ft.IconButton(
                                icon=ft.icons.CLEAR_ALL,
                                tooltip="清空日志",
                                on_click=self._on_clear_log,
                                icon_color=ft.colors.GREY_600
                            )
                        ],
                        alignment=ft.MainAxisAlignment.SPACE_BETWEEN
                    ),
                    ft.Container(height=10),
                    ft.Container(
                        content=self.log_container,
                        height=200,
                        padding=ft.padding.all(15),
                        bgcolor=ft.colors.GREY_900,
                        border_radius=8
                    )
                ]
            ),
            padding=ft.padding.all(25),
            bgcolor=ft.colors.WHITE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.colors.BLACK12,
                offset=ft.Offset(0, 4)
            )
        )

    def _add_log(self, message: str, level: str = "info"):
        """添加日志消息"""
        timestamp = datetime.now().strftime('%H:%M:%S')

        color_map = {
            "info": ft.colors.WHITE,
            "success": ft.colors.GREEN,
            "warning": ft.colors.ORANGE,
            "error": ft.colors.RED
        }

        log_entry = ft.Text(
            f"[{timestamp}] {message}",
            size=12,
            color=color_map.get(level, ft.colors.WHITE),
            font_family="Courier New"
        )

        self.log_container.controls.append(log_entry)

        # 限制日志条数
        if len(self.log_container.controls) > 50:
            self.log_container.controls.pop(0)

    async def _on_days_change(self, e):
        """分析天数变化"""
        self.analysis_days = int(e.control.value)
        await self.app.page.update_async()

    async def _on_confidence_change(self, e):
        """置信度阈值变化"""
        self.confidence_threshold = round(e.control.value, 1)
        await self.app.page.update_async()

    async def _on_auto_sync_change(self, e):
        """自动同步开关变化"""
        self.auto_sync = e.control.value
        await self.app.page.update_async()

    async def _on_start_analysis(self, e):
        """开始分析"""
        if self.is_analyzing:
            return

        self.is_analyzing = True
        self.start_button.disabled = True
        self.stop_button.disabled = False

        await self.app.page.update_async()

        # 开始模拟分析过程
        await self._simulate_analysis()

    async def _on_stop_analysis(self, e):
        """停止分析"""
        if not self.is_analyzing:
            return

        self.is_analyzing = False
        self.start_button.disabled = False
        self.stop_button.disabled = True

        self._add_log("用户手动停止分析任务", "warning")
        await self._update_status("已停止", 0)

        await self.app.page.update_async()

    async def _on_export_data(self, e):
        """导出数据"""
        await self.app.show_message("导出数据", "数据导出功能开发中...", "info")

    async def _on_clear_log(self, e):
        """清空日志"""
        self.log_container.controls.clear()
        self._add_log("日志已清空", "info")
        await self.app.page.update_async()

    async def _simulate_analysis(self):
        """模拟分析过程"""
        try:
            steps = [
                ("连接Chatlog服务...", 10),
                ("获取群组消息...", 25),
                ("模式匹配筛选...", 40),
                ("AI智能识别...", 60),
                ("提取用户档案...", 80),
                ("同步到飞书...", 95),
                ("分析完成!", 100)
            ]

            for step_name, progress in steps:
                if not self.is_analyzing:
                    break

                self._add_log(step_name, "info")
                await self._update_status(step_name, progress)
                await asyncio.sleep(1.5)  # 模拟处理时间

            if self.is_analyzing:
                self._add_log("分析任务完成！发现12个自我介绍，提取8个有效档案", "success")
                await self._update_status("分析完成", 100)

                # 重置按钮状态
                self.is_analyzing = False
                self.start_button.disabled = False
                self.stop_button.disabled = True

                await self.app.page.update_async()
                await self.app.show_message("分析完成", "成功分析群组消息，提取了8个用户档案", "success")

        except Exception as e:
            self._add_log(f"分析过程出错: {str(e)}", "error")
            await self._update_status("分析失败", 0)

            self.is_analyzing = False
            self.start_button.disabled = False
            self.stop_button.disabled = True

            await self.app.page.update_async()

    async def _update_status(self, status: str, progress: int):
        """更新状态和进度"""
        self.analysis_status = status
        self.current_progress = progress

        if self.progress_bar:
            self.progress_bar.value = progress / 100
        if self.progress_text:
            self.progress_text.value = f"{progress}%"
        if self.status_text:
            self.status_text.value = status

        await self.app.page.update_async()
