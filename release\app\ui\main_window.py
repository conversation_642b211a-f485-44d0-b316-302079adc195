"""
主窗口界面
包含配置、群组管理、分析和结果查看等功能标签页
"""

import asyncio
import threading
import tkinter as tk
from tkinter import messagebox
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import logging
from typing import Dict, Any

from ..services.analysis_service import AnalysisService
from .config_tab import ConfigTab
from .groups_tab import GroupsTab
from .analysis_tab import AnalysisTab
from .results_tab import ResultsTab

logger = logging.getLogger(__name__)

class MainWindow:
    """主窗口类"""
    
    def __init__(self, root: ttk.Window):
        self.root = root
        self.analysis_service = AnalysisService()
        
        # 设置进度回调
        self.analysis_service.set_progress_callback(self.on_progress_update)
        
        # 异步事件循环
        self.loop = None
        self.loop_thread = None
        
        # 创建界面
        self._create_widgets()
        self._setup_menu()
        self._start_async_loop()
        
        logger.info("主窗口初始化完成")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 创建主要容器
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # 创建标题
        title_label = ttk.Label(
            main_frame,
            text="微信群用户自我介绍分析器",
            font=("微软雅黑", 16, "bold"),
            bootstyle=PRIMARY
        )
        title_label.pack(pady=(0, 20))
        
        # 创建标签页控件
        self.notebook = ttk.Notebook(main_frame, bootstyle="info")
        self.notebook.pack(fill=BOTH, expand=True)
        
        # 创建各个标签页
        self.config_tab = ConfigTab(self.notebook, self.analysis_service)
        self.groups_tab = GroupsTab(self.notebook, self.analysis_service)
        self.analysis_tab = AnalysisTab(self.notebook, self.analysis_service)
        self.results_tab = ResultsTab(self.notebook, self.analysis_service)
        
        # 添加标签页到notebook
        self.notebook.add(self.config_tab.frame, text="📋 配置设置")
        self.notebook.add(self.groups_tab.frame, text="👥 群组管理")
        self.notebook.add(self.analysis_tab.frame, text="🔍 分析任务")
        self.notebook.add(self.results_tab.frame, text="📊 结果查看")
        
        # 状态栏
        self._create_status_bar(main_frame)
        
        # 绑定标签页切换事件
        self.notebook.bind("<<NotebookTabChanged>>", self.on_tab_changed)
    
    def _create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=X, pady=(10, 0))
        
        # 分隔线
        separator = ttk.Separator(status_frame, orient=HORIZONTAL)
        separator.pack(fill=X, pady=(0, 5))
        
        # 状态栏内容
        status_content = ttk.Frame(status_frame)
        status_content.pack(fill=X)
        
        # API连接状态
        self.api_status_frame = ttk.Frame(status_content)
        self.api_status_frame.pack(side=LEFT)
        
        self.api_status_labels = {}
        api_names = ["Chatlog", "DeepSeek", "Feishu"]
        for i, api_name in enumerate(api_names):
            label = ttk.Label(self.api_status_frame, text=f"{api_name}: 未连接", bootstyle="secondary")
            label.pack(side=LEFT, padx=(0, 15))
            self.api_status_labels[api_name.lower()] = label
        
        # 版本信息
        version_label = ttk.Label(status_content, text="v1.0.0", bootstyle="secondary")
        version_label.pack(side=RIGHT)
    
    def _setup_menu(self):
        """设置菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="导出配置", command=self.export_config)
        file_menu.add_command(label="导入配置", command=self.import_config)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="测试API连接", command=self.test_api_connections)
        tools_menu.add_command(label="清理日志", command=self.clean_logs)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def _start_async_loop(self):
        """启动异步事件循环"""
        def run_loop():
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            self.loop.run_forever()
        
        self.loop_thread = threading.Thread(target=run_loop, daemon=True)
        self.loop_thread.start()
        
        # 等待循环启动
        import time
        time.sleep(0.1)
    
    def run_async(self, coro):
        """在异步循环中运行协程"""
        if self.loop and not self.loop.is_closed():
            return asyncio.run_coroutine_threadsafe(coro, self.loop)
        else:
            logger.error("异步事件循环未运行")
            return None
    
    def on_tab_changed(self, event):
        """标签页切换事件处理"""
        try:
            selected_tab = self.notebook.select()
            tab_text = self.notebook.tab(selected_tab, "text")
            logger.debug(f"切换到标签页: {tab_text}")
            
            # 根据不同标签页执行相应操作
            if "群组管理" in tab_text:
                self.groups_tab.refresh_groups()
            elif "结果查看" in tab_text:
                self.results_tab.refresh_results()
                
        except Exception as e:
            logger.error(f"标签页切换处理失败: {e}")
    
    def on_progress_update(self, event: str, data: Dict[str, Any]):
        """处理进度更新事件"""
        try:
            # 在主线程中更新UI
            self.root.after(0, self._handle_progress_update, event, data)
        except Exception as e:
            logger.error(f"进度更新处理失败: {e}")
    
    def _handle_progress_update(self, event: str, data: Dict[str, Any]):
        """在主线程中处理进度更新"""
        try:
            logger.debug(f"进度更新: {event} - {data}")
            
            # 更新API状态
            if event == "api_test_completed":
                self._update_api_status(data)
            
            # 通知相关标签页更新
            if hasattr(self.analysis_tab, 'on_progress_update'):
                self.analysis_tab.on_progress_update(event, data)
            
            if hasattr(self.results_tab, 'on_progress_update'):
                self.results_tab.on_progress_update(event, data)
                
        except Exception as e:
            logger.error(f"UI进度更新失败: {e}")
    
    def _update_api_status(self, status_data: Dict[str, bool]):
        """更新API连接状态显示"""
        try:
            for api_name, is_connected in status_data.items():
                if api_name in self.api_status_labels:
                    label = self.api_status_labels[api_name]
                    if is_connected:
                        label.config(text=f"{api_name.title()}: 已连接", bootstyle="success")
                    else:
                        label.config(text=f"{api_name.title()}: 连接失败", bootstyle="danger")
        except Exception as e:
            logger.error(f"更新API状态显示失败: {e}")
    
    def test_api_connections(self):
        """测试API连接"""
        try:
            # 显示测试提示
            for label in self.api_status_labels.values():
                label.config(text="测试中...", bootstyle="warning")
            
            # 异步测试连接
            future = self.run_async(self.analysis_service.test_api_connections())
            if future:
                def on_complete(fut):
                    try:
                        result = fut.result()
                        self.root.after(0, self._update_api_status, result)
                    except Exception as e:
                        logger.error(f"API连接测试失败: {e}")
                        self.root.after(0, messagebox.showerror, "测试失败", f"API连接测试失败:\n{str(e)}")
                
                future.add_done_callback(on_complete)
        except Exception as e:
            logger.error(f"启动API连接测试失败: {e}")
            messagebox.showerror("测试失败", f"启动API连接测试失败:\n{str(e)}")
    
    def export_config(self):
        """导出配置"""
        try:
            from tkinter import filedialog
            import json
            from ..config import get_config
            
            filename = filedialog.asksaveasfilename(
                title="导出配置",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            
            if filename:
                config = get_config()
                config_dict = {
                    'ui': {
                        'theme': config.ui.theme,
                        'window_width': config.ui.window_width,
                        'window_height': config.ui.window_height
                    },
                    'analysis': {
                        'batch_size': config.analysis.batch_size,
                        'confidence_threshold': config.analysis.confidence_threshold,
                        'auto_detect': config.analysis.auto_detect
                    }
                }
                
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(config_dict, f, ensure_ascii=False, indent=2)
                
                messagebox.showinfo("导出成功", f"配置已导出到:\n{filename}")
        except Exception as e:
            logger.error(f"导出配置失败: {e}")
            messagebox.showerror("导出失败", f"导出配置失败:\n{str(e)}")
    
    def import_config(self):
        """导入配置"""
        try:
            from tkinter import filedialog
            import json
            
            filename = filedialog.askopenfilename(
                title="导入配置",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            
            if filename:
                with open(filename, 'r', encoding='utf-8') as f:
                    config_dict = json.load(f)
                
                # 更新配置（这里可以添加更详细的配置更新逻辑）
                messagebox.showinfo("导入成功", "配置导入成功，重启应用后生效")
        except Exception as e:
            logger.error(f"导入配置失败: {e}")
            messagebox.showerror("导入失败", f"导入配置失败:\n{str(e)}")
    
    def clean_logs(self):
        """清理日志文件"""
        try:
            from pathlib import Path
            import shutil
            
            result = messagebox.askyesno("确认清理", "确定要清理所有日志文件吗？")
            if result:
                log_dir = Path("app/data/logs")
                if log_dir.exists():
                    shutil.rmtree(log_dir)
                    log_dir.mkdir(parents=True, exist_ok=True)
                
                messagebox.showinfo("清理完成", "日志文件已清理完成")
        except Exception as e:
            logger.error(f"清理日志失败: {e}")
            messagebox.showerror("清理失败", f"清理日志失败:\n{str(e)}")
    
    def show_help(self):
        """显示帮助信息"""
        help_text = """
微信群用户自我介绍分析器 使用说明

1. 配置设置
   - 设置Chatlog API地址和DeepSeek API密钥
   - 配置飞书多维表格信息
   - 调整分析参数

2. 群组管理
   - 查看可用的微信群组
   - 选择要分析的群组

3. 分析任务
   - 启动分析任务
   - 监控分析进度
   - 查看实时状态

4. 结果查看
   - 查看提取的用户画像
   - 导出分析结果
   - 管理数据同步

如需更多帮助，请查看项目文档。
        """
        
        messagebox.showinfo("使用说明", help_text)
    
    def show_about(self):
        """显示关于信息"""
        about_text = """
微信群用户自我介绍分析器 v1.0.0

一个基于AI的微信群用户画像自动分析工具

技术栈：
- Python 3.8+
- ttkbootstrap (现代化UI)
- LlamaIndex + DeepSeek (AI分析)
- 飞书多维表格 (数据存储)

开发者：Assistant
        """
        
        messagebox.showinfo("关于", about_text) 