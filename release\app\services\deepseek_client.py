"""
DeepSeek API 客户端
负责与DeepSeek API通信，进行AI智能分析和用户画像提取
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any, Optional, List
import logging

from llama_index.core import Settings
from llama_index.llms.openai_like import OpenAILike

from ..models import UserProfile, ProfileField
from ..config import get_config

logger = logging.getLogger(__name__)

class DeepSeekClient:
    """DeepSeek API客户端"""
    
    def __init__(self):
        self.config = get_config().deepseek
        self.llm = None
        self._initialize_llm()
    
    def _initialize_llm(self):
        """初始化LLM客户端"""
        try:
            self.llm = OpenAILike(
                api_key=self.config.api_key,
                api_base=self.config.base_url,
                model=self.config.model,
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature,
                timeout=self.config.timeout
            )
            Settings.llm = self.llm
            logger.info("DeepSeek客户端初始化成功")
        except Exception as e:
            logger.error(f"DeepSeek客户端初始化失败: {e}")
    
    async def test_connection(self) -> bool:
        """测试API连接"""
        try:
            test_prompt = "请回复'连接成功'"
            response = await self.llm.acomplete(test_prompt)
            return "连接成功" in str(response) or len(str(response)) > 0
        except Exception as e:
            logger.error(f"测试DeepSeek连接失败: {e}")
            return False
    
    async def detect_introduction(self, message_content: str) -> Dict[str, Any]:
        """
        检测消息是否为自我介绍
        
        Args:
            message_content: 消息内容
            
        Returns:
            包含检测结果的字典
        """
        try:
            prompt = self._build_detection_prompt(message_content)
            response = await self.llm.acomplete(prompt)
            
            # 解析响应
            result = self._parse_detection_response(str(response))
            return result
            
        except Exception as e:
            logger.error(f"检测自我介绍失败: {e}")
            return {
                'is_introduction': False,
                'confidence': 0.0,
                'reason': f'AI检测失败: {str(e)}'
            }
    
    async def extract_profile(self, message_content: str) -> Dict[str, Any]:
        """
        从自我介绍中提取用户画像
        
        Args:
            message_content: 自我介绍内容
            
        Returns:
            提取的用户画像信息
        """
        try:
            prompt = self._build_extraction_prompt(message_content)
            response = await self.llm.acomplete(prompt)
            
            # 解析响应
            result = self._parse_extraction_response(str(response))
            return result
            
        except Exception as e:
            logger.error(f"提取用户画像失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'profile': {}
            }
    
    def _build_detection_prompt(self, message_content: str) -> str:
        """构建自我介绍检测提示词"""
        prompt = f"""
你是一个专业的文本分析专家，需要判断以下消息是否为自我介绍。

自我介绍的特征：
1. 包含个人基本信息（姓名、职业、工作等）
2. 介绍自己的技能、经验或专业领域
3. 说明自己能提供什么帮助或服务
4. 表达希望得到什么帮助或寻求什么机会
5. 使用第一人称进行自我描述

请分析以下消息：
=================
{message_content}
=================

请严格按照以下JSON格式回复：
{{
    "is_introduction": true/false,
    "confidence": 0.0-1.0,
    "reason": "判断理由"
}}

注意：
- is_introduction: 是否为自我介绍（布尔值）
- confidence: 置信度（0.0-1.0）
- reason: 详细的判断理由
"""
        return prompt
    
    def _build_extraction_prompt(self, message_content: str) -> str:
        """构建用户画像提取提示词"""
        prompt = f"""
你是一个专业的信息提取专家，需要从自我介绍中提取结构化的用户画像信息。

请从以下自我介绍中提取信息：
=================
{message_content}
=================

请严格按照以下JSON格式回复：
{{
    "success": true/false,
    "confidence": 0.0-1.0,
    "profile": {{
        "nickname": "用户昵称或姓名",
        "profession": "职业或工作岗位",
        "personal_intro": "个人简介或背景描述",
        "can_provide": "能够提供的帮助或服务",
        "looking_for": "寻求的帮助或机会",
        "industry": "所属行业或领域"
    }}
}}

提取规则：
1. 如果某个字段没有明确信息，设置为null
2. 尽量保持原文的表达方式，适当精简
3. profession应该是具体的职业名称
4. industry应该是行业分类
5. can_provide和looking_for要具体明确
6. confidence表示整体提取的置信度

注意：
- 只提取明确表达的信息，不要推测
- 保持信息的准确性和完整性
- 如果无法提取有效信息，success设为false
"""
        return prompt
    
    def _parse_detection_response(self, response: str) -> Dict[str, Any]:
        """解析检测响应"""
        try:
            # 尝试从响应中提取JSON
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                result = json.loads(json_str)
                
                # 验证必要字段
                if 'is_introduction' in result and 'confidence' in result:
                    return {
                        'is_introduction': bool(result['is_introduction']),
                        'confidence': float(result['confidence']),
                        'reason': result.get('reason', ''),
                        'raw_response': response
                    }
            
            # 如果JSON解析失败，使用关键词检测
            return self._fallback_detection(response)
            
        except Exception as e:
            logger.error(f"解析检测响应失败: {e}")
            return self._fallback_detection(response)
    
    def _parse_extraction_response(self, response: str) -> Dict[str, Any]:
        """解析提取响应"""
        try:
            # 尝试从响应中提取JSON
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                result = json.loads(json_str)
                
                if 'profile' in result:
                    return {
                        'success': result.get('success', True),
                        'confidence': result.get('confidence', 0.5),
                        'profile': result['profile'],
                        'raw_response': response
                    }
            
            # 如果JSON解析失败
            return {
                'success': False,
                'error': 'JSON解析失败',
                'profile': {},
                'raw_response': response
            }
            
        except Exception as e:
            logger.error(f"解析提取响应失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'profile': {},
                'raw_response': response
            }
    
    def _fallback_detection(self, response: str) -> Dict[str, Any]:
        """回退检测方法"""
        # 基于关键词的简单检测
        keywords = ['我是', '我叫', '我的工作', '我来自', '我做', '我能', '我希望', '我寻求']
        confidence = 0.3
        
        for keyword in keywords:
            if keyword in response:
                confidence += 0.1
        
        is_introduction = confidence > 0.5
        
        return {
            'is_introduction': is_introduction,
            'confidence': min(confidence, 1.0),
            'reason': '使用回退检测方法',
            'raw_response': response
        }

# 同步版本的客户端
class SyncDeepSeekClient:
    """同步版本的DeepSeek客户端"""
    
    def __init__(self):
        self.config = get_config().deepseek
        self.llm = None
        self._initialize_llm()
    
    def _initialize_llm(self):
        """初始化LLM客户端"""
        try:
            self.llm = OpenAILike(
                api_key=self.config.api_key,
                api_base=self.config.base_url,
                model=self.config.model,
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature,
                timeout=self.config.timeout
            )
            logger.info("同步DeepSeek客户端初始化成功")
        except Exception as e:
            logger.error(f"同步DeepSeek客户端初始化失败: {e}")
    
    def test_connection(self) -> bool:
        """测试API连接"""
        try:
            test_prompt = "请回复'连接成功'"
            response = self.llm.complete(test_prompt)
            return "连接成功" in str(response) or len(str(response)) > 0
        except Exception as e:
            logger.error(f"测试DeepSeek连接失败: {e}")
            return False
    
    def detect_introduction(self, message_content: str) -> Dict[str, Any]:
        """检测消息是否为自我介绍"""
        try:
            client = DeepSeekClient()
            # 构建检测提示词
            prompt = client._build_detection_prompt(message_content)
            response = self.llm.complete(prompt)
            
            # 解析响应
            result = client._parse_detection_response(str(response))
            return result
            
        except Exception as e:
            logger.error(f"检测自我介绍失败: {e}")
            return {
                'is_introduction': False,
                'confidence': 0.0,
                'reason': f'AI检测失败: {str(e)}'
            } 