[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "wechat-profile-analyzer"
version = "1.0.2"
description = "微信群用户自我介绍分析器 - 基于AI的用户画像自动分析工具"
readme = "README.md"
requires-python = ">=3.9"
license = {text = "MIT"}
authors = [
    {name = "Assistant", email = "<EMAIL>"},
]
keywords = ["wechat", "ai", "profile", "analysis", "llm"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

dependencies = [
    # 图形界面
    "ttkbootstrap>=1.10.1",
    
    # 数据处理
    "pandas>=2.0.0",
    "openpyxl>=3.1.0",
    
    # HTTP客户端
    "requests>=2.31.0",
    "aiohttp>=3.9.0",
    
    # 配置管理
    "python-dotenv>=1.0.0",
    
    # 日志处理
    "python-dateutil>=2.8.0",
]

[project.optional-dependencies]
# AI相关依赖（可选安装）
ai = [
    "llama-index-core>=0.11.0,<0.13.0",
    "llama-index-llms-deepseek>=0.1.0,<0.2.0",
]

# 开发依赖
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
]

# 构建和打包
build = [
    "pyinstaller>=6.0.0",
    "wheel>=0.41.0",
    "build>=0.10.0",
]

# 完整安装（包含所有功能）
full = [
    "wechat-profile-analyzer[ai,dev,build]"
]

[project.urls]
Homepage = "https://github.com/your-username/wechat-profile-analyzer"
Documentation = "https://github.com/your-username/wechat-profile-analyzer#readme"
Repository = "https://github.com/your-username/wechat-profile-analyzer.git"
Issues = "https://github.com/your-username/wechat-profile-analyzer/issues"

[project.scripts]
wechat-analyzer = "app.main:main"
wechat-profile-analyzer = "app.main:main"

[tool.uv]
dev-dependencies = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
]

[tool.hatch.build.targets.wheel]
packages = ["app"]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
exclude = '''
/(
    \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["app"]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "ttkbootstrap.*",
    "llama_index.*",
    "pandas.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers"
testpaths = [
    "tests",
]
pythonpath = [
    "."
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "ui: marks tests as UI tests",
]
