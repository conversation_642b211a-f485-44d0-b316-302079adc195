# 使用说明

## 安装和启动

### Windows用户
1. 双击运行 `安装依赖.bat` 安装依赖
2. 双击运行 `启动应用.bat` 启动程序

### Linux/Mac用户
1. 运行 `chmod +x start.sh` 设置权限
2. 运行 `./start.sh` 启动程序

### 手动运行
```bash
# 安装依赖
uv sync

# 安装AI功能（可选）
uv sync --extra ai

# 启动程序
uv run python app/main.py
```

## 开发模式

```bash
# 安装开发依赖
uv sync --extra dev

# 运行测试
uv run pytest

# 代码格式化
uv run black app/
uv run isort app/

# 代码检查
uv run flake8 app/
```

## 配置

1. 复制 `env.example` 为 `.env`
2. 在 `.env` 中填入你的API密钥
3. 在应用的"配置设置"标签页中完成配置

详细说明请参考 README.md
