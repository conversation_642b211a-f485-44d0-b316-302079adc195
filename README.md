# 微信群自我介绍分析工具

一款智能化的用户画像分析软件，无需编程知识，通过简单的图形界面操作即可完成微信群成员自我介绍的智能分析。

## 🚀 主要功能

- **智能识别**：自动识别群组中的自我介绍消息
- **用户画像**：提取职业、技能、需求等关键信息  
- **数据导出**：支持Excel和JSON格式导出
- **飞书同步**：可选择同步到飞书多维表格
- **图形界面**：完全通过点击操作，无需编写代码

## 📥 快速开始

### 方式一：直接运行（推荐）

1. 下载 `微信群分析工具.exe` 
2. 双击运行程序
3. 按照界面向导完成配置
4. 开始分析群组数据

### 方式二：Python源码运行

如果您有Python环境，也可以：

1. 下载源码到本地
2. 安装依赖：`pip install -r requirements.txt`
3. 选择界面版本运行：
   - **智能选择器**：`python start_ui.py` (推荐)
   - **现代化界面**：`python app/main_flet.py`
   - **经典界面**：`python app/main.py`

## 🔧 使用说明

### 🎯 首次使用

启动程序后会显示**欢迎向导**，按以下步骤操作：

1. **配置AI服务**
   - 获取DeepSeek API密钥（访问 https://platform.deepseek.com）
   - 在向导中填入API密钥

2. **配置聊天记录服务**  
   - 确保微信聊天记录导出工具已运行
   - 填入服务地址（默认：http://127.0.0.1:5030）

3. **配置飞书同步（可选）**
   - 创建飞书应用获取凭证
   - 填入应用ID、密钥和表格Token

### 📱 基本操作

1. **群组管理**：查看和选择要分析的微信群组
2. **分析任务**：启动智能分析，查看实时进度
3. **分析结果**：查看用户画像，导出Excel/JSON数据
4. **配置设置**：管理所有配置，测试API连接

## ✨ 特色功能

### 🎨 双界面选择
- **现代化界面 (Flet)**：Material Design风格，美观易用
- **经典界面 (tkinter)**：传统桌面应用，稳定可靠
- **智能选择器**：自动检测依赖，引导用户选择合适界面

### 🧠 智能配置向导
- 一键配置所有必要参数
- 详细的步骤指导和说明
- 实时连接测试和状态显示

### 📊 可视化界面
- 现代化的图形界面设计
- 实时进度显示和统计信息
- 直观的数据筛选和搜索功能

### 🔒 用户友好
- 无需编程知识
- 完整的中文界面
- 详细的错误提示和解决方案

## 💰 费用说明

本工具本身**完全免费**，但需要使用AI服务：

- **DeepSeek API**：按使用量计费，分析1000条消息约0.1-0.5元
- **新用户福利**：通常有免费额度可以试用
- **费用透明**：所有费用由AI服务商收取，本工具不收取任何费用

## 📖 详细文档

- [用户使用手册](docs/用户使用手册.md) - 详细的图文教程
- [飞书配置指南](docs/feishu_setup.md) - 飞书同步设置说明
- [常见问题解答](docs/FAQ.md) - 故障排除指南

## 🛠️ 系统要求

- **操作系统**：Windows 10/11 64位
- **内存**：至少4GB RAM  
- **硬盘**：至少500MB可用空间
- **网络**：稳定的互联网连接

## 📞 技术支持

遇到问题时：

1. **查看内置帮助**：点击软件中的"帮助"菜单
2. **查看日志文件**：`app/data/logs/app.log`
3. **阅读使用手册**：详细的故障排除指南
4. **问题反馈**：提供详细的错误描述和日志信息

## 🔄 更新日志

### v2.0.0 (最新)
- ✅ 全新现代化Flet界面
- ✅ Material Design设计风格
- ✅ 响应式布局和流畅动画
- ✅ 双界面选择 (Flet + tkinter)
- ✅ 智能启动选择器
- ✅ 优化的用户体验

### v1.0.0
- ✅ 完整的GUI配置界面
- ✅ 智能配置向导
- ✅ AI自我介绍识别
- ✅ 用户画像提取
- ✅ Excel/JSON数据导出
- ✅ 飞书多维表格同步
- ✅ 实时进度监控
- ✅ 完整的中文界面

## ⚠️ 重要提示

1. **隐私保护**：请妥善保管API密钥等敏感信息
2. **数据合规**：确保微信数据使用符合相关法规
3. **备份数据**：重要分析结果请及时导出备份
4. **网络安全**：建议在安全的网络环境中使用

---

**感谢使用微信群自我介绍分析工具！** 🎉

*本工具专为技术小白设计，所有功能均可通过图形界面完成，无需编程知识。如有疑问，请优先查看软件内置的帮助文档。* 