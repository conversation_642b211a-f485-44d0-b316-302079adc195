"""
Chatlog API 客户端
负责与Chatlog HTTP API通信，获取微信群聊数据
"""

import asyncio
import aiohttp
import requests
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import logging
from urllib.parse import urljoin

from ..models import Message, ChatGroup, MessageType
from ..config import get_config

logger = logging.getLogger(__name__)

class ChatlogClient:
    """Chatlog API客户端"""
    
    def __init__(self):
        self.config = get_config().chatlog
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        connector = aiohttp.TCPConnector(limit=20, limit_per_host=10)
        timeout = aiohttp.ClientTimeout(total=self.config.timeout)
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={'Content-Type': 'application/json'}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        if self.session:
            await self.session.close()
    
    def _build_url(self, endpoint: str) -> str:
        """构建完整的API URL"""
        return urljoin(self.config.base_url.rstrip('/') + '/', endpoint.lstrip('/'))
    
    async def test_connection(self) -> bool:
        """测试API连接"""
        try:
            url = self._build_url('/api/health')
            async with self.session.get(url) as response:
                return response.status == 200
        except Exception as e:
            logger.error(f"测试Chatlog连接失败: {e}")
            return False
    
    async def get_chat_groups(self) -> List[ChatGroup]:
        """获取所有群组列表"""
        try:
            url = self._build_url('/api/chats')
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    groups = []
                    for item in data.get('chats', []):
                        group = ChatGroup(
                            id=str(item['id']),
                            name=item.get('name', '未知群组'),
                            description=item.get('description', ''),
                            member_count=item.get('member_count', 0),
                            is_active=item.get('is_active', True),
                            last_message_time=self._parse_datetime(item.get('last_message_time')),
                            created_time=self._parse_datetime(item.get('created_time'))
                        )
                        groups.append(group)
                    logger.info(f"获取到 {len(groups)} 个群组")
                    return groups
                else:
                    logger.error(f"获取群组列表失败: HTTP {response.status}")
                    return []
        except Exception as e:
            logger.error(f"获取群组列表异常: {e}")
            return []
    
    async def get_chat_messages(
        self, 
        chat_id: str, 
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 1000,
        offset: int = 0
    ) -> List[Message]:
        """获取群组消息"""
        try:
            url = self._build_url(f'/api/chats/{chat_id}/messages')
            params = {
                'limit': limit,
                'offset': offset
            }
            
            if start_time:
                params['start_time'] = start_time.isoformat()
            if end_time:
                params['end_time'] = end_time.isoformat()
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    messages = []
                    for item in data.get('messages', []):
                        message = self._parse_message(item, chat_id)
                        if message:
                            messages.append(message)
                    logger.info(f"获取到群组 {chat_id} 的 {len(messages)} 条消息")
                    return messages
                else:
                    logger.error(f"获取群组消息失败: HTTP {response.status}")
                    return []
        except Exception as e:
            logger.error(f"获取群组消息异常: {e}")
            return []
    
    async def get_messages_in_batches(
        self,
        chat_id: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        batch_size: int = 1000
    ) -> List[Message]:
        """批量获取消息"""
        all_messages = []
        offset = 0
        
        while True:
            messages = await self.get_chat_messages(
                chat_id=chat_id,
                start_time=start_time,
                end_time=end_time,
                limit=batch_size,
                offset=offset
            )
            
            if not messages:
                break
            
            all_messages.extend(messages)
            offset += len(messages)
            
            # 如果返回的消息数少于批次大小，说明已经获取完毕
            if len(messages) < batch_size:
                break
            
            # 添加延迟避免API限制
            await asyncio.sleep(0.1)
        
        logger.info(f"总共获取到 {len(all_messages)} 条消息")
        return all_messages
    
    async def get_user_info(self, user_id: str) -> Optional[Dict[str, Any]]:
        """获取用户信息"""
        try:
            url = self._build_url(f'/api/users/{user_id}')
            async with self.session.get(url) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logger.warning(f"获取用户信息失败: HTTP {response.status}")
                    return None
        except Exception as e:
            logger.error(f"获取用户信息异常: {e}")
            return None
    
    def _parse_message(self, data: Dict[str, Any], chat_id: str) -> Optional[Message]:
        """解析消息数据"""
        try:
            message_type = MessageType.TEXT  # 默认为文本消息
            
            # 根据消息内容类型确定消息类型
            if data.get('type'):
                type_mapping = {
                    'text': MessageType.TEXT,
                    'image': MessageType.IMAGE,
                    'file': MessageType.FILE,
                    'system': MessageType.SYSTEM
                }
                message_type = type_mapping.get(data['type'], MessageType.TEXT)
            
            message = Message(
                id=str(data['id']),
                chat_id=chat_id,
                user_id=str(data.get('user_id', '')),
                username=data.get('username', ''),
                content=data.get('content', ''),
                message_type=message_type,
                timestamp=self._parse_datetime(data.get('timestamp')),
                reply_to=str(data.get('reply_to')) if data.get('reply_to') else None,
                media_url=data.get('media_url'),
                raw_data=data
            )
            return message
        except Exception as e:
            logger.error(f"解析消息数据失败: {e}, 数据: {data}")
            return None
    
    def _parse_datetime(self, timestamp_str: Optional[str]) -> Optional[datetime]:
        """解析时间戳字符串"""
        if not timestamp_str:
            return None
        
        try:
            # 尝试不同的时间格式
            formats = [
                '%Y-%m-%dT%H:%M:%S.%fZ',  # ISO格式带微秒
                '%Y-%m-%dT%H:%M:%SZ',     # ISO格式
                '%Y-%m-%d %H:%M:%S',       # 标准格式
                '%Y-%m-%d',                # 仅日期
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(timestamp_str, fmt)
                except ValueError:
                    continue
            
            # 如果是时间戳
            if timestamp_str.isdigit():
                timestamp = int(timestamp_str)
                # 判断是秒还是毫秒
                if timestamp > 1e10:  # 毫秒时间戳
                    timestamp = timestamp / 1000
                return datetime.fromtimestamp(timestamp)
                
        except Exception as e:
            logger.warning(f"解析时间戳失败: {timestamp_str}, 错误: {e}")
        
        return None

# 同步版本的客户端（用于非异步环境）
class SyncChatlogClient:
    """同步版本的Chatlog客户端"""
    
    def __init__(self):
        self.config = get_config().chatlog
        self.session = requests.Session()
        self.session.headers.update({'Content-Type': 'application/json'})
    
    def _build_url(self, endpoint: str) -> str:
        """构建完整的API URL"""
        return urljoin(self.config.base_url.rstrip('/') + '/', endpoint.lstrip('/'))
    
    def test_connection(self) -> bool:
        """测试API连接"""
        try:
            url = self._build_url('/api/health')
            response = self.session.get(url, timeout=self.config.timeout)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"测试Chatlog连接失败: {e}")
            return False
    
    def get_chat_groups(self) -> List[ChatGroup]:
        """获取所有群组列表"""
        try:
            url = self._build_url('/api/chats')
            response = self.session.get(url, timeout=self.config.timeout)
            
            if response.status_code == 200:
                data = response.json()
                groups = []
                for item in data.get('chats', []):
                    group = ChatGroup(
                        id=str(item['id']),
                        name=item.get('name', '未知群组'),
                        description=item.get('description', ''),
                        member_count=item.get('member_count', 0),
                        is_active=item.get('is_active', True)
                    )
                    groups.append(group)
                return groups
            else:
                logger.error(f"获取群组列表失败: HTTP {response.status_code}")
                return []
        except Exception as e:
            logger.error(f"获取群组列表异常: {e}")
            return [] 