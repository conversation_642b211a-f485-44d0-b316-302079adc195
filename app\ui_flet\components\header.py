"""
顶部标题栏组件
显示当前页面标题和快捷操作
"""

import flet as ft
from datetime import datetime
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ..main_app import FletMainApp

class Header:
    """顶部标题栏组件"""
    
    def __init__(self, app: 'FletMainApp'):
        self.app = app
        self.title_text = None
        self.status_text = None
        self.time_text = None
    
    def build(self) -> ft.Container:
        """构建标题栏"""
        
        # 当前时间
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M")
        
        # 标题文本
        self.title_text = ft.Text(
            "📊 仪表盘",
            size=24,
            weight=ft.FontWeight.BOLD,
            color=ft.colors.GREY_800
        )
        
        # 状态指示器
        self.status_text = ft.Text(
            "● 就绪",
            size=14,
            color=ft.colors.GREEN
        )
        
        # 时间显示
        self.time_text = ft.Text(
            current_time,
            size=14,
            color=ft.colors.GREY_600
        )
        
        # 快捷操作按钮
        quick_actions = ft.Row(
            controls=[
                # 刷新按钮
                ft.IconButton(
                    icon=ft.icons.REFRESH,
                    tooltip="刷新",
                    on_click=self._on_refresh_click,
                    icon_color=ft.colors.GREY_600,
                    bgcolor=ft.colors.GREY_100,
                    style=ft.ButtonStyle(
                        shape=ft.CircleBorder()
                    )
                ),
                
                # 帮助按钮
                ft.IconButton(
                    icon=ft.icons.HELP_OUTLINE,
                    tooltip="帮助",
                    on_click=self._on_help_click,
                    icon_color=ft.colors.GREY_600,
                    bgcolor=ft.colors.GREY_100,
                    style=ft.ButtonStyle(
                        shape=ft.CircleBorder()
                    )
                ),
                
                # 设置按钮
                ft.IconButton(
                    icon=ft.icons.SETTINGS,
                    tooltip="设置",
                    on_click=self._on_settings_click,
                    icon_color=ft.colors.GREY_600,
                    bgcolor=ft.colors.GREY_100,
                    style=ft.ButtonStyle(
                        shape=ft.CircleBorder()
                    )
                )
            ],
            spacing=8
        )
        
        # 标题栏布局
        header_content = ft.Row(
            controls=[
                # 左侧：标题
                ft.Column(
                    controls=[
                        self.title_text,
                        ft.Row(
                            controls=[
                                self.status_text,
                                ft.Text("•", color=ft.colors.GREY_400),
                                self.time_text
                            ],
                            spacing=8
                        )
                    ],
                    spacing=5,
                    expand=True
                ),
                
                # 右侧：快捷操作
                quick_actions
            ],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
            vertical_alignment=ft.CrossAxisAlignment.CENTER
        )
        
        return ft.Container(
            content=header_content,
            padding=ft.padding.symmetric(horizontal=30, vertical=20),
            bgcolor=ft.colors.WHITE,
            border=ft.border.only(bottom=ft.BorderSide(1, ft.colors.GREY_200)),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=4,
                color=ft.colors.BLACK12,
                offset=ft.Offset(0, 2)
            )
        )
    
    async def update_title(self, title: str):
        """更新标题"""
        if self.title_text:
            self.title_text.value = title
            await self.app.page.update_async()
    
    async def update_status(self, status: str, color: str = ft.colors.GREEN):
        """更新状态"""
        if self.status_text:
            self.status_text.value = f"● {status}"
            self.status_text.color = color
            await self.app.page.update_async()
    
    async def update_time(self):
        """更新时间显示"""
        if self.time_text:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M")
            self.time_text.value = current_time
            await self.app.page.update_async()
    
    async def _on_refresh_click(self, e):
        """刷新按钮点击事件"""
        await self.update_status("刷新中...", ft.colors.ORANGE)
        
        # 模拟刷新操作
        import asyncio
        await asyncio.sleep(1)
        
        await self.update_status("就绪", ft.colors.GREEN)
        await self.update_time()
        
        # 显示刷新完成消息
        await self.app.show_message("刷新完成", "页面数据已刷新", "success")
    
    async def _on_help_click(self, e):
        """帮助按钮点击事件"""
        help_content = """
🔧 使用指南：

1. 📱 群组管理：查看和选择要分析的微信群组
2. 🚀 分析任务：启动智能分析，查看实时进度  
3. 📊 分析结果：查看用户画像，导出数据
4. ⚙️ 配置设置：管理API密钥和系统设置

💡 提示：
• 首次使用需要配置DeepSeek API密钥
• 确保Chatlog服务正常运行
• 分析结果可导出为Excel或同步到飞书
        """
        
        await self.app.show_message("使用帮助", help_content.strip(), "info")
    
    async def _on_settings_click(self, e):
        """设置按钮点击事件"""
        await self.app.navigate_to("settings")
