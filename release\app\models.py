"""
数据模型定义
定义应用程序中使用的所有数据结构，包括用户画像、群组信息、消息等
"""

from dataclasses import dataclass, field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class MessageType(Enum):
    """消息类型"""
    TEXT = "text"
    IMAGE = "image"
    FILE = "file"
    SYSTEM = "system"

class IntroductionStatus(Enum):
    """自我介绍处理状态"""
    PENDING = "pending"  # 待处理
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"  # 失败
    SKIPPED = "skipped"  # 跳过

class ProfileField(Enum):
    """用户画像字段"""
    NICKNAME = "nickname"  # 昵称
    PROFESSION = "profession"  # 职业
    PERSONAL_INTRO = "personal_intro"  # 个人介绍
    CAN_PROVIDE = "can_provide"  # 能够提供
    LOOKING_FOR = "looking_for"  # 寻求帮助
    INDUSTRY = "industry"  # 行业领域

@dataclass
class Message:
    """消息数据模型"""
    id: str  # 消息ID
    chat_id: str  # 聊天ID（群组ID）
    user_id: str  # 用户ID
    username: str  # 用户名
    content: str  # 消息内容
    message_type: MessageType  # 消息类型
    timestamp: datetime  # 发送时间
    reply_to: Optional[str] = None  # 回复的消息ID
    media_url: Optional[str] = None  # 媒体文件URL
    raw_data: Dict[str, Any] = field(default_factory=dict)  # 原始数据

@dataclass
class ChatGroup:
    """微信群组数据模型"""
    id: str  # 群组ID
    name: str  # 群组名称
    description: Optional[str] = None  # 群组描述
    member_count: int = 0  # 成员数量
    is_active: bool = True  # 是否活跃
    last_message_time: Optional[datetime] = None  # 最后消息时间
    created_time: Optional[datetime] = None  # 创建时间
    settings: Dict[str, Any] = field(default_factory=dict)  # 群组设置

@dataclass
class UserProfile:
    """用户画像数据模型"""
    id: str  # 唯一标识
    user_id: str  # 用户ID
    chat_id: str  # 群组ID
    message_id: str  # 对应的消息ID
    nickname: Optional[str] = None  # 昵称
    profession: Optional[str] = None  # 职业
    personal_intro: Optional[str] = None  # 个人介绍
    can_provide: Optional[str] = None  # 能够提供
    looking_for: Optional[str] = None  # 寻求帮助
    industry: Optional[str] = None  # 行业领域
    confidence_score: float = 0.0  # 提取置信度
    status: IntroductionStatus = IntroductionStatus.PENDING  # 处理状态
    created_time: datetime = field(default_factory=datetime.now)  # 创建时间
    updated_time: datetime = field(default_factory=datetime.now)  # 更新时间
    raw_content: str = ""  # 原始消息内容
    extraction_log: List[Dict[str, Any]] = field(default_factory=list)  # 提取日志
    feishu_record_id: Optional[str] = None  # 飞书记录ID
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'chat_id': self.chat_id,
            'message_id': self.message_id,
            'nickname': self.nickname,
            'profession': self.profession,
            'personal_intro': self.personal_intro,
            'can_provide': self.can_provide,
            'looking_for': self.looking_for,
            'industry': self.industry,
            'confidence_score': self.confidence_score,
            'status': self.status.value,
            'created_time': self.created_time.isoformat(),
            'updated_time': self.updated_time.isoformat(),
            'raw_content': self.raw_content,
            'feishu_record_id': self.feishu_record_id
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserProfile':
        """从字典创建实例"""
        return cls(
            id=data['id'],
            user_id=data['user_id'],
            chat_id=data['chat_id'],
            message_id=data['message_id'],
            nickname=data.get('nickname'),
            profession=data.get('profession'),
            personal_intro=data.get('personal_intro'),
            can_provide=data.get('can_provide'),
            looking_for=data.get('looking_for'),
            industry=data.get('industry'),
            confidence_score=data.get('confidence_score', 0.0),
            status=IntroductionStatus(data.get('status', 'pending')),
            created_time=datetime.fromisoformat(data['created_time']),
            updated_time=datetime.fromisoformat(data['updated_time']),
            raw_content=data.get('raw_content', ''),
            feishu_record_id=data.get('feishu_record_id')
        )

@dataclass
class IntroductionCandidate:
    """自我介绍候选项"""
    message: Message  # 消息对象
    is_introduction: bool = False  # 是否为自我介绍
    confidence: float = 0.0  # 置信度
    detection_method: str = ""  # 检测方法（pattern/ai）
    reason: str = ""  # 判断理由
    status: IntroductionStatus = IntroductionStatus.PENDING  # 处理状态

@dataclass
class AnalysisTask:
    """分析任务"""
    id: str  # 任务ID
    chat_id: str  # 群组ID
    start_time: datetime  # 开始时间
    end_time: Optional[datetime] = None  # 结束时间
    total_messages: int = 0  # 总消息数
    processed_messages: int = 0  # 已处理消息数
    found_introductions: int = 0  # 找到的自我介绍数
    extracted_profiles: int = 0  # 提取的用户画像数
    synced_to_feishu: int = 0  # 同步到飞书的数量
    status: str = "running"  # 任务状态
    error_messages: List[str] = field(default_factory=list)  # 错误信息
    
    @property
    def progress(self) -> float:
        """获取进度百分比"""
        if self.total_messages == 0:
            return 0.0
        return (self.processed_messages / self.total_messages) * 100

@dataclass
class FeishuTableConfig:
    """飞书多维表格配置"""
    app_token: str  # 多维表格app_token
    table_id: str  # 表格ID
    view_id: Optional[str] = None  # 视图ID
    field_mapping: Dict[str, str] = field(default_factory=dict)  # 字段映射
    
    def get_field_id(self, profile_field: ProfileField) -> Optional[str]:
        """获取画像字段对应的飞书字段ID"""
        return self.field_mapping.get(profile_field.value)

@dataclass
class SyncRecord:
    """同步记录"""
    id: str  # 记录ID
    profile_id: str  # 用户画像ID
    feishu_record_id: str  # 飞书记录ID
    sync_time: datetime  # 同步时间
    sync_status: str  # 同步状态
    error_message: Optional[str] = None  # 错误信息

@dataclass
class AppState:
    """应用程序状态"""
    current_chat_id: Optional[str] = None  # 当前选中的群组ID
    is_analyzing: bool = False  # 是否正在分析
    current_task: Optional[AnalysisTask] = None  # 当前任务
    last_sync_time: Optional[datetime] = None  # 最后同步时间
    api_status: Dict[str, str] = field(default_factory=dict)  # API状态
    
    def update_api_status(self, api_name: str, status: str):
        """更新API状态"""
        self.api_status[api_name] = status 