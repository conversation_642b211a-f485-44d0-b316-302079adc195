"""
群组管理页面
显示和管理微信群组
"""

import flet as ft
from typing import TYPE_CHECKING, List, Dict, Any
import asyncio

if TYPE_CHECKING:
    from ..main_app import FletMainApp

class GroupsPage:
    """群组管理页面"""
    
    def __init__(self, app: 'FletMainApp'):
        self.app = app
        self.groups_data = []
        self.selected_groups = set()
        self.search_query = ""
        self.groups_list = None
        self.search_field = None
        self.refresh_button = None
    
    def build(self) -> ft.Column:
        """构建群组管理页面"""
        
        # 顶部操作栏
        top_bar = self._create_top_bar()
        
        # 群组列表
        self.groups_list = ft.Column(
            controls=[],
            spacing=8,
            scroll=ft.ScrollMode.AUTO
        )
        
        # 群组列表容器
        groups_container = ft.Container(
            content=self.groups_list,
            padding=ft.padding.all(20),
            bgcolor=ft.colors.WHITE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.colors.BLACK12,
                offset=ft.Offset(0, 4)
            ),
            expand=True
        )
        
        # 底部操作栏
        bottom_bar = self._create_bottom_bar()
        
        # 加载群组数据
        asyncio.create_task(self._load_groups())
        
        return ft.Column(
            controls=[
                top_bar,
                ft.Container(height=20),
                groups_container,
                ft.Container(height=20),
                bottom_bar
            ],
            spacing=0,
            expand=True
        )
    
    def _create_top_bar(self) -> ft.Container:
        """创建顶部操作栏"""
        
        # 搜索框
        self.search_field = ft.TextField(
            hint_text="搜索群组名称...",
            prefix_icon=ft.icons.SEARCH,
            border_radius=25,
            bgcolor=ft.colors.GREY_100,
            border_color=ft.colors.TRANSPARENT,
            focused_border_color=ft.colors.BLUE,
            width=300,
            height=45,
            on_change=self._on_search_change
        )
        
        # 刷新按钮
        self.refresh_button = ft.ElevatedButton(
            text="刷新群组",
            icon=ft.icons.REFRESH,
            on_click=self._on_refresh_click,
            style=ft.ButtonStyle(
                bgcolor=ft.colors.BLUE,
                color=ft.colors.WHITE,
                padding=ft.padding.symmetric(horizontal=20, vertical=12)
            )
        )
        
        # 连接状态指示器
        connection_status = ft.Container(
            content=ft.Row(
                controls=[
                    ft.Icon(ft.icons.CIRCLE, size=12, color=ft.colors.RED),
                    ft.Text("Chatlog服务未连接", size=12, color=ft.colors.GREY_600)
                ],
                spacing=8
            ),
            padding=ft.padding.symmetric(horizontal=15, vertical=8),
            bgcolor=ft.colors.RED_50,
            border_radius=20,
            border=ft.border.all(1, ft.colors.RED_200)
        )
        
        return ft.Container(
            content=ft.Row(
                controls=[
                    self.search_field,
                    self.refresh_button,
                    connection_status
                ],
                alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                vertical_alignment=ft.CrossAxisAlignment.CENTER
            ),
            padding=ft.padding.all(20),
            bgcolor=ft.colors.WHITE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.colors.BLACK12,
                offset=ft.Offset(0, 4)
            )
        )
    
    def _create_bottom_bar(self) -> ft.Container:
        """创建底部操作栏"""
        
        return ft.Container(
            content=ft.Row(
                controls=[
                    # 选中统计
                    ft.Text(
                        f"已选择 {len(self.selected_groups)} 个群组",
                        size=14,
                        color=ft.colors.GREY_600
                    ),
                    
                    # 操作按钮
                    ft.Row(
                        controls=[
                            ft.ElevatedButton(
                                text="全选",
                                icon=ft.icons.SELECT_ALL,
                                on_click=self._on_select_all,
                                style=ft.ButtonStyle(
                                    bgcolor=ft.colors.GREY_600,
                                    color=ft.colors.WHITE
                                )
                            ),
                            ft.ElevatedButton(
                                text="清空选择",
                                icon=ft.icons.CLEAR,
                                on_click=self._on_clear_selection,
                                style=ft.ButtonStyle(
                                    bgcolor=ft.colors.GREY_400,
                                    color=ft.colors.WHITE
                                )
                            ),
                            ft.ElevatedButton(
                                text="开始分析",
                                icon=ft.icons.ANALYTICS,
                                on_click=self._on_start_analysis,
                                style=ft.ButtonStyle(
                                    bgcolor=ft.colors.GREEN,
                                    color=ft.colors.WHITE
                                ),
                                disabled=len(self.selected_groups) == 0
                            )
                        ],
                        spacing=10
                    )
                ],
                alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                vertical_alignment=ft.CrossAxisAlignment.CENTER
            ),
            padding=ft.padding.all(20),
            bgcolor=ft.colors.WHITE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.colors.BLACK12,
                offset=ft.Offset(0, 4)
            )
        )
    
    def _create_group_card(self, group: Dict[str, Any]) -> ft.Container:
        """创建群组卡片"""
        
        is_selected = group['id'] in self.selected_groups
        
        def on_select_change(e):
            if e.control.value:
                self.selected_groups.add(group['id'])
            else:
                self.selected_groups.discard(group['id'])
            self._update_bottom_bar()
        
        # 群组信息
        group_info = ft.Column(
            controls=[
                ft.Text(
                    group['name'],
                    size=16,
                    weight=ft.FontWeight.BOLD,
                    color=ft.colors.GREY_800
                ),
                ft.Row(
                    controls=[
                        ft.Icon(ft.icons.PEOPLE, size=16, color=ft.colors.GREY_500),
                        ft.Text(f"{group['member_count']} 成员", size=12, color=ft.colors.GREY_600),
                        ft.Text("•", size=12, color=ft.colors.GREY_400),
                        ft.Icon(ft.icons.MESSAGE, size=16, color=ft.colors.GREY_500),
                        ft.Text(f"{group['message_count']} 消息", size=12, color=ft.colors.GREY_600),
                        ft.Text("•", size=12, color=ft.colors.GREY_400),
                        ft.Text(f"最后活动: {group['last_activity']}", size=12, color=ft.colors.GREY_600)
                    ],
                    spacing=5
                )
            ],
            spacing=8,
            expand=True
        )
        
        # 选择框
        checkbox = ft.Checkbox(
            value=is_selected,
            on_change=on_select_change,
            active_color=ft.colors.BLUE
        )
        
        # 操作按钮
        action_buttons = ft.Row(
            controls=[
                ft.IconButton(
                    icon=ft.icons.VISIBILITY,
                    tooltip="查看详情",
                    on_click=lambda e: self._show_group_details(group),
                    icon_color=ft.colors.BLUE
                ),
                ft.IconButton(
                    icon=ft.icons.ANALYTICS,
                    tooltip="单独分析",
                    on_click=lambda e: self._analyze_single_group(group),
                    icon_color=ft.colors.GREEN
                )
            ],
            spacing=5
        )
        
        return ft.Container(
            content=ft.Row(
                controls=[
                    checkbox,
                    group_info,
                    action_buttons
                ],
                spacing=15,
                alignment=ft.MainAxisAlignment.START,
                vertical_alignment=ft.CrossAxisAlignment.CENTER
            ),
            padding=ft.padding.all(15),
            bgcolor=ft.colors.BLUE_50 if is_selected else ft.colors.GREY_50,
            border_radius=8,
            border=ft.border.all(
                2 if is_selected else 1,
                ft.colors.BLUE if is_selected else ft.colors.GREY_200
            ),
            animate=ft.animation.Animation(200, ft.AnimationCurve.EASE_OUT)
        )
    
    async def _load_groups(self):
        """加载群组数据"""
        # 模拟群组数据
        self.groups_data = [
            {
                "id": "group_1",
                "name": "技术交流群",
                "member_count": 156,
                "message_count": 2340,
                "last_activity": "2小时前"
            },
            {
                "id": "group_2", 
                "name": "创业者联盟",
                "member_count": 89,
                "message_count": 1567,
                "last_activity": "30分钟前"
            },
            {
                "id": "group_3",
                "name": "产品经理群",
                "member_count": 234,
                "message_count": 3456,
                "last_activity": "1小时前"
            },
            {
                "id": "group_4",
                "name": "AI学习小组",
                "member_count": 67,
                "message_count": 890,
                "last_activity": "5分钟前"
            }
        ]
        
        await self._update_groups_display()
    
    async def _update_groups_display(self):
        """更新群组显示"""
        # 过滤群组
        filtered_groups = []
        for group in self.groups_data:
            if not self.search_query or self.search_query.lower() in group['name'].lower():
                filtered_groups.append(group)
        
        # 清空现有列表
        self.groups_list.controls.clear()
        
        if not filtered_groups:
            # 显示空状态
            empty_state = ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Icon(ft.icons.SEARCH_OFF, size=64, color=ft.colors.GREY_400),
                        ft.Text(
                            "未找到匹配的群组" if self.search_query else "暂无群组数据",
                            size=16,
                            color=ft.colors.GREY_600,
                            text_align=ft.TextAlign.CENTER
                        ),
                        ft.Text(
                            "请检查Chatlog服务连接状态" if not self.search_query else "尝试调整搜索条件",
                            size=14,
                            color=ft.colors.GREY_500,
                            text_align=ft.TextAlign.CENTER
                        )
                    ],
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    spacing=10
                ),
                alignment=ft.alignment.center,
                height=300
            )
            self.groups_list.controls.append(empty_state)
        else:
            # 添加群组卡片
            for group in filtered_groups:
                self.groups_list.controls.append(self._create_group_card(group))
        
        await self.app.page.update_async()
    
    def _update_bottom_bar(self):
        """更新底部操作栏"""
        # 这里应该更新底部栏的选中统计和按钮状态
        # 由于Flet的限制，这里简化处理
        pass
    
    async def _on_search_change(self, e):
        """搜索框变化事件"""
        self.search_query = e.control.value
        await self._update_groups_display()
    
    async def _on_refresh_click(self, e):
        """刷新按钮点击事件"""
        self.refresh_button.disabled = True
        self.refresh_button.text = "刷新中..."
        await self.app.page.update_async()
        
        # 模拟刷新延迟
        await asyncio.sleep(2)
        
        await self._load_groups()
        
        self.refresh_button.disabled = False
        self.refresh_button.text = "刷新群组"
        await self.app.page.update_async()
        
        await self.app.show_message("刷新完成", "群组列表已更新", "success")
    
    async def _on_select_all(self, e):
        """全选事件"""
        for group in self.groups_data:
            self.selected_groups.add(group['id'])
        await self._update_groups_display()
    
    async def _on_clear_selection(self, e):
        """清空选择事件"""
        self.selected_groups.clear()
        await self._update_groups_display()
    
    async def _on_start_analysis(self, e):
        """开始分析事件"""
        if not self.selected_groups:
            await self.app.show_message("提示", "请先选择要分析的群组", "warning")
            return
        
        # 导航到分析页面
        await self.app.navigate_to("analysis")
    
    async def _show_group_details(self, group: Dict[str, Any]):
        """显示群组详情"""
        details = f"""
群组名称：{group['name']}
成员数量：{group['member_count']} 人
消息数量：{group['message_count']} 条
最后活动：{group['last_activity']}

群组ID：{group['id']}
        """
        await self.app.show_message("群组详情", details.strip(), "info")
    
    async def _analyze_single_group(self, group: Dict[str, Any]):
        """分析单个群组"""
        self.selected_groups.clear()
        self.selected_groups.add(group['id'])
        await self.app.navigate_to("analysis")
