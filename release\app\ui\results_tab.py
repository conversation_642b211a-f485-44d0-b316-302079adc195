"""
结果查看标签页
展示分析结果，包括用户画像列表、统计信息、数据导出等功能
"""

import tkinter as tk
from tkinter import messagebox, filedialog
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import logging
from typing import List, Dict, Any, Optional
import json
import pandas as pd
from datetime import datetime

from ..models import UserProfile, IntroductionStatus

logger = logging.getLogger(__name__)

class ResultsTab:
    """结果查看标签页"""
    
    def __init__(self, parent, analysis_service):
        self.parent = parent
        self.analysis_service = analysis_service
        
        # 数据
        self.profiles: List[UserProfile] = []
        self.filtered_profiles: List[UserProfile] = []
        
        # 创建界面
        self.frame = ttk.Frame(parent)
        self._create_widgets()
        
        logger.info("结果查看标签页初始化完成")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主容器 - 使用PanedWindow分割
        paned = ttk.PanedWindow(self.frame, orient=HORIZONTAL)
        paned.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # 左侧面板 - 控制和统计
        left_panel = ttk.Frame(paned)
        paned.add(left_panel, weight=1)
        
        # 右侧面板 - 结果展示
        right_panel = ttk.Frame(paned)
        paned.add(right_panel, weight=3)
        
        # 创建左侧控制面板
        self._create_control_panel(left_panel)
        
        # 创建右侧结果面板
        self._create_results_panel(right_panel)
    
    def _create_control_panel(self, parent):
        """创建左侧控制面板"""
        # 标题
        title_label = ttk.Label(parent, text="数据控制", font=("微软雅黑", 12, "bold"))
        title_label.pack(anchor=W, pady=(0, 10))
        
        # 操作按钮组
        btn_frame = ttk.LabelFrame(parent, text="操作", padding=10)
        btn_frame.pack(fill=X, pady=(0, 10))
        
        # 刷新按钮
        refresh_btn = ttk.Button(
            btn_frame, 
            text="🔄 刷新数据",
            command=self.refresh_results,
            bootstyle="primary"
        )
        refresh_btn.pack(fill=X, pady=(0, 5))
        
        # 导出按钮
        export_btn = ttk.Button(
            btn_frame,
            text="📊 导出Excel", 
            command=self.export_to_excel,
            bootstyle="success"
        )
        export_btn.pack(fill=X, pady=(0, 5))
        
        # 导出JSON按钮
        export_json_btn = ttk.Button(
            btn_frame,
            text="📄 导出JSON",
            command=self.export_to_json,
            bootstyle="info"
        )
        export_json_btn.pack(fill=X, pady=(0, 5))
        
        # 清空数据按钮
        clear_btn = ttk.Button(
            btn_frame,
            text="🗑️ 清空数据",
            command=self.clear_data,
            bootstyle="danger"
        )
        clear_btn.pack(fill=X)
        
        # 筛选器
        filter_frame = ttk.LabelFrame(parent, text="筛选", padding=10)
        filter_frame.pack(fill=X, pady=(0, 10))
        
        # 置信度筛选
        conf_label = ttk.Label(filter_frame, text="最低置信度:")
        conf_label.pack(anchor=W)
        
        self.confidence_var = tk.DoubleVar(value=0.0)
        self.confidence_scale = ttk.Scale(
            filter_frame,
            from_=0.0,
            to=1.0,
            variable=self.confidence_var,
            length=200,
            command=self._on_filter_change
        )
        self.confidence_scale.pack(fill=X, pady=(0, 5))
        
        self.confidence_label = ttk.Label(filter_frame, text="0.0")
        self.confidence_label.pack(anchor=W)
        
        # 行业筛选
        industry_label = ttk.Label(filter_frame, text="行业筛选:")
        industry_label.pack(anchor=W, pady=(10, 0))
        
        self.industry_var = tk.StringVar(value="全部")
        self.industry_combo = ttk.Combobox(
            filter_frame,
            textvariable=self.industry_var,
            state="readonly"
        )
        self.industry_combo.pack(fill=X, pady=(0, 5))
        self.industry_combo.bind("<<ComboboxSelected>>", self._on_filter_change)
        
        # 状态筛选
        status_label = ttk.Label(filter_frame, text="状态筛选:")
        status_label.pack(anchor=W, pady=(10, 0))
        
        self.status_var = tk.StringVar(value="全部")
        self.status_combo = ttk.Combobox(
            filter_frame,
            textvariable=self.status_var,
            values=["全部", "已完成", "失败", "跳过"],
            state="readonly"
        )
        self.status_combo.pack(fill=X)
        self.status_combo.bind("<<ComboboxSelected>>", self._on_filter_change)
        
        # 统计信息
        stats_frame = ttk.LabelFrame(parent, text="统计信息", padding=10)
        stats_frame.pack(fill=X, pady=(0, 10))
        
        self.stats_text = tk.Text(
            stats_frame,
            height=12,
            width=30,
            font=("微软雅黑", 9),
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.stats_text.pack(fill=BOTH, expand=True)
    
    def _create_results_panel(self, parent):
        """创建右侧结果面板"""
        # 标题和搜索
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill=X, pady=(0, 10))
        
        title_label = ttk.Label(header_frame, text="用户画像列表", font=("微软雅黑", 12, "bold"))
        title_label.pack(side=LEFT)
        
        # 搜索框
        search_frame = ttk.Frame(header_frame)
        search_frame.pack(side=RIGHT)
        
        search_label = ttk.Label(search_frame, text="搜索:")
        search_label.pack(side=LEFT, padx=(0, 5))
        
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=20)
        search_entry.pack(side=LEFT)
        search_entry.bind("<KeyRelease>", self._on_search_change)
        
        # 结果表格
        self._create_results_table(parent)
        
        # 详情面板
        self._create_detail_panel(parent)
    
    def _create_results_table(self, parent):
        """创建结果表格"""
        # 表格容器
        table_frame = ttk.LabelFrame(parent, text="数据表格", padding=5)
        table_frame.pack(fill=BOTH, expand=True, pady=(0, 10))
        
        # 创建表格
        columns = ("昵称", "职业", "行业", "能提供", "寻求", "置信度", "状态")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # 设置列标题和宽度
        column_widths = {"昵称": 100, "职业": 120, "行业": 100, "能提供": 150, "寻求": 150, "置信度": 80, "状态": 80}
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=column_widths.get(col, 100), minwidth=50)
        
        # 添加滚动条
        v_scrollbar = ttk.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # 绑定选择事件
        self.tree.bind("<<TreeviewSelect>>", self._on_tree_select)
        
        # 右键菜单
        self._create_context_menu()
    
    def _create_context_menu(self):
        """创建右键菜单"""
        self.context_menu = tk.Menu(self.tree, tearoff=0)
        self.context_menu.add_command(label="查看详情", command=self._show_detail)
        self.context_menu.add_command(label="复制信息", command=self._copy_info)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="删除记录", command=self._delete_record)
        
        def show_context_menu(event):
            try:
                self.context_menu.post(event.x_root, event.y_root)
            except:
                pass
        
        self.tree.bind("<Button-3>", show_context_menu)  # 右键
    
    def _create_detail_panel(self, parent):
        """创建详情面板"""
        detail_frame = ttk.LabelFrame(parent, text="详细信息", padding=5)
        detail_frame.pack(fill=X)
        
        # 详情文本
        self.detail_text = tk.Text(
            detail_frame,
            height=6,
            font=("微软雅黑", 9),
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        
        detail_scrollbar = ttk.Scrollbar(detail_frame, command=self.detail_text.yview)
        self.detail_text.configure(yscrollcommand=detail_scrollbar.set)
        
        self.detail_text.pack(side=LEFT, fill=BOTH, expand=True)
        detail_scrollbar.pack(side=RIGHT, fill=Y)
    
    def refresh_results(self):
        """刷新结果数据"""
        try:
            # 这里应该从实际的数据源获取数据
            # 由于当前是演示版本，我们创建一些示例数据
            self._load_sample_data()
            
            # 更新界面
            self._update_display()
            self._update_statistics()
            self._update_filter_options()
            
            logger.info("结果数据刷新完成")
            
        except Exception as e:
            logger.error(f"刷新结果数据失败: {e}")
            messagebox.showerror("刷新失败", f"刷新结果数据失败:\n{str(e)}")
    
    def _load_sample_data(self):
        """加载示例数据（实际使用时应该从数据库或文件加载）"""
        import uuid
        from datetime import datetime, timedelta
        import random
        
        # 示例数据
        sample_data = [
            {
                "nickname": "张三",
                "profession": "产品经理", 
                "industry": "互联网",
                "can_provide": "产品设计咨询",
                "looking_for": "技术合作",
                "personal_intro": "5年产品经验",
                "confidence_score": 0.92
            },
            {
                "nickname": "李四",
                "profession": "前端工程师",
                "industry": "互联网", 
                "can_provide": "前端开发",
                "looking_for": "职业发展建议",
                "personal_intro": "精通React和Vue",
                "confidence_score": 0.88
            },
            {
                "nickname": "王五",
                "profession": "UI设计师",
                "industry": "设计",
                "can_provide": "界面设计",
                "looking_for": "设计灵感",
                "personal_intro": "专注移动端设计",
                "confidence_score": 0.85
            },
            {
                "nickname": "赵六",
                "profession": "数据分析师", 
                "industry": "金融",
                "can_provide": "数据分析",
                "looking_for": "数据源",
                "personal_intro": "擅长机器学习",
                "confidence_score": 0.79
            },
            {
                "nickname": "孙七",
                "profession": "市场营销",
                "industry": "电商",
                "can_provide": "营销策划",
                "looking_for": "流量获取",
                "personal_intro": "电商运营专家",
                "confidence_score": 0.73
            }
        ]
        
        # 转换为UserProfile对象
        self.profiles = []
        for data in sample_data:
            profile = UserProfile(
                id=str(uuid.uuid4()),
                user_id=f"user_{len(self.profiles)}",
                chat_id="sample_chat",
                message_id=f"msg_{len(self.profiles)}",
                nickname=data["nickname"],
                profession=data["profession"],
                industry=data["industry"],
                can_provide=data["can_provide"],
                looking_for=data["looking_for"],
                personal_intro=data["personal_intro"],
                confidence_score=data["confidence_score"],
                status=IntroductionStatus.COMPLETED,
                raw_content=f"大家好，我是{data['nickname']}，{data['personal_intro']}...",
                created_time=datetime.now() - timedelta(hours=random.randint(1, 48))
            )
            self.profiles.append(profile)
        
        self.filtered_profiles = self.profiles.copy()
    
    def _update_display(self):
        """更新显示"""
        # 清空表格
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 添加数据
        for profile in self.filtered_profiles:
            status_text = self._get_status_text(profile.status)
            confidence_text = f"{profile.confidence_score:.2f}"
            
            self.tree.insert("", "end", values=(
                profile.nickname or "未知",
                profile.profession or "未知",
                profile.industry or "未知", 
                profile.can_provide or "无",
                profile.looking_for or "无",
                confidence_text,
                status_text
            ), tags=(profile.id,))
    
    def _get_status_text(self, status: IntroductionStatus) -> str:
        """获取状态文本"""
        status_map = {
            IntroductionStatus.COMPLETED: "已完成",
            IntroductionStatus.FAILED: "失败",
            IntroductionStatus.SKIPPED: "跳过",
            IntroductionStatus.PENDING: "待处理",
            IntroductionStatus.PROCESSING: "处理中"
        }
        return status_map.get(status, "未知")
    
    def _update_statistics(self):
        """更新统计信息"""
        if not self.profiles:
            stats_text = "暂无数据"
        else:
            total = len(self.profiles)
            completed = sum(1 for p in self.profiles if p.status == IntroductionStatus.COMPLETED)
            failed = sum(1 for p in self.profiles if p.status == IntroductionStatus.FAILED)
            avg_confidence = sum(p.confidence_score for p in self.profiles) / total
            
            # 行业分布
            industries = {}
            for p in self.profiles:
                industry = p.industry or "未知"
                industries[industry] = industries.get(industry, 0) + 1
            
            # 职业分布
            professions = {}
            for p in self.profiles:
                profession = p.profession or "未知"
                professions[profession] = professions.get(profession, 0) + 1
            
            stats_text = f"""总记录数: {total}
已完成: {completed}
失败: {failed}
成功率: {completed/total*100:.1f}%
平均置信度: {avg_confidence:.2f}

行业分布:
"""
            for industry, count in sorted(industries.items(), key=lambda x: x[1], reverse=True)[:5]:
                stats_text += f"  {industry}: {count}\n"
            
            stats_text += "\n职业分布:\n"
            for profession, count in sorted(professions.items(), key=lambda x: x[1], reverse=True)[:5]:
                stats_text += f"  {profession}: {count}\n"
        
        # 更新统计文本
        self.stats_text.config(state=tk.NORMAL)
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(1.0, stats_text)
        self.stats_text.config(state=tk.DISABLED)
    
    def _update_filter_options(self):
        """更新筛选选项"""
        # 更新行业选项
        industries = set(p.industry for p in self.profiles if p.industry)
        industries_list = ["全部"] + sorted(list(industries))
        self.industry_combo['values'] = industries_list
    
    def _on_filter_change(self, *args):
        """筛选条件改变"""
        # 更新置信度标签
        conf_value = self.confidence_var.get()
        self.confidence_label.config(text=f"{conf_value:.2f}")
        
        # 应用筛选
        self._apply_filters()
    
    def _on_search_change(self, *args):
        """搜索条件改变"""
        self._apply_filters()
    
    def _apply_filters(self):
        """应用筛选条件"""
        filtered = self.profiles.copy()
        
        # 置信度筛选
        min_confidence = self.confidence_var.get()
        filtered = [p for p in filtered if p.confidence_score >= min_confidence]
        
        # 行业筛选
        industry_filter = self.industry_var.get()
        if industry_filter != "全部":
            filtered = [p for p in filtered if p.industry == industry_filter]
        
        # 状态筛选
        status_filter = self.status_var.get()
        if status_filter != "全部":
            status_map = {
                "已完成": IntroductionStatus.COMPLETED,
                "失败": IntroductionStatus.FAILED,
                "跳过": IntroductionStatus.SKIPPED
            }
            if status_filter in status_map:
                filtered = [p for p in filtered if p.status == status_map[status_filter]]
        
        # 搜索筛选
        search_text = self.search_var.get().lower()
        if search_text:
            filtered = [p for p in filtered if
                       search_text in (p.nickname or "").lower() or
                       search_text in (p.profession or "").lower() or
                       search_text in (p.industry or "").lower() or
                       search_text in (p.can_provide or "").lower() or
                       search_text in (p.looking_for or "").lower()]
        
        self.filtered_profiles = filtered
        self._update_display()
    
    def _on_tree_select(self, event):
        """表格选择事件"""
        selection = self.tree.selection()
        if selection:
            item = selection[0]
            tags = self.tree.item(item)['tags']
            if tags:
                profile_id = tags[0]
                profile = next((p for p in self.profiles if p.id == profile_id), None)
                if profile:
                    self._show_profile_detail(profile)
    
    def _show_profile_detail(self, profile: UserProfile):
        """显示用户画像详情"""
        detail_text = f"""昵称: {profile.nickname or '未知'}
职业: {profile.profession or '未知'}
行业: {profile.industry or '未知'}
个人介绍: {profile.personal_intro or '无'}
能够提供: {profile.can_provide or '无'}
寻求帮助: {profile.looking_for or '无'}
置信度: {profile.confidence_score:.2f}
状态: {self._get_status_text(profile.status)}
创建时间: {profile.created_time.strftime('%Y-%m-%d %H:%M:%S')}

原始消息:
{profile.raw_content}"""
        
        self.detail_text.config(state=tk.NORMAL)
        self.detail_text.delete(1.0, tk.END)
        self.detail_text.insert(1.0, detail_text)
        self.detail_text.config(state=tk.DISABLED)
    
    def _show_detail(self):
        """显示详情（右键菜单）"""
        selection = self.tree.selection()
        if selection:
            # 可以弹出独立的详情窗口
            messagebox.showinfo("详情", "详情功能开发中...")
    
    def _copy_info(self):
        """复制信息"""
        selection = self.tree.selection()
        if selection:
            item = selection[0]
            values = self.tree.item(item)['values']
            info = f"昵称: {values[0]}\n职业: {values[1]}\n行业: {values[2]}\n能提供: {values[3]}\n寻求: {values[4]}"
            
            self.frame.clipboard_clear()
            self.frame.clipboard_append(info)
            messagebox.showinfo("提示", "信息已复制到剪贴板")
    
    def _delete_record(self):
        """删除记录"""
        selection = self.tree.selection()
        if selection:
            result = messagebox.askyesno("确认删除", "确定要删除选中的记录吗？")
            if result:
                # 删除逻辑（这里只是演示）
                messagebox.showinfo("提示", "删除功能开发中...")
    
    def export_to_excel(self):
        """导出到Excel"""
        try:
            if not self.filtered_profiles:
                messagebox.showwarning("提示", "没有数据可导出")
                return
            
            filename = filedialog.asksaveasfilename(
                title="导出Excel文件",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
            )
            
            if filename:
                # 准备数据
                data = []
                for profile in self.filtered_profiles:
                    data.append({
                        '昵称': profile.nickname,
                        '职业': profile.profession,
                        '行业': profile.industry,
                        '个人介绍': profile.personal_intro,
                        '能够提供': profile.can_provide,
                        '寻求帮助': profile.looking_for,
                        '置信度': profile.confidence_score,
                        '状态': self._get_status_text(profile.status),
                        '创建时间': profile.created_time.strftime('%Y-%m-%d %H:%M:%S'),
                        '原始消息': profile.raw_content
                    })
                
                # 创建DataFrame并导出
                df = pd.DataFrame(data)
                df.to_excel(filename, index=False)
                
                messagebox.showinfo("导出成功", f"数据已导出到:\n{filename}")
                
        except Exception as e:
            logger.error(f"导出Excel失败: {e}")
            messagebox.showerror("导出失败", f"导出Excel失败:\n{str(e)}")
    
    def export_to_json(self):
        """导出到JSON"""
        try:
            if not self.filtered_profiles:
                messagebox.showwarning("提示", "没有数据可导出")
                return
            
            filename = filedialog.asksaveasfilename(
                title="导出JSON文件",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            
            if filename:
                # 准备数据
                data = []
                for profile in self.filtered_profiles:
                    data.append(profile.to_dict())
                
                # 导出JSON
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                messagebox.showinfo("导出成功", f"数据已导出到:\n{filename}")
                
        except Exception as e:
            logger.error(f"导出JSON失败: {e}")
            messagebox.showerror("导出失败", f"导出JSON失败:\n{str(e)}")
    
    def clear_data(self):
        """清空数据"""
        result = messagebox.askyesno("确认清空", "确定要清空所有数据吗？\n此操作不可恢复！")
        if result:
            self.profiles.clear()
            self.filtered_profiles.clear()
            self._update_display()
            self._update_statistics()
            messagebox.showinfo("提示", "数据已清空")
    
    def on_progress_update(self, event: str, data: Dict[str, Any]):
        """处理进度更新事件"""
        try:
            # 根据不同事件更新界面
            if event == "analysis_completed":
                # 分析完成后自动刷新
                self.refresh_results()
            elif event == "profiles_extracted":
                # 画像提取完成
                logger.info(f"收到画像提取完成事件: {data}")
        except Exception as e:
            logger.error(f"处理进度更新失败: {e}") 