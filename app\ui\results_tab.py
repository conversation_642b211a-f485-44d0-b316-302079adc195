"""
结果查看标签页
展示分析结果，包括用户画像列表、统计信息、数据导出等功能
"""

import tkinter as tk
from tkinter import messagebox, filedialog
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import logging
from typing import List, Dict, Any, Optional
import json
import pandas as pd
from datetime import datetime

from ..models import UserProfile, IntroductionStatus
from ..config import get_config

logger = logging.getLogger(__name__)

class ResultsTab:
    """结果查看标签页"""
    
    def __init__(self, parent, analysis_service):
        self.parent = parent
        self.analysis_service = analysis_service
        
        # 数据
        self.profiles: List[UserProfile] = []
        self.filtered_profiles: List[UserProfile] = []
        
        # 创建界面
        self.frame = ttk.Frame(parent)
        self._create_widgets()
        
        logger.info("结果查看标签页初始化完成")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主容器 - 使用PanedWindow分割
        paned = ttk.PanedWindow(self.frame, orient=HORIZONTAL)
        paned.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # 左侧面板 - 控制和统计
        left_panel = ttk.Frame(paned)
        paned.add(left_panel, weight=1)
        
        # 右侧面板 - 结果展示
        right_panel = ttk.Frame(paned)
        paned.add(right_panel, weight=3)
        
        # 创建左侧控制面板
        self._create_control_panel(left_panel)
        
        # 创建右侧结果面板
        self._create_results_panel(right_panel)
    
    def _create_control_panel(self, parent):
        """创建左侧控制面板"""
        # 标题
        title_label = ttk.Label(parent, text="数据控制", font=("微软雅黑", 12, "bold"))
        title_label.pack(anchor=W, pady=(0, 10))
        
        # 操作按钮组
        btn_frame = ttk.LabelFrame(parent, text="操作", padding=10)
        btn_frame.pack(fill=X, pady=(0, 10))
        
        # 刷新按钮
        refresh_btn = ttk.Button(
            btn_frame, 
            text="🔄 刷新数据",
            command=self.refresh_results,
            bootstyle="primary"
        )
        refresh_btn.pack(fill=X, pady=(0, 5))
        
        # 导出按钮
        export_btn = ttk.Button(
            btn_frame,
            text="📊 导出Excel", 
            command=self.export_to_excel,
            bootstyle="success"
        )
        export_btn.pack(fill=X, pady=(0, 5))
        
        # 导出JSON按钮
        export_json_btn = ttk.Button(
            btn_frame,
            text="📄 导出JSON",
            command=self.export_to_json,
            bootstyle="info"
        )
        export_json_btn.pack(fill=X, pady=(0, 5))
        
        # 清空数据按钮
        clear_btn = ttk.Button(
            btn_frame,
            text="🗑️ 清空数据",
            command=self.clear_data,
            bootstyle="danger"
        )
        clear_btn.pack(fill=X)
        
        # 飞书同步按钮
        sync_feishu_btn = ttk.Button(
            btn_frame,
            text="🚀 同步到飞书",
            command=self.sync_to_feishu,
            bootstyle="warning"
        )
        sync_feishu_btn.pack(fill=X, pady=(5, 0))
        
        # 筛选器
        filter_frame = ttk.LabelFrame(parent, text="筛选", padding=10)
        filter_frame.pack(fill=X, pady=(0, 10))
        
        # 置信度筛选
        conf_label = ttk.Label(filter_frame, text="最低置信度:")
        conf_label.pack(anchor=W)
        
        self.confidence_var = tk.DoubleVar(value=0.0)
        self.confidence_scale = ttk.Scale(
            filter_frame,
            from_=0.0,
            to=1.0,
            variable=self.confidence_var,
            length=200,
            command=self._on_filter_change
        )
        self.confidence_scale.pack(fill=X, pady=(0, 5))
        
        self.confidence_label = ttk.Label(filter_frame, text="0.0")
        self.confidence_label.pack(anchor=W)
        
        # 行业筛选
        industry_label = ttk.Label(filter_frame, text="行业筛选:")
        industry_label.pack(anchor=W, pady=(10, 0))
        
        self.industry_var = tk.StringVar(value="全部")
        self.industry_combo = ttk.Combobox(
            filter_frame,
            textvariable=self.industry_var,
            state="readonly"
        )
        self.industry_combo.pack(fill=X, pady=(0, 5))
        self.industry_combo.bind("<<ComboboxSelected>>", self._on_filter_change)
        
        # 状态筛选
        status_label = ttk.Label(filter_frame, text="状态筛选:")
        status_label.pack(anchor=W, pady=(10, 0))
        
        self.status_var = tk.StringVar(value="全部")
        self.status_combo = ttk.Combobox(
            filter_frame,
            textvariable=self.status_var,
            values=["全部", "已完成", "失败", "跳过"],
            state="readonly"
        )
        self.status_combo.pack(fill=X)
        self.status_combo.bind("<<ComboboxSelected>>", self._on_filter_change)
        
        # 统计信息
        stats_frame = ttk.LabelFrame(parent, text="统计信息", padding=10)
        stats_frame.pack(fill=X, pady=(0, 10))
        
        self.stats_text = tk.Text(
            stats_frame,
            height=12,
            width=30,
            font=("微软雅黑", 9),
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.stats_text.pack(fill=BOTH, expand=True)
    
    def _create_results_panel(self, parent):
        """创建右侧结果面板"""
        # 标题和搜索
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill=X, pady=(0, 10))
        
        title_label = ttk.Label(header_frame, text="用户画像列表", font=("微软雅黑", 12, "bold"))
        title_label.pack(side=LEFT)
        
        # 搜索框
        search_frame = ttk.Frame(header_frame)
        search_frame.pack(side=RIGHT)
        
        search_label = ttk.Label(search_frame, text="搜索:")
        search_label.pack(side=LEFT, padx=(0, 5))
        
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=20)
        search_entry.pack(side=LEFT)
        search_entry.bind("<KeyRelease>", self._on_search_change)
        
        # 结果表格
        self._create_results_table(parent)
        
        # 详情面板
        self._create_detail_panel(parent)
    
    def _create_results_table(self, parent):
        """创建结果表格"""
        # 表格容器
        table_frame = ttk.LabelFrame(parent, text="数据表格", padding=5)
        table_frame.pack(fill=BOTH, expand=True, pady=(0, 10))
        
        # 创建表格
        columns = ("昵称", "发送者", "行业", "能提供", "需要帮助", "置信度", "状态")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # 设置列标题和宽度
        column_widths = {"昵称": 100, "发送者": 120, "行业": 100, "能提供": 150, "需要帮助": 150, "置信度": 80, "状态": 80}
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=column_widths.get(col, 100), minwidth=50)
        
        # 添加滚动条
        v_scrollbar = ttk.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # 绑定选择事件
        self.tree.bind("<<TreeviewSelect>>", self._on_tree_select)
        
        # 右键菜单
        self._create_context_menu()
    
    def _create_context_menu(self):
        """创建右键菜单"""
        self.context_menu = tk.Menu(self.tree, tearoff=0)
        self.context_menu.add_command(label="查看详情", command=self._show_detail)
        self.context_menu.add_command(label="复制信息", command=self._copy_info)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="删除记录", command=self._delete_record)
        
        def show_context_menu(event):
            try:
                self.context_menu.post(event.x_root, event.y_root)
            except:
                pass
        
        self.tree.bind("<Button-3>", show_context_menu)  # 右键
    
    def _create_detail_panel(self, parent):
        """创建详情面板"""
        detail_frame = ttk.LabelFrame(parent, text="详细信息", padding=5)
        detail_frame.pack(fill=X)
        
        # 详情文本
        self.detail_text = tk.Text(
            detail_frame,
            height=6,
            font=("微软雅黑", 9),
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        
        detail_scrollbar = ttk.Scrollbar(detail_frame, command=self.detail_text.yview)
        self.detail_text.configure(yscrollcommand=detail_scrollbar.set)
        
        self.detail_text.pack(side=LEFT, fill=BOTH, expand=True)
        detail_scrollbar.pack(side=RIGHT, fill=Y)
    
    def refresh_results(self):
        """刷新结果数据"""
        try:
            # 从分析服务获取实际数据
            self._load_actual_data()
            
            # 更新界面
            self._update_display()
            self._update_statistics()
            self._update_filter_options()
            
            logger.info("结果数据刷新完成")
            
        except Exception as e:
            logger.error(f"刷新结果数据失败: {e}")
            messagebox.showerror("刷新失败", f"刷新结果数据失败:\n{str(e)}")
    
    def _load_actual_data(self):
        """从分析服务加载实际数据"""
        try:
            # 获取应用状态
            app_state = self.analysis_service.get_app_state()
            
            # 如果有已完成的任务，获取其结果
            if hasattr(app_state, 'completed_profiles') and app_state.completed_profiles:
                self.profiles = app_state.completed_profiles.copy()
                logger.info(f"从应用状态加载了 {len(self.profiles)} 个用户画像")
            else:
                # 如果没有完成的分析，尝试从其他地方获取
                # 这里可以根据实际情况从数据库、文件等位置加载
                self.profiles = []
                logger.info("未找到已完成的分析结果")
            
            self.filtered_profiles = self.profiles.copy()
            
        except Exception as e:
            logger.error(f"加载实际数据失败: {e}")
            # 如果加载失败，使用空数据而不是示例数据
            self.profiles = []
            self.filtered_profiles = []
    
    def _update_display(self):
        """更新显示"""
        # 清空表格
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 添加数据
        for profile in self.filtered_profiles:
            # UserProfile没有status字段，我们可以假设提取成功的都是"已完成"状态
            status_text = "已完成"
            confidence_text = f"{profile.confidence_score:.2f}"
            
            # 处理列表字段的显示
            can_provide_text = ", ".join(profile.can_provide) if profile.can_provide else "无"
            need_help_text = ", ".join(profile.need_help) if profile.need_help else "无"
            
            self.tree.insert("", "end", values=(
                profile.nickname or "未知",
                profile.sender_name or "未知",  # 用sender_name代替profession
                profile.industry or "未知", 
                can_provide_text,
                need_help_text,  # 用need_help代替looking_for
                confidence_text,
                status_text
            ), tags=(profile.id,))
    
    def _get_status_text(self, status: IntroductionStatus) -> str:
        """获取状态文本"""
        status_map = {
            IntroductionStatus.COMPLETED: "已完成",
            IntroductionStatus.FAILED: "失败",
            IntroductionStatus.SKIPPED: "跳过",
            IntroductionStatus.PENDING: "待处理",
            IntroductionStatus.PROCESSING: "处理中"
        }
        return status_map.get(status, "未知")
    
    def _update_statistics(self):
        """更新统计信息"""
        if not self.profiles:
            stats_text = "暂无数据"
        else:
            total = len(self.profiles)
            # UserProfile没有status字段，假设所有提取成功的都是已完成状态
            completed = total  # 所有在self.profiles中的都是已完成的
            failed = 0  # 失败的不会存储在profiles中
            avg_confidence = sum(p.confidence_score for p in self.profiles) / total
            
            # 行业分布
            industries = {}
            for p in self.profiles:
                industry = p.industry or "未知"
                industries[industry] = industries.get(industry, 0) + 1
            
            # 发送者分布（代替职业分布）
            senders = {}
            for p in self.profiles:
                sender = p.sender_name or "未知"
                senders[sender] = senders.get(sender, 0) + 1
            
            stats_text = f"""总记录数: {total}
已完成: {completed}
失败: {failed}
成功率: {completed/total*100:.1f}%
平均置信度: {avg_confidence:.2f}

行业分布:
"""
            for industry, count in sorted(industries.items(), key=lambda x: x[1], reverse=True)[:5]:
                stats_text += f"  {industry}: {count}\n"
            
            stats_text += "\n发送者分布:\n"
            for sender, count in sorted(senders.items(), key=lambda x: x[1], reverse=True)[:5]:
                stats_text += f"  {sender}: {count}\n"
        
        # 更新统计文本
        self.stats_text.config(state=tk.NORMAL)
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(1.0, stats_text)
        self.stats_text.config(state=tk.DISABLED)
    
    def _update_filter_options(self):
        """更新筛选选项"""
        # 更新行业选项
        industries = set(p.industry for p in self.profiles if p.industry)
        industries_list = ["全部"] + sorted(list(industries))
        self.industry_combo['values'] = industries_list
    
    def _on_filter_change(self, *args):
        """筛选条件改变"""
        # 更新置信度标签
        conf_value = self.confidence_var.get()
        self.confidence_label.config(text=f"{conf_value:.2f}")
        
        # 应用筛选
        self._apply_filters()
    
    def _on_search_change(self, *args):
        """搜索条件改变"""
        self._apply_filters()
    
    def _apply_filters(self):
        """应用筛选条件"""
        filtered = self.profiles.copy()
        
        # 置信度筛选
        min_confidence = self.confidence_var.get()
        filtered = [p for p in filtered if p.confidence_score >= min_confidence]
        
        # 行业筛选
        industry_filter = self.industry_var.get()
        if industry_filter != "全部":
            filtered = [p for p in filtered if p.industry == industry_filter]
        
        # 状态筛选
        status_filter = self.status_var.get()
        if status_filter != "全部":
            # UserProfile没有status字段，这里只保留"已完成"的筛选逻辑
            if status_filter == "已完成":
                # 所有profiles都是已完成的，不需要过滤
                pass
            else:
                # 其他状态（失败、跳过）在UserProfile中不存在，过滤掉所有
                filtered = []
        
        # 搜索筛选
        search_text = self.search_var.get().lower()
        if search_text:
            filtered = [p for p in filtered if
                       search_text in (p.nickname or "").lower() or
                       search_text in (p.sender_name or "").lower() or
                       search_text in (p.industry or "").lower() or
                       search_text in " ".join(p.can_provide).lower() or
                       search_text in " ".join(p.need_help).lower()]
        
        self.filtered_profiles = filtered
        self._update_display()
    
    def _on_tree_select(self, event):
        """表格选择事件"""
        selection = self.tree.selection()
        if selection:
            item = selection[0]
            tags = self.tree.item(item)['tags']
            if tags:
                profile_id = tags[0]
                profile = next((p for p in self.profiles if p.id == profile_id), None)
                if profile:
                    self._show_profile_detail(profile)
    
    def _show_profile_detail(self, profile: UserProfile):
        """显示用户画像详情"""
        # 处理列表字段的显示
        can_provide_text = ", ".join(profile.can_provide) if profile.can_provide else "无"
        need_help_text = ", ".join(profile.need_help) if profile.need_help else "无"
        tags_text = ", ".join(profile.tags) if profile.tags else "无"
        
        detail_text = f"""昵称: {profile.nickname or '未知'}
发送者: {profile.sender_name or '未知'}
行业: {profile.industry or '未知'}
地区: {profile.location or '未知'}
个人介绍: {profile.personal_intro or '无'}
能够提供: {can_provide_text}
需要帮助: {need_help_text}
标签: {tags_text}
置信度: {profile.confidence_score:.2f}
状态: 已完成
提取时间: {profile.extraction_timestamp.strftime('%Y-%m-%d %H:%M:%S')}

原始消息:
{profile.original_content}"""
        
        self.detail_text.config(state=tk.NORMAL)
        self.detail_text.delete(1.0, tk.END)
        self.detail_text.insert(1.0, detail_text)
        self.detail_text.config(state=tk.DISABLED)
    
    def _show_detail(self):
        """显示详情（右键菜单）"""
        selection = self.tree.selection()
        if selection:
            # 可以弹出独立的详情窗口
            messagebox.showinfo("详情", "详情功能开发中...")
    
    def _copy_info(self):
        """复制信息"""
        selection = self.tree.selection()
        if selection:
            item = selection[0]
            values = self.tree.item(item)['values']
            info = f"昵称: {values[0]}\n职业: {values[1]}\n行业: {values[2]}\n能提供: {values[3]}\n寻求: {values[4]}"
            
            self.frame.clipboard_clear()
            self.frame.clipboard_append(info)
            messagebox.showinfo("提示", "信息已复制到剪贴板")
    
    def _delete_record(self):
        """删除记录"""
        selection = self.tree.selection()
        if selection:
            result = messagebox.askyesno("确认删除", "确定要删除选中的记录吗？")
            if result:
                # 删除逻辑（这里只是演示）
                messagebox.showinfo("提示", "删除功能开发中...")
    
    def export_to_excel(self):
        """导出到Excel"""
        try:
            if not self.filtered_profiles:
                messagebox.showwarning("提示", "没有数据可导出")
                return
            
            filename = filedialog.asksaveasfilename(
                title="导出Excel文件",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
            )
            
            if filename:
                # 准备数据
                data = []
                for profile in self.filtered_profiles:
                    can_provide_text = ", ".join(profile.can_provide) if profile.can_provide else ""
                    need_help_text = ", ".join(profile.need_help) if profile.need_help else ""
                    tags_text = ", ".join(profile.tags) if profile.tags else ""
                    
                    data.append({
                        '昵称': profile.nickname,
                        '发送者': profile.sender_name,
                        '行业': profile.industry,
                        '地区': profile.location,
                        '个人介绍': profile.personal_intro,
                        '能够提供': can_provide_text,
                        '需要帮助': need_help_text,
                        '标签': tags_text,
                        '置信度': profile.confidence_score,
                        '状态': '已完成',
                        '提取时间': profile.extraction_timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                        '原始内容': profile.original_content
                    })
                
                # 创建DataFrame并导出
                df = pd.DataFrame(data)
                df.to_excel(filename, index=False)
                
                messagebox.showinfo("导出成功", f"数据已导出到:\n{filename}")
                
        except Exception as e:
            logger.error(f"导出Excel失败: {e}")
            messagebox.showerror("导出失败", f"导出Excel失败:\n{str(e)}")
    
    def export_to_json(self):
        """导出到JSON"""
        try:
            if not self.filtered_profiles:
                messagebox.showwarning("提示", "没有数据可导出")
                return
            
            filename = filedialog.asksaveasfilename(
                title="导出JSON文件",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            
            if filename:
                # 准备数据
                data = []
                for profile in self.filtered_profiles:
                    data.append(profile.to_dict())
                
                # 导出JSON
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                messagebox.showinfo("导出成功", f"数据已导出到:\n{filename}")
                
        except Exception as e:
            logger.error(f"导出JSON失败: {e}")
            messagebox.showerror("导出失败", f"导出JSON失败:\n{str(e)}")
    
    def clear_data(self):
        """清空所有数据"""
        try:
            result = messagebox.askyesno(
                "确认清空", 
                "确定要清空所有分析结果吗？\n此操作不可恢复。",
                icon="warning"
            )
            
            if result:
                # 清空内存中的数据
                self.profiles.clear()
                self.filtered_profiles.clear()
                
                # 清空界面显示
                self._update_display()
                self._update_statistics()
                
                # 清空分析服务中的数据
                if hasattr(self.analysis_service, 'clear_all_data'):
                    self.analysis_service.clear_all_data()
                
                logger.info("已清空所有数据")
                messagebox.showinfo("清空完成", "所有数据已清空")
                
        except Exception as e:
            logger.error(f"清空数据失败: {e}")
            messagebox.showerror("清空失败", f"清空数据失败:\n{str(e)}")
    
    def sync_to_feishu(self):
        """手动同步到飞书"""
        try:
            # 检查是否有数据可同步
            if not self.profiles:
                messagebox.showwarning("同步警告", "没有可同步的数据，请先进行分析")
                return
            
            # 确认同步
            result = messagebox.askyesno(
                "确认同步", 
                f"确定要将 {len(self.profiles)} 个用户画像同步到飞书吗？\n"
                "请确保已正确配置飞书应用参数。",
                icon="question"
            )
            
            if not result:
                return
                
            # 显示同步进度窗口
            self._show_sync_progress_window()
            
        except Exception as e:
            logger.error(f"启动飞书同步失败: {e}")
            messagebox.showerror("同步失败", f"启动飞书同步失败:\n{str(e)}")
    
    def _show_sync_progress_window(self):
        """显示同步进度窗口"""
        # 创建进度窗口
        self.sync_window = tk.Toplevel(self.parent)
        self.sync_window.title("飞书同步进度")
        self.sync_window.geometry("500x300")
        self.sync_window.transient(self.parent)
        self.sync_window.grab_set()
        
        # 创建窗口内容
        main_frame = ttk.Frame(self.sync_window, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(
            main_frame, 
            text="🚀 正在同步到飞书多维表格",
            font=("微软雅黑", 14, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # 进度信息
        self.sync_status_var = tk.StringVar(value="正在准备同步...")
        status_label = ttk.Label(main_frame, textvariable=self.sync_status_var, font=("微软雅黑", 10))
        status_label.pack(pady=(0, 10))
        
        # 进度条
        self.sync_progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(
            main_frame,
            variable=self.sync_progress_var,
            maximum=100,
            bootstyle="info",
            length=400
        )
        progress_bar.pack(pady=(0, 20))
        
        # 详细信息文本框
        info_frame = ttk.LabelFrame(main_frame, text="同步详情", padding=10)
        info_frame.pack(fill=BOTH, expand=True, pady=(0, 20))
        
        self.sync_info_text = tk.Text(
            info_frame,
            height=8,
            wrap=tk.WORD,
            font=("Consolas", 9),
            state="disabled"
        )
        
        sync_scrollbar = ttk.Scrollbar(info_frame, command=self.sync_info_text.yview)
        self.sync_info_text.configure(yscrollcommand=sync_scrollbar.set)
        
        self.sync_info_text.pack(side=LEFT, fill=BOTH, expand=True)
        sync_scrollbar.pack(side=RIGHT, fill=Y)
        
        # 按钮区域
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=X)
        
        self.sync_cancel_btn = ttk.Button(
            btn_frame,
            text="取消同步",
            command=self._cancel_sync,
            bootstyle="danger"
        )
        self.sync_cancel_btn.pack(side=LEFT)
        
        self.sync_close_btn = ttk.Button(
            btn_frame,
            text="关闭",
            command=self._close_sync_window,
            state="disabled"
        )
        self.sync_close_btn.pack(side=RIGHT)
        
        # 开始同步
        self._start_sync_task()
    
    def _start_sync_task(self):
        """启动同步任务"""
        import threading
        
        def sync_worker():
            try:
                self._add_sync_message("🔍 检查飞书配置...")
                
                # 检查配置
                config = get_config().feishu
                if not all([config.app_id, config.app_secret, config.base_url]):
                    raise ValueError("飞书配置不完整，请检查配置文件")
                
                self._add_sync_message("✅ 配置检查通过")
                self.sync_status_var.set("正在连接飞书...")
                self.sync_progress_var.set(10)
                
                # 测试连接
                from ..services.feishu_client import SyncFeishuClient
                feishu_client = SyncFeishuClient()
                
                if not feishu_client.test_connection():
                    raise ConnectionError("无法连接到飞书API，请检查网络和配置")
                
                self._add_sync_message("✅ 飞书连接成功")
                self.sync_status_var.set("正在同步数据...")
                self.sync_progress_var.set(30)
                
                # 执行同步
                total_profiles = len(self.profiles)
                synced_count = 0
                failed_count = 0
                
                for i, profile in enumerate(self.profiles):
                    try:
                        # 模拟同步过程（这里需要根据实际的飞书同步API调整）
                        self._add_sync_message(f"同步用户: {profile.sender_name or '未知用户'}")
                        
                        # 实际的同步逻辑会在这里调用
                        # result = await feishu_client.sync_profile(profile)
                        
                        synced_count += 1
                        progress = 30 + (i + 1) / total_profiles * 60
                        self.sync_progress_var.set(progress)
                        self.sync_status_var.set(f"同步进度: {i+1}/{total_profiles}")
                        
                        # 模拟延迟
                        import time
                        time.sleep(0.1)
                        
                    except Exception as e:
                        failed_count += 1
                        self._add_sync_message(f"❌ 同步失败: {profile.sender_name} - {str(e)}")
                
                # 完成同步
                self.sync_progress_var.set(100)
                self.sync_status_var.set("同步完成")
                
                success_msg = f"✅ 同步完成！成功: {synced_count}, 失败: {failed_count}"
                self._add_sync_message(success_msg)
                
                # 启用关闭按钮，禁用取消按钮
                self.sync_cancel_btn.config(state="disabled")
                self.sync_close_btn.config(state="normal")
                
                # 显示完成消息
                messagebox.showinfo("同步完成", success_msg)
                
            except Exception as e:
                self._add_sync_message(f"❌ 同步失败: {str(e)}")
                self.sync_status_var.set("同步失败")
                self.sync_cancel_btn.config(state="disabled") 
                self.sync_close_btn.config(state="normal")
                messagebox.showerror("同步失败", f"飞书同步失败:\n{str(e)}")
        
        # 启动后台线程
        sync_thread = threading.Thread(target=sync_worker, daemon=True)
        sync_thread.start()
    
    def _add_sync_message(self, message: str):
        """添加同步消息"""
        def add_message():
            timestamp = datetime.now().strftime("%H:%M:%S")
            formatted_message = f"[{timestamp}] {message}\n"
            
            self.sync_info_text.config(state="normal")
            self.sync_info_text.insert(tk.END, formatted_message)
            self.sync_info_text.see(tk.END)
            self.sync_info_text.config(state="disabled")
        
        # 在主线程中执行UI更新
        self.parent.after(0, add_message)
    
    def _cancel_sync(self):
        """取消同步"""
        result = messagebox.askyesno("确认取消", "确定要取消同步吗？")
        if result:
            self._add_sync_message("❌ 用户取消了同步")
            self.sync_status_var.set("同步已取消")
            self.sync_cancel_btn.config(state="disabled")
            self.sync_close_btn.config(state="normal")
    
    def _close_sync_window(self):
        """关闭同步窗口"""
        if hasattr(self, 'sync_window'):
            self.sync_window.destroy()
    
    def on_progress_update(self, event: str, data: Dict[str, Any]):
        """处理进度更新事件"""
        try:
            # 根据不同事件更新界面
            if event == "analysis_completed":
                # 分析完成后自动刷新
                logger.info("分析任务完成，自动刷新结果数据")
                self.refresh_results()
            elif event == "profiles_extracted":
                # 画像提取完成后也刷新一次
                logger.info(f"画像提取完成，提取了 {data.get('high_quality', 0)} 个高质量画像")
                self.refresh_results()
            elif event == "sync_completed":
                # 同步完成后刷新
                logger.info(f"飞书同步完成，同步了 {data.get('synced', 0)} 个画像")
                self.refresh_results()
        except Exception as e:
            logger.error(f"处理进度更新失败: {e}") 