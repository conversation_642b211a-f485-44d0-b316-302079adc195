"""
数据模型定义
定义应用程序中使用的所有数据结构，包括用户画像、群组信息、消息等
"""

from dataclasses import dataclass, field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class MessageType(Enum):
    """消息类型"""
    TEXT = "text"
    IMAGE = "image"
    FILE = "file"
    SYSTEM = "system"

class IntroductionStatus(Enum):
    """自我介绍处理状态"""
    PENDING = "pending"  # 待处理
    PROCESSING = "processing"  # 处理中
    DETECTED = "detected"  # 已检测为自我介绍
    EXTRACTED = "extracted"  # 已提取用户画像
    EXTRACTION_FAILED = "extraction_failed"  # 提取用户画像失败
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"  # 失败
    SKIPPED = "skipped"  # 跳过

class ProfileField(Enum):
    """用户画像字段"""
    NICKNAME = "nickname"  # 昵称
    INDUSTRY = "industry"  # 行业领域
    PERSONAL_INTRO = "personal_intro"  # 个人介绍
    LOCATION = "location"  # 地区
    CAN_PROVIDE = "can_provide"  # 能够提供
    NEED_HELP = "need_help"  # 需要帮助
    TAGS = "tags"  # 标签

@dataclass
class Message:
    """消息数据模型"""
    id: str  # 消息ID
    chat_id: str  # 聊天ID（群组ID）
    user_id: str  # 用户ID
    username: str  # 用户名
    content: str  # 消息内容
    message_type: MessageType  # 消息类型
    timestamp: datetime  # 发送时间
    reply_to: Optional[str] = None  # 回复的消息ID
    media_url: Optional[str] = None  # 媒体文件URL
    raw_data: Dict[str, Any] = field(default_factory=dict)  # 原始数据

@dataclass
class ChatGroup:
    """微信群组数据模型"""
    id: str  # 群组ID
    name: str  # 群组名称
    description: Optional[str] = None  # 群组描述
    member_count: int = 0  # 成员数量
    is_active: bool = True  # 是否活跃
    last_message_time: Optional[datetime] = None  # 最后消息时间
    created_time: Optional[datetime] = None  # 创建时间
    settings: Dict[str, Any] = field(default_factory=dict)  # 群组设置

@dataclass
class UserProfile:
    """用户画像数据模型"""
    id: str  # 唯一标识
    introduction_id: str  # 自我介绍候选ID
    chat_id: str  # 群组ID
    sender_id: str  # 发送者ID
    sender_name: str  # 发送者名称
    
    # 简化的用户画像字段
    nickname: Optional[str] = None  # 昵称
    industry: Optional[str] = None  # 行业领域
    personal_intro: Optional[str] = None  # 个人介绍
    location: Optional[str] = None  # 地区
    can_provide: List[str] = field(default_factory=list)  # 能提供的服务
    need_help: List[str] = field(default_factory=list)  # 需要的帮助
    tags: List[str] = field(default_factory=list)  # 标签
    
    # 元数据
    original_content: str = ""  # 原始消息内容
    confidence_score: float = 0.0  # 提取置信度
    extraction_timestamp: datetime = field(default_factory=datetime.now)  # 提取时间
    ai_request_id: str = ""  # AI请求ID
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'introduction_id': self.introduction_id,
            'chat_id': self.chat_id,
            'sender_id': self.sender_id,
            'sender_name': self.sender_name,
            'nickname': self.nickname,
            'industry': self.industry,
            'personal_intro': self.personal_intro,
            'location': self.location,
            'can_provide': self.can_provide,
            'need_help': self.need_help,
            'tags': self.tags,
            'original_content': self.original_content,
            'confidence_score': self.confidence_score,
            'extraction_timestamp': self.extraction_timestamp.isoformat(),
            'ai_request_id': self.ai_request_id
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserProfile':
        """从字典创建实例"""
        return cls(
            id=data['id'],
            introduction_id=data['introduction_id'],
            chat_id=data['chat_id'],
            sender_id=data['sender_id'],
            sender_name=data['sender_name'],
            nickname=data.get('nickname'),
            industry=data.get('industry'),
            personal_intro=data.get('personal_intro'),
            location=data.get('location'),
            can_provide=data.get('can_provide', []),
            need_help=data.get('need_help', []),
            tags=data.get('tags', []),
            original_content=data.get('original_content', ''),
            confidence_score=data.get('confidence_score', 0.0),
            extraction_timestamp=datetime.fromisoformat(data['extraction_timestamp']),
            ai_request_id=data.get('ai_request_id', '')
        )

@dataclass
class IntroductionCandidate:
    """自我介绍候选项"""
    id: str  # 候选ID
    message_id: str  # 消息ID
    sender_id: str  # 发送者ID
    sender_name: str  # 发送者名称
    content: str  # 消息内容
    timestamp: datetime  # 消息时间戳
    confidence_score: float = 0.0  # 置信度
    detection_reason: str = ""  # 判断理由
    keywords: List[str] = field(default_factory=list)  # 关键词
    introduction_type: str = ""  # 自我介绍类型
    ai_request_id: str = ""  # AI请求ID
    status: IntroductionStatus = IntroductionStatus.PENDING  # 处理状态

@dataclass
class AnalysisTask:
    """分析任务"""
    id: str  # 任务ID
    chat_id: str  # 群组ID
    start_time: datetime  # 开始时间
    end_time: Optional[datetime] = None  # 结束时间
    total_messages: int = 0  # 总消息数
    processed_messages: int = 0  # 已处理消息数
    found_introductions: int = 0  # 找到的自我介绍数
    extracted_profiles: int = 0  # 提取的用户画像数
    synced_to_feishu: int = 0  # 同步到飞书的数量
    status: str = "running"  # 任务状态
    error_messages: List[str] = field(default_factory=list)  # 错误信息
    
    @property
    def progress(self) -> float:
        """获取进度百分比"""
        if self.total_messages == 0:
            return 0.0
        return (self.processed_messages / self.total_messages) * 100

@dataclass
class FeishuTableConfig:
    """飞书多维表格配置"""
    app_token: str  # 多维表格app_token
    table_id: str  # 表格ID
    view_id: Optional[str] = None  # 视图ID
    field_mapping: Dict[str, str] = field(default_factory=dict)  # 字段映射
    
    def get_field_id(self, profile_field: ProfileField) -> Optional[str]:
        """获取画像字段对应的飞书字段ID"""
        return self.field_mapping.get(profile_field.value)

@dataclass
class SyncRecord:
    """同步记录"""
    id: str  # 记录ID
    profile_id: str  # 用户画像ID
    feishu_record_id: str  # 飞书记录ID
    sync_time: datetime  # 同步时间
    sync_status: str  # 同步状态
    error_message: Optional[str] = None  # 错误信息

@dataclass
class AppState:
    """应用程序状态"""
    current_chat_id: Optional[str] = None  # 当前选中的群组ID
    is_analyzing: bool = False  # 是否正在分析
    current_task: Optional[AnalysisTask] = None  # 当前任务
    last_sync_time: Optional[datetime] = None  # 最后同步时间
    api_status: Dict[str, str] = field(default_factory=dict)  # API状态
    completed_profiles: List[UserProfile] = field(default_factory=list)  # 已完成的用户画像
    
    def update_api_status(self, api_name: str, status: str):
        """更新API状态"""
        self.api_status[api_name] = status 
    
    def add_completed_profiles(self, profiles: List[UserProfile]):
        """添加已完成的用户画像"""
        self.completed_profiles.extend(profiles)
    
    def clear_completed_profiles(self):
        """清空已完成的用户画像"""
        self.completed_profiles.clear()
    
    def get_profiles_by_chat(self, chat_id: str) -> List[UserProfile]:
        """获取指定群组的用户画像"""
        return [p for p in self.completed_profiles if p.chat_id == chat_id] 