"""
用户画像提取器
从自我介绍消息中提取结构化的用户画像信息
"""

import asyncio
import uuid
from typing import List, Dict, Any, Optional
import logging
from datetime import datetime

from ..models import UserProfile, IntroductionCandidate, IntroductionStatus, ProfileField
from ..config import get_config
from .deepseek_client import DeepSeekClient

logger = logging.getLogger(__name__)

class ProfileExtractor:
    """用户画像提取器"""
    
    def __init__(self):
        self.config = get_config().analysis
        self.deepseek_client = DeepSeekClient()
    
    async def extract_profiles(self, candidates: List[IntroductionCandidate]) -> List[UserProfile]:
        """
        从自我介绍候选项中提取用户画像
        
        Args:
            candidates: 自我介绍候选项列表
            
        Returns:
            用户画像列表
        """
        profiles = []
        
        # 过滤出确认的自我介绍
        introductions = [c for c in candidates if c.is_introduction]
        
        if not introductions:
            logger.info("没有确认的自我介绍，跳过画像提取")
            return []
        
        logger.info(f"开始提取 {len(introductions)} 个自我介绍的用户画像")
        
        # 批量处理
        batch_size = self.config.batch_size
        for i in range(0, len(introductions), batch_size):
            batch = introductions[i:i + batch_size]
            batch_profiles = await self._extract_batch_profiles(batch)
            profiles.extend(batch_profiles)
            
            # 添加延迟避免API限制
            if i + batch_size < len(introductions):
                await asyncio.sleep(1)
        
        logger.info(f"提取完成，生成 {len(profiles)} 个用户画像")
        return profiles
    
    async def _extract_batch_profiles(self, batch: List[IntroductionCandidate]) -> List[UserProfile]:
        """批量提取用户画像"""
        tasks = []
        for candidate in batch:
            task = self._extract_single_profile(candidate)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        profiles = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"提取用户画像失败: {result}")
                # 创建一个失败的画像记录
                failed_profile = self._create_failed_profile(batch[i], str(result))
                profiles.append(failed_profile)
            elif result:
                profiles.append(result)
        
        return profiles
    
    async def _extract_single_profile(self, candidate: IntroductionCandidate) -> Optional[UserProfile]:
        """提取单个用户画像"""
        try:
            message = candidate.message
            
            # 调用AI提取画像信息
            extraction_result = await self.deepseek_client.extract_profile(message.content)
            
            if not extraction_result.get('success', False):
                logger.warning(f"AI提取失败: {extraction_result.get('error', '未知错误')}")
                return self._create_failed_profile(candidate, extraction_result.get('error', '提取失败'))
            
            # 解析提取的画像数据
            profile_data = extraction_result.get('profile', {})
            confidence = extraction_result.get('confidence', 0.0)
            
            # 创建用户画像对象
            profile = UserProfile(
                id=str(uuid.uuid4()),
                user_id=message.user_id,
                chat_id=message.chat_id,
                message_id=message.id,
                nickname=self._clean_field_value(profile_data.get('nickname')),
                profession=self._clean_field_value(profile_data.get('profession')),
                personal_intro=self._clean_field_value(profile_data.get('personal_intro')),
                can_provide=self._clean_field_value(profile_data.get('can_provide')),
                looking_for=self._clean_field_value(profile_data.get('looking_for')),
                industry=self._clean_field_value(profile_data.get('industry')),
                confidence_score=confidence,
                status=IntroductionStatus.COMPLETED,
                raw_content=message.content,
                extraction_log=[{
                    'timestamp': datetime.now().isoformat(),
                    'method': 'ai_extraction',
                    'confidence': confidence,
                    'raw_response': extraction_result.get('raw_response', '')
                }]
            )
            
            # 验证画像数据质量
            self._validate_profile(profile)
            
            logger.info(f"成功提取用户画像: {profile.id} (置信度: {confidence:.2f})")
            return profile
            
        except Exception as e:
            logger.error(f"提取用户画像异常: {e}")
            return self._create_failed_profile(candidate, str(e))
    
    def _clean_field_value(self, value: Any) -> Optional[str]:
        """清理字段值"""
        if value is None or value == 'null' or value == '':
            return None
        
        if isinstance(value, str):
            cleaned = value.strip()
            # 移除常见的无效值
            invalid_values = ['无', '暂无', 'null', 'None', 'N/A', 'n/a', '不详', '未知']
            if cleaned.lower() in [v.lower() for v in invalid_values]:
                return None
            return cleaned if cleaned else None
        
        return str(value).strip() if str(value).strip() else None
    
    def _validate_profile(self, profile: UserProfile):
        """验证用户画像数据质量"""
        # 检查是否至少有一个有效字段
        fields_to_check = [
            profile.nickname,
            profile.profession,
            profile.personal_intro,
            profile.can_provide,
            profile.looking_for,
            profile.industry
        ]
        
        valid_fields = [f for f in fields_to_check if f is not None]
        
        if not valid_fields:
            logger.warning(f"用户画像 {profile.id} 没有有效字段")
            profile.status = IntroductionStatus.FAILED
            profile.extraction_log.append({
                'timestamp': datetime.now().isoformat(),
                'method': 'validation',
                'error': '没有有效的提取字段'
            })
        
        # 检查置信度
        if profile.confidence_score < self.config.confidence_threshold:
            logger.warning(f"用户画像 {profile.id} 置信度过低: {profile.confidence_score}")
            profile.extraction_log.append({
                'timestamp': datetime.now().isoformat(),
                'method': 'validation',
                'warning': f'置信度低于阈值 {self.config.confidence_threshold}'
            })
    
    def _create_failed_profile(self, candidate: IntroductionCandidate, error_msg: str) -> UserProfile:
        """创建失败的用户画像记录"""
        message = candidate.message
        
        return UserProfile(
            id=str(uuid.uuid4()),
            user_id=message.user_id,
            chat_id=message.chat_id,
            message_id=message.id,
            confidence_score=0.0,
            status=IntroductionStatus.FAILED,
            raw_content=message.content,
            extraction_log=[{
                'timestamp': datetime.now().isoformat(),
                'method': 'extraction_error',
                'error': error_msg
            }]
        )
    
    async def enhance_profile(self, profile: UserProfile, additional_messages: List[Any] = None) -> UserProfile:
        """
        增强用户画像信息
        
        Args:
            profile: 原始用户画像
            additional_messages: 该用户的其他消息（用于补充信息）
            
        Returns:
            增强后的用户画像
        """
        try:
            # 如果有额外的消息，可以用于补充画像信息
            if additional_messages:
                logger.info(f"使用 {len(additional_messages)} 条额外消息增强画像 {profile.id}")
                
                # 合并消息内容
                combined_content = profile.raw_content
                for msg in additional_messages:
                    if hasattr(msg, 'content') and msg.content.strip():
                        combined_content += "\n" + msg.content
                
                # 重新提取画像
                enhanced_result = await self.deepseek_client.extract_profile(combined_content)
                
                if enhanced_result.get('success', False):
                    enhanced_data = enhanced_result.get('profile', {})
                    
                    # 更新字段（只有原字段为空时才更新）
                    if not profile.nickname and enhanced_data.get('nickname'):
                        profile.nickname = self._clean_field_value(enhanced_data['nickname'])
                    
                    if not profile.profession and enhanced_data.get('profession'):
                        profile.profession = self._clean_field_value(enhanced_data['profession'])
                    
                    if not profile.personal_intro and enhanced_data.get('personal_intro'):
                        profile.personal_intro = self._clean_field_value(enhanced_data['personal_intro'])
                    
                    if not profile.can_provide and enhanced_data.get('can_provide'):
                        profile.can_provide = self._clean_field_value(enhanced_data['can_provide'])
                    
                    if not profile.looking_for and enhanced_data.get('looking_for'):
                        profile.looking_for = self._clean_field_value(enhanced_data['looking_for'])
                    
                    if not profile.industry and enhanced_data.get('industry'):
                        profile.industry = self._clean_field_value(enhanced_data['industry'])
                    
                    # 更新置信度（取更高值）
                    enhanced_confidence = enhanced_result.get('confidence', 0.0)
                    if enhanced_confidence > profile.confidence_score:
                        profile.confidence_score = enhanced_confidence
                    
                    # 更新时间和日志
                    profile.updated_time = datetime.now()
                    profile.extraction_log.append({
                        'timestamp': datetime.now().isoformat(),
                        'method': 'enhancement',
                        'confidence': enhanced_confidence,
                        'additional_messages_count': len(additional_messages)
                    })
                    
                    logger.info(f"成功增强用户画像 {profile.id}")
            
            return profile
            
        except Exception as e:
            logger.error(f"增强用户画像失败: {e}")
            profile.extraction_log.append({
                'timestamp': datetime.now().isoformat(),
                'method': 'enhancement_error',
                'error': str(e)
            })
            return profile
    
    def get_extraction_stats(self, profiles: List[UserProfile]) -> Dict[str, Any]:
        """
        获取提取统计信息
        
        Args:
            profiles: 用户画像列表
            
        Returns:
            统计信息
        """
        if not profiles:
            return {
                'total_profiles': 0,
                'successful_extractions': 0,
                'failed_extractions': 0,
                'average_confidence': 0.0,
                'field_coverage': {},
                'extraction_rate': 0.0
            }
        
        total = len(profiles)
        successful = sum(1 for p in profiles if p.status == IntroductionStatus.COMPLETED)
        failed = total - successful
        
        # 计算平均置信度
        successful_profiles = [p for p in profiles if p.status == IntroductionStatus.COMPLETED]
        avg_confidence = 0.0
        if successful_profiles:
            total_confidence = sum(p.confidence_score for p in successful_profiles)
            avg_confidence = total_confidence / len(successful_profiles)
        
        # 计算字段覆盖率
        field_coverage = {}
        if successful_profiles:
            fields = ['nickname', 'profession', 'personal_intro', 'can_provide', 'looking_for', 'industry']
            for field in fields:
                covered = sum(1 for p in successful_profiles if getattr(p, field) is not None)
                field_coverage[field] = covered / len(successful_profiles)
        
        return {
            'total_profiles': total,
            'successful_extractions': successful,
            'failed_extractions': failed,
            'average_confidence': avg_confidence,
            'field_coverage': field_coverage,
            'extraction_rate': successful / total if total > 0 else 0.0
        }
    
    def filter_high_quality_profiles(self, profiles: List[UserProfile]) -> List[UserProfile]:
        """
        过滤高质量的用户画像
        
        Args:
            profiles: 用户画像列表
            
        Returns:
            高质量的用户画像列表
        """
        high_quality_profiles = []
        
        for profile in profiles:
            # 检查状态
            if profile.status != IntroductionStatus.COMPLETED:
                continue
            
            # 检查置信度
            if profile.confidence_score < self.config.confidence_threshold:
                continue
            
            # 检查字段完整性
            valid_fields = 0
            if profile.nickname:
                valid_fields += 1
            if profile.profession:
                valid_fields += 1
            if profile.personal_intro:
                valid_fields += 1
            if profile.can_provide:
                valid_fields += 1
            if profile.looking_for:
                valid_fields += 1
            if profile.industry:
                valid_fields += 1
            
            # 至少需要3个有效字段
            if valid_fields >= 3:
                high_quality_profiles.append(profile)
        
        logger.info(f"过滤出 {len(high_quality_profiles)}/{len(profiles)} 个高质量用户画像")
        return high_quality_profiles 