# 微信群用户档案自动分析器 - 需求文档

## 📋 项目概述

### 1.1 项目背景
随着社群经济的快速发展，微信群已成为重要的社交和商业场景。社群运营人员需要快速了解群成员的背景信息，以便进行精准的社群管理和资源匹配。目前，群成员的自我介绍信息分散在聊天记录中，格式不统一，人工整理效率低下且容易遗漏。

### 1.2 项目目标
开发一个基于AI的自动化工具，能够从微信群聊天记录中智能识别和提取用户自我介绍信息，并将其结构化存储到飞书多维表格中，帮助社群运营人员高效管理群成员档案。

### 1.3 目标用户
- **主要用户**：社群运营人员
- **用户特征**：
  - 无技术背景或技术背景较弱
  - 日常管理多个微信群
  - 需要了解群成员背景进行精准运营
  - 使用飞书等协作工具进行数据管理

### 1.4 应用场景
- **兴趣社群**：技术交流群、行业交流群、学习群等
- **商务社群**：创业者群、投资人群、合作伙伴群等
- **知识付费群**：课程学习群、专业咨询群等
- **群规模**：主要针对几十到几百人的中大型群组

## 🎯 功能需求

### 2.1 核心功能

#### 2.1.1 自我介绍智能识别
- **功能描述**：从微信群聊天记录中自动识别用户的自我介绍消息
- **识别策略**：
  - 基于关键词模式匹配（大家好、我是、来自等）
  - 结合AI大模型进行语义识别
  - 考虑新成员入群时间窗口
  - 优先保证精确率，宁缺毋滥
- **输入**：微信群聊天记录
- **输出**：识别出的自我介绍消息列表

#### 2.1.2 结构化信息提取
- **功能描述**：从自我介绍文本中提取结构化的用户信息
- **提取字段**：
  - 昵称（必填）
  - 职业/职位
  - 个人介绍/标签
  - 能够提供的资源/服务
  - 希望得到的帮助/资源
  - 所在行业
- **技术要求**：
  - 使用LlamaIndex框架进行信息提取，底层调用DeepSeek API
  - 提供置信度评分和结构化输出
  - 支持中文自然语言处理
  - 处理非标准格式的自我介绍
  - 利用Pydantic进行数据验证和类型检查

#### 2.1.3 数据存储与同步
- **功能描述**：将提取的结构化信息存储并同步到飞书多维表格
- **存储要求**：
  - 支持新增和更新用户档案
  - 记录数据提取的置信度和时间戳
  - 保留原始自我介绍文本用于追溯
  - 避免重复数据，支持去重机制
- **同步功能**：
  - 实时或批量同步到飞书多维表格
  - 支持手动触发同步
  - 提供同步状态反馈

### 2.2 辅助功能

#### 2.2.1 群组管理
- **功能描述**：管理和选择要分析的微信群组
- **功能点**：
  - 获取用户加入的群组列表
  - 显示群组基本信息（群名、成员数等）
  - 支持群组选择和批量操作
  - 记录分析历史和状态

#### 2.2.2 任务调度
- **功能描述**：支持定时和手动触发分析任务
- **调度方式**：
  - 手动一键分析
  - 定时自动分析（每日、每周）
  - 增量分析（只处理新消息）
- **任务监控**：
  - 实时显示分析进度
  - 提供详细的分析日志
  - 错误处理和重试机制

#### 2.2.3 结果展示与导出
- **功能描述**：展示分析结果并支持数据导出
- **展示功能**：
  - 表格形式展示用户档案
  - 提供筛选和排序功能
  - 显示统计信息（总数、置信度分布等）
- **导出功能**：
  - 导出Excel格式文件
  - 一键跳转到飞书多维表格
  - 支持数据备份和恢复

### 2.3 配置管理
- **LlamaIndex配置**：DeepSeek API密钥、模型参数、温度设置等
- **飞书配置**：飞书应用信息和表格配置
- **Chatlog配置**：Chatlog服务连接配置
- **分析参数**：分析天数、置信度阈值、消息长度限制等参数配置

## 🔧 非功能性需求

### 3.1 性能要求
- **响应时间**：单次分析任务完成时间不超过5分钟（100条消息）
- **并发处理**：支持单用户多群组同时分析
- **数据处理量**：单次处理不超过1000条消息
- **内存占用**：运行时内存占用不超过500MB

### 3.2 可用性要求
- **界面友好**：图形化界面，操作直观简单
- **错误处理**：完善的错误提示和恢复机制
- **用户指导**：提供配置向导和使用说明
- **容错性**：网络异常或API调用失败时的优雅降级

### 3.3 安全性要求
- **数据隐私**：
  - 所有数据处理在本地进行
  - API密钥本地加密存储
  - 不上传聊天记录到第三方服务器
- **访问控制**：
  - 只处理用户主动加入的群组
  - 不处理敏感个人信息（手机号、身份证等）
- **数据安全**：
  - 支持配置文件备份和恢复
  - 异常情况下的数据保护

### 3.4 兼容性要求
- **操作系统**：Windows 10+（主要），macOS 10.15+（次要）
- **微信版本**：支持微信3.x和4.0版本
- **Chatlog版本**：兼容最新版本的chatlog
- **Python版本**：基于Python 3.8+开发

### 3.5 部署要求
- **打包方式**：单文件可执行程序，无需安装Python环境
- **文件大小**：打包后不超过50MB
- **依赖管理**：所有依赖内置，用户无需额外安装
- **更新机制**：支持版本检查和更新提醒

## 📊 数据流规范

### 4.1 输入数据
- **数据源**：Chatlog API提供的微信群聊天记录
- **数据格式**：JSON格式的消息数组
- **数据字段**：
  ```json
  {
    "sender": "wxid_xxx",
    "sender_nickname": "张三",
    "content": "大家好，我是张三...",
    "timestamp": "2024-01-01 10:00:00",
    "message_type": "text"
  }
  ```

### 4.2 输出数据
- **目标格式**：飞书多维表格记录
- **数据结构**：
  ```json
  {
    "微信ID": "wxid_xxx",
    "昵称": "张三",
    "职业": "产品经理",
    "个人介绍": "5年互联网产品经验",
    "能够提供": "产品设计咨询",
    "希望得到": "技术合作机会",
    "所在行业": "互联网",
    "原始消息": "完整的自我介绍文本",
    "置信度": 0.85,
    "入群日期": "2024-01-01",
    "最后更新": "2024-01-01 15:30:00"
  }
  ```

### 4.3 数据质量要求
- **完整性**：必填字段不能为空
- **准确性**：置信度不低于0.7的记录才写入表格
- **一致性**：同一用户的多次自我介绍取最新且置信度最高的记录
- **时效性**：数据更新延迟不超过1分钟

## 🚀 项目约束

### 5.1 技术约束
- **基础框架**：必须基于chatlog项目，利用其微信数据解密能力
- **AI服务**：使用LlamaIndex框架，底层集成DeepSeek API，不依赖其他LLM服务
- **界面技术**：使用Python tkinter/ttkbootstrap，确保跨平台兼容
- **数据存储**：主要数据存储在飞书多维表格，本地仅缓存配置

### 5.2 业务约束
- **合规要求**：
  - 仅处理用户自己的聊天数据
  - 遵守微信使用条款
  - 不用于商业数据买卖
- **使用限制**：
  - 个人学习和正当社群运营使用
  - 不得用于恶意收集他人隐私信息

### 5.3 成本约束
- **API成本**：DeepSeek API调用成本控制在合理范围内
- **开发周期**：项目开发周期不超过4周
- **维护成本**：保持代码简洁，便于后续维护

## 📈 成功标准

### 6.1 功能指标
- **识别准确率**：自我介绍识别准确率 ≥ 90%
- **提取完整度**：关键信息提取完整度 ≥ 80%
- **数据同步率**：数据同步成功率 ≥ 95%

### 6.2 用户体验指标
- **操作简易度**：非技术用户在10分钟内完成首次配置
- **界面友好度**：用户界面满意度 ≥ 4/5分
- **错误处理**：用户遇到错误时能通过界面提示自行解决 ≥ 80%

### 6.3 技术指标
- **稳定性**：连续运行24小时无崩溃
- **兼容性**：在主流Windows系统上正常运行 ≥ 95%
- **性能**：处理100条消息用时 ≤ 2分钟

## 🔄 后续迭代计划

### Phase 1（当前版本）
- 基础的自我介绍识别和信息提取
- 简单的图形化界面
- 飞书多维表格基础集成

### Phase 2（未来版本）
- 支持更多数据源（QQ群、钉钉群等）
- 增加数据分析和可视化功能
- 优化AI提取模型，提高准确率

### Phase 3（长期规划）
- 开发Web版本，支持团队协作
- 集成更多办公平台（钉钉、企业微信等）
- 提供API接口供第三方系统集成

---

**文档版本**：v1.0  
**最后更新**：2024年12月  
**负责人**：项目团队  
**审核状态**：待审核 