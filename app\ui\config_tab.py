"""
配置设置标签页
用于设置API配置和分析参数
"""

import tkinter as tk
from tkinter import messagebox
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import logging
from typing import Dict, Any

from ..config import get_config, save_config, update_config

logger = logging.getLogger(__name__)

class ConfigTab:
    """配置设置标签页"""
    
    def __init__(self, parent, analysis_service):
        self.parent = parent
        self.analysis_service = analysis_service
        
        # 创建主框架
        self.frame = ttk.Frame(parent)
        
        # 配置变量
        self.config_vars = {}
        
        # 创建界面
        self._create_widgets()
        self._load_config()
        
        logger.debug("配置标签页初始化完成")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 创建滚动容器
        canvas = tk.Canvas(self.frame)
        scrollbar = ttk.Scrollbar(self.frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 布局滚动组件
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 创建配置向导和分组
        self._create_wizard_section(scrollable_frame)
        self._create_chatlog_config(scrollable_frame)
        self._create_deepseek_config(scrollable_frame)
        self._create_feishu_config(scrollable_frame)
        self._create_analysis_config(scrollable_frame)
        self._create_ui_config(scrollable_frame)
        self._create_buttons(scrollable_frame)
    
    def _create_wizard_section(self, parent):
        """创建配置向导区域"""
        wizard_frame = ttk.LabelFrame(parent, text="🚀 快速配置向导", padding=15)
        wizard_frame.pack(fill=X, padx=10, pady=5)
        
        # 欢迎文本
        welcome_text = """欢迎使用微信群自我介绍分析工具！
        
为了帮助您快速开始使用，请按照以下步骤进行配置：
1️⃣ 配置AI分析服务（DeepSeek API）
2️⃣ 配置聊天记录服务（Chatlog API）  
3️⃣ 配置飞书同步（可选）
4️⃣ 测试连接并开始使用

💡 提示：带有红色星号(*)的为必填项目"""
        
        welcome_label = ttk.Label(
            wizard_frame, 
            text=welcome_text,
            font=("微软雅黑", 10),
            justify=LEFT
        )
        welcome_label.pack(fill=X, pady=(0, 15))
        
        # 配置状态指示器
        status_frame = ttk.LabelFrame(wizard_frame, text="配置状态", padding=10)
        status_frame.pack(fill=X, pady=(0, 10))
        
        self.status_vars = {
            'deepseek': tk.StringVar(value="❌ 未配置"),
            'chatlog': tk.StringVar(value="❌ 未配置"),
            'feishu': tk.StringVar(value="⚪ 可选")
        }
        
        ttk.Label(status_frame, text="AI分析服务:").grid(row=0, column=0, sticky=W, padx=(0, 10))
        ttk.Label(status_frame, textvariable=self.status_vars['deepseek']).grid(row=0, column=1, sticky=W)
        
        ttk.Label(status_frame, text="聊天记录服务:").grid(row=1, column=0, sticky=W, padx=(0, 10))
        ttk.Label(status_frame, textvariable=self.status_vars['chatlog']).grid(row=1, column=1, sticky=W)
        
        ttk.Label(status_frame, text="飞书同步:").grid(row=2, column=0, sticky=W, padx=(0, 10))
        ttk.Label(status_frame, textvariable=self.status_vars['feishu']).grid(row=2, column=1, sticky=W)
        
        # 快速操作按钮
        quick_actions = ttk.Frame(wizard_frame)
        quick_actions.pack(fill=X, pady=(10, 0))
        
        ttk.Button(
            quick_actions,
            text="🔧 一键配置向导",
            command=self._start_config_wizard,
            bootstyle="success"
        ).pack(side=LEFT, padx=(0, 10))
        
        ttk.Button(
            quick_actions,
            text="📖 查看使用教程",
            command=self._show_tutorial,
            bootstyle="info-outline"
        ).pack(side=LEFT, padx=(0, 10))
        
        ttk.Button(
            quick_actions,
            text="🔗 测试所有连接",
            command=self._test_all_connections,
            bootstyle="warning-outline"
        ).pack(side=LEFT)
    
    def _create_chatlog_config(self, parent):
        """创建Chatlog配置组"""
        group = ttk.LabelFrame(parent, text="📡 聊天记录服务配置 *", padding=10)
        group.pack(fill=X, padx=10, pady=5)
        
        # 配置说明
        info_text = "连接到您的微信聊天记录导出服务，用于获取群组消息数据"
        ttk.Label(group, text=info_text, font=("微软雅黑", 9), foreground="gray").grid(
            row=0, column=0, columnspan=3, sticky=W, pady=(0, 10)
        )
        
        # API地址
        ttk.Label(group, text="*API服务地址:").grid(row=1, column=0, sticky=W, pady=2)
        self.config_vars['chatlog_base_url'] = tk.StringVar()
        base_url_entry = ttk.Entry(
            group, 
            textvariable=self.config_vars['chatlog_base_url'],
            width=50
        )
        base_url_entry.grid(row=1, column=1, sticky=W+E, padx=(10, 0), pady=2)
        
        ttk.Button(
            group,
            text="📖 获取地址教程",
            command=self._show_chatlog_tutorial,
            width=15
        ).grid(row=1, column=2, padx=(5, 0), pady=2)
        
        # 默认值提示
        default_hint = ttk.Label(
            group, 
            text="💡 默认地址: http://127.0.0.1:5030",
            font=("微软雅黑", 8), 
            foreground="blue"
        )
        default_hint.grid(row=2, column=1, sticky=W, padx=(10, 0))
        
        # 超时时间
        ttk.Label(group, text="请求超时(秒):").grid(row=3, column=0, sticky=W, pady=2)
        self.config_vars['chatlog_timeout'] = tk.IntVar()
        ttk.Spinbox(
            group,
            from_=10,
            to=120,
            textvariable=self.config_vars['chatlog_timeout'],
            width=10
        ).grid(row=3, column=1, sticky=W, padx=(10, 0), pady=2)
        
        # 最大重试次数
        ttk.Label(group, text="最大重试次数:").grid(row=4, column=0, sticky=W, pady=2)
        self.config_vars['chatlog_max_retries'] = tk.IntVar()
        ttk.Spinbox(
            group,
            from_=1,
            to=10,
            textvariable=self.config_vars['chatlog_max_retries'],
            width=10
        ).grid(row=4, column=1, sticky=W, padx=(10, 0), pady=2)
        
        # 配置第2列宽度
        group.columnconfigure(1, weight=1)
    
    def _create_deepseek_config(self, parent):
        """创建DeepSeek配置组"""
        group = ttk.LabelFrame(parent, text="🤖 AI分析服务配置 *", padding=10)
        group.pack(fill=X, padx=10, pady=5)
        
        # 配置说明
        info_text = "AI服务用于智能识别自我介绍内容并提取用户画像信息"
        ttk.Label(group, text=info_text, font=("微软雅黑", 9), foreground="gray").grid(
            row=0, column=0, columnspan=3, sticky=W, pady=(0, 10)
        )
        
        # 客户端类型选择
        ttk.Label(group, text="AI服务类型:").grid(row=1, column=0, sticky=W, pady=2)
        self.config_vars['deepseek_use_openai_like'] = tk.BooleanVar()
        
        client_frame = ttk.Frame(group)
        client_frame.grid(row=1, column=1, columnspan=2, sticky=W, padx=(10, 0), pady=2)
        
        deepseek_radio = ttk.Radiobutton(
            client_frame,
            text="DeepSeek官方服务",
            variable=self.config_vars['deepseek_use_openai_like'],
            value=False,
            command=self._on_client_type_changed
        )
        deepseek_radio.pack(side=LEFT, padx=(0, 15))
        
        openai_like_radio = ttk.Radiobutton(
            client_frame,
            text="OpenAI兼容服务",
            variable=self.config_vars['deepseek_use_openai_like'],
            value=True,
            command=self._on_client_type_changed
        )
        openai_like_radio.pack(side=LEFT)
        
        # 当前客户端状态显示
        self.client_status_label = ttk.Label(
            client_frame, 
            text="当前: DeepSeek官方服务", 
            bootstyle="info"
        )
        self.client_status_label.pack(side=LEFT, padx=(15, 0))
        
        # API密钥
        ttk.Label(group, text="*API密钥:").grid(row=2, column=0, sticky=W, pady=2)
        self.config_vars['deepseek_api_key'] = tk.StringVar()
        api_key_entry = ttk.Entry(
            group, 
            textvariable=self.config_vars['deepseek_api_key'],
            show="*",
            width=50
        )
        api_key_entry.grid(row=2, column=1, sticky=W+E, padx=(10, 0), pady=2)
        
        # 显示/隐藏密钥按钮
        def toggle_api_key():
            if api_key_entry.cget("show") == "*":
                api_key_entry.config(show="")
                toggle_btn.config(text="隐藏")
            else:
                api_key_entry.config(show="*")
                toggle_btn.config(text="显示")
        
        toggle_btn = ttk.Button(group, text="显示", command=toggle_api_key, width=8)
        toggle_btn.grid(row=2, column=2, padx=(5, 0), pady=2)
        
        # API获取教程按钮
        ttk.Button(
            group,
            text="📖 如何获取API密钥",
            command=self._show_deepseek_tutorial,
            bootstyle="info-outline"
        ).grid(row=3, column=0, columnspan=3, pady=(5, 0))
        
        # API基础地址
        ttk.Label(group, text="API服务地址:").grid(row=4, column=0, sticky=W, pady=2)
        self.config_vars['deepseek_base_url'] = tk.StringVar()
        ttk.Entry(
            group, 
            textvariable=self.config_vars['deepseek_base_url'],
            width=50
        ).grid(row=4, column=1, columnspan=2, sticky=W+E, padx=(10, 0), pady=2)
        
        # 模型名称
        ttk.Label(group, text="AI模型:").grid(row=5, column=0, sticky=W, pady=2)
        self.config_vars['deepseek_model'] = tk.StringVar()
        ttk.Combobox(
            group,
            textvariable=self.config_vars['deepseek_model'],
            values=["deepseek-chat", "deepseek-coder"],
            width=20
        ).grid(row=5, column=1, sticky=W, padx=(10, 0), pady=2)
        
        # 最大tokens
        ttk.Label(group, text="最大输出长度:").grid(row=6, column=0, sticky=W, pady=2)
        self.config_vars['deepseek_max_tokens'] = tk.IntVar()
        ttk.Spinbox(
            group,
            from_=1000,
            to=8000,
            increment=500,
            textvariable=self.config_vars['deepseek_max_tokens'],
            width=10
        ).grid(row=6, column=1, sticky=W, padx=(10, 0), pady=2)
        
        # 温度参数
        ttk.Label(group, text="创意度(0-1):").grid(row=7, column=0, sticky=W, pady=2)
        self.config_vars['deepseek_temperature'] = tk.DoubleVar()
        ttk.Scale(
            group,
            from_=0.0,
            to=1.0,
            variable=self.config_vars['deepseek_temperature'],
            orient=HORIZONTAL,
            length=200
        ).grid(row=7, column=1, sticky=W, padx=(10, 0), pady=2)
        
        temp_label = ttk.Label(group, text="0.1")
        temp_label.grid(row=7, column=2, padx=(5, 0), pady=2)
        
        def update_temp_label(*args):
            temp_label.config(text=f"{self.config_vars['deepseek_temperature'].get():.1f}")
        
        self.config_vars['deepseek_temperature'].trace('w', update_temp_label)
        
        # 立即应用客户端切换按钮
        switch_frame = ttk.Frame(group)
        switch_frame.grid(row=8, column=0, columnspan=3, pady=(10, 0))
        
        self.switch_client_btn = ttk.Button(
            switch_frame,
            text="立即应用客户端切换",
            command=self._switch_client_immediately,
            bootstyle="warning"
        )
        self.switch_client_btn.pack(side=LEFT)
        
        # 客户端信息显示
        info_btn = ttk.Button(
            switch_frame,
            text="查看客户端信息",
            command=self._show_client_info,
            bootstyle="info-outline"
        )
        info_btn.pack(side=LEFT, padx=(10, 0))
        
        group.columnconfigure(1, weight=1)
    
    def _create_feishu_config(self, parent):
        """创建飞书配置组"""
        group = ttk.LabelFrame(parent, text="📋 飞书多维表格配置（可选）", padding=10)
        group.pack(fill=X, padx=10, pady=5)
        
        # 配置说明
        info_text = "配置后可将分析结果自动同步到飞书多维表格，方便团队协作"
        ttk.Label(group, text=info_text, font=("微软雅黑", 9), foreground="gray").grid(
            row=0, column=0, columnspan=3, sticky=W, pady=(0, 10)
        )
        
        # 应用ID
        ttk.Label(group, text="应用ID (App ID):").grid(row=1, column=0, sticky=W, pady=2)
        self.config_vars['feishu_app_id'] = tk.StringVar()
        ttk.Entry(
            group, 
            textvariable=self.config_vars['feishu_app_id'],
            width=50
        ).grid(row=1, column=1, sticky=W+E, padx=(10, 0), pady=2)
        
        # 应用密钥
        ttk.Label(group, text="应用密钥 (App Secret):").grid(row=2, column=0, sticky=W, pady=2)
        self.config_vars['feishu_app_secret'] = tk.StringVar()
        secret_entry = ttk.Entry(
            group, 
            textvariable=self.config_vars['feishu_app_secret'],
            show="*",
            width=50
        )
        secret_entry.grid(row=2, column=1, sticky=W+E, padx=(10, 0), pady=2)
        
        # 显示/隐藏密钥按钮
        def toggle_secret():
            if secret_entry.cget("show") == "*":
                secret_entry.config(show="")
                toggle_secret_btn.config(text="隐藏")
            else:
                secret_entry.config(show="*")
                toggle_secret_btn.config(text="显示")
        
        toggle_secret_btn = ttk.Button(group, text="显示", command=toggle_secret, width=8)
        toggle_secret_btn.grid(row=2, column=2, padx=(5, 0), pady=2)
        
        # 表格Token
        ttk.Label(group, text="表格Token (App Token):").grid(row=3, column=0, sticky=W, pady=2)
        self.config_vars['feishu_app_token'] = tk.StringVar()
        token_entry = ttk.Entry(
            group, 
            textvariable=self.config_vars['feishu_app_token'],
            width=50
        )
        token_entry.grid(row=3, column=1, sticky=W+E, padx=(10, 0), pady=2)
        
        ttk.Button(
            group,
            text="❓ 如何获取",
            command=self._show_app_token_help,
            width=10
        ).grid(row=3, column=2, padx=(5, 0), pady=2)
        
        # App Token获取提示
        token_hint = ttk.Label(
            group, 
            text="💡 从飞书多维表格浏览器地址栏复制：/base/后面的字符串",
            font=("微软雅黑", 8), 
            foreground="blue"
        )
        token_hint.grid(row=4, column=1, sticky=W, padx=(10, 0))
        
        # API基础地址
        ttk.Label(group, text="API服务地址:").grid(row=5, column=0, sticky=W, pady=2)
        self.config_vars['feishu_base_url'] = tk.StringVar()
        ttk.Entry(
            group, 
            textvariable=self.config_vars['feishu_base_url'],
            width=50
        ).grid(row=5, column=1, columnspan=2, sticky=W+E, padx=(10, 0), pady=2)
        
        # 配置指南和测试按钮
        guide_frame = ttk.Frame(group)
        guide_frame.grid(row=6, column=0, columnspan=3, pady=(10, 0))
        
        ttk.Button(
            guide_frame,
            text="📖 完整配置指南",
            command=self._show_feishu_guide,
            bootstyle="info"
        ).pack(side=LEFT, padx=(0, 10))
        
        ttk.Button(
            guide_frame,
            text="🔌 测试飞书连接",
            command=self._test_feishu_connection,
            bootstyle="success-outline"
        ).pack(side=LEFT)
        
        group.columnconfigure(1, weight=1)
    
    def _create_analysis_config(self, parent):
        """创建分析配置组"""
        group = ttk.LabelFrame(parent, text="🔧 分析参数配置", padding=10)
        group.pack(fill=X, padx=10, pady=5)
        
        # 配置说明
        info_text = "调整分析性能和精度参数，默认设置适合大多数用户"
        ttk.Label(group, text=info_text, font=("微软雅黑", 9), foreground="gray").grid(
            row=0, column=0, columnspan=3, sticky=W, pady=(0, 10)
        )
        
        # 批处理大小
        ttk.Label(group, text="批处理大小:").grid(row=1, column=0, sticky=W, pady=2)
        self.config_vars['analysis_batch_size'] = tk.IntVar()
        ttk.Spinbox(
            group,
            from_=1,
            to=50,
            textvariable=self.config_vars['analysis_batch_size'],
            width=10
        ).grid(row=1, column=1, sticky=W, padx=(10, 0), pady=2)
        
        ttk.Label(group, text="(一次处理的消息数量)", font=("微软雅黑", 8), foreground="gray").grid(
            row=1, column=2, sticky=W, padx=(5, 0)
        )
        
        # 最大工作线程数
        ttk.Label(group, text="最大工作线程:").grid(row=2, column=0, sticky=W, pady=2)
        self.config_vars['analysis_max_workers'] = tk.IntVar()
        ttk.Spinbox(
            group,
            from_=1,
            to=20,
            textvariable=self.config_vars['analysis_max_workers'],
            width=10
        ).grid(row=2, column=1, sticky=W, padx=(10, 0), pady=2)
        
        ttk.Label(group, text="(并发处理线程数)", font=("微软雅黑", 8), foreground="gray").grid(
            row=2, column=2, sticky=W, padx=(5, 0)
        )
        
        # 置信度阈值
        ttk.Label(group, text="置信度阈值:").grid(row=3, column=0, sticky=W, pady=2)
        self.config_vars['analysis_confidence_threshold'] = tk.DoubleVar()
        ttk.Scale(
            group,
            from_=0.5,
            to=0.95,
            variable=self.config_vars['analysis_confidence_threshold'],
            orient=HORIZONTAL,
            length=200
        ).grid(row=3, column=1, sticky=W, padx=(10, 0), pady=2)
        
        conf_label = ttk.Label(group, text="0.8")
        conf_label.grid(row=3, column=2, padx=(5, 0), pady=2)
        
        def update_conf_label(*args):
            conf_label.config(text=f"{self.config_vars['analysis_confidence_threshold'].get():.2f}")
        
        self.config_vars['analysis_confidence_threshold'].trace('w', update_conf_label)
        
        # 自动检测开关
        self.config_vars['analysis_auto_detect'] = tk.BooleanVar()
        ttk.Checkbutton(
            group,
            text="启用AI自动检测自我介绍",
            variable=self.config_vars['analysis_auto_detect']
        ).grid(row=4, column=0, columnspan=3, sticky=W, pady=5)
        
        group.columnconfigure(1, weight=1)
    
    def _create_ui_config(self, parent):
        """创建界面配置组"""
        group = ttk.LabelFrame(parent, text="🎨 界面配置", padding=10)
        group.pack(fill=X, padx=10, pady=5)
        
        # 主题选择
        ttk.Label(group, text="界面主题:").grid(row=0, column=0, sticky=W, pady=2)
        self.config_vars['ui_theme'] = tk.StringVar()
        theme_combo = ttk.Combobox(
            group,
            textvariable=self.config_vars['ui_theme'],
            values=["cosmo", "flatly", "journal", "litera", "lumen", "minty", "pulse", "sandstone", "united", "yeti"],
            width=15
        )
        theme_combo.grid(row=0, column=1, sticky=W, padx=(10, 0), pady=2)
        
        # 窗口大小
        ttk.Label(group, text="窗口宽度:").grid(row=1, column=0, sticky=W, pady=2)
        self.config_vars['ui_window_width'] = tk.IntVar()
        ttk.Spinbox(
            group,
            from_=800,
            to=1920,
            increment=50,
            textvariable=self.config_vars['ui_window_width'],
            width=10
        ).grid(row=1, column=1, sticky=W, padx=(10, 0), pady=2)
        
        ttk.Label(group, text="窗口高度:").grid(row=2, column=0, sticky=W, pady=2)
        self.config_vars['ui_window_height'] = tk.IntVar()
        ttk.Spinbox(
            group,
            from_=600,
            to=1080,
            increment=50,
            textvariable=self.config_vars['ui_window_height'],
            width=10
        ).grid(row=2, column=1, sticky=W, padx=(10, 0), pady=2)
        
        group.columnconfigure(1, weight=1)
    
    def _create_buttons(self, parent):
        """创建操作按钮"""
        btn_frame = ttk.Frame(parent)
        btn_frame.pack(fill=X, padx=10, pady=20)
        
        # 测试连接按钮
        ttk.Button(
            btn_frame,
            text="🔗 测试API连接",
            command=self._test_connections,
            bootstyle="info"
        ).pack(side=LEFT, padx=(0, 10))
        
        # 保存配置按钮
        ttk.Button(
            btn_frame,
            text="💾 保存配置",
            command=self._save_config,
            bootstyle="success"
        ).pack(side=LEFT, padx=(0, 10))
        
        # 重置配置按钮
        ttk.Button(
            btn_frame,
            text="🔄 重置配置",
            command=self._reset_config,
            bootstyle="warning"
        ).pack(side=LEFT, padx=(0, 10))
        
        # 应用配置按钮
        ttk.Button(
            btn_frame,
            text="✅ 应用配置",
            command=self._apply_config,
            bootstyle="primary"
        ).pack(side=RIGHT)
    
    def _load_config(self):
        """加载配置到界面"""
        try:
            config = get_config()
            
            # Chatlog配置
            self.config_vars['chatlog_base_url'].set(config.chatlog.base_url)
            self.config_vars['chatlog_timeout'].set(config.chatlog.timeout)
            self.config_vars['chatlog_max_retries'].set(config.chatlog.max_retries)
            
            # DeepSeek配置
            self.config_vars['deepseek_use_openai_like'].set(config.deepseek.use_openai_like)
            self.config_vars['deepseek_api_key'].set(config.deepseek.api_key)
            self.config_vars['deepseek_base_url'].set(config.deepseek.base_url)
            self.config_vars['deepseek_model'].set(config.deepseek.model)
            self.config_vars['deepseek_max_tokens'].set(config.deepseek.max_tokens)
            self.config_vars['deepseek_temperature'].set(config.deepseek.temperature)
            
            # 更新客户端状态显示
            self._update_client_status()
            
            # Feishu配置
            self.config_vars['feishu_app_id'].set(config.feishu.app_id)
            self.config_vars['feishu_app_secret'].set(config.feishu.app_secret)
            self.config_vars['feishu_app_token'] = tk.StringVar()
            if hasattr(config.feishu, 'app_token'):
                self.config_vars['feishu_app_token'].set(config.feishu.app_token)
            self.config_vars['feishu_base_url'].set(config.feishu.base_url)
            
            # 分析配置
            self.config_vars['analysis_batch_size'].set(config.analysis.batch_size)
            self.config_vars['analysis_max_workers'].set(config.analysis.max_workers)
            self.config_vars['analysis_confidence_threshold'].set(config.analysis.confidence_threshold)
            self.config_vars['analysis_auto_detect'].set(config.analysis.auto_detect)
            
            # UI配置
            self.config_vars['ui_theme'].set(config.ui.theme)
            self.config_vars['ui_window_width'].set(config.ui.window_width)
            self.config_vars['ui_window_height'].set(config.ui.window_height)
            
            # 更新配置状态
            self._update_config_status()
            
            logger.debug("配置加载完成")
            
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            messagebox.showerror("错误", f"加载配置失败: {str(e)}")
    
    def _save_config(self):
        """保存配置到文件"""
        try:
            config = get_config()
            
            # Chatlog配置
            config.chatlog.base_url = self.config_vars['chatlog_base_url'].get()
            config.chatlog.timeout = self.config_vars['chatlog_timeout'].get()
            config.chatlog.max_retries = self.config_vars['chatlog_max_retries'].get()
            
            # DeepSeek配置
            config.deepseek.use_openai_like = self.config_vars['deepseek_use_openai_like'].get()
            config.deepseek.api_key = self.config_vars['deepseek_api_key'].get()
            config.deepseek.base_url = self.config_vars['deepseek_base_url'].get()
            config.deepseek.model = self.config_vars['deepseek_model'].get()
            config.deepseek.max_tokens = self.config_vars['deepseek_max_tokens'].get()
            config.deepseek.temperature = self.config_vars['deepseek_temperature'].get()
            
            # Feishu配置
            config.feishu.app_id = self.config_vars['feishu_app_id'].get()
            config.feishu.app_secret = self.config_vars['feishu_app_secret'].get()
            if hasattr(config.feishu, 'app_token'):
                config.feishu.app_token = self.config_vars['feishu_app_token'].get()
            else:
                # 为旧配置添加app_token字段
                setattr(config.feishu, 'app_token', self.config_vars['feishu_app_token'].get())
            config.feishu.base_url = self.config_vars['feishu_base_url'].get()
            
            # 分析配置
            config.analysis.batch_size = self.config_vars['analysis_batch_size'].get()
            config.analysis.max_workers = self.config_vars['analysis_max_workers'].get()
            config.analysis.confidence_threshold = self.config_vars['analysis_confidence_threshold'].get()
            config.analysis.auto_detect = self.config_vars['analysis_auto_detect'].get()
            
            # UI配置
            config.ui.theme = self.config_vars['ui_theme'].get()
            config.ui.window_width = self.config_vars['ui_window_width'].get()
            config.ui.window_height = self.config_vars['ui_window_height'].get()
            
            # 保存配置
            if save_config(config):
                messagebox.showinfo("成功", "配置保存成功！")
                logger.info("配置保存成功")
                
                # 更新配置状态
                self._update_config_status()
                return True
            else:
                messagebox.showerror("错误", "配置保存失败")
                return False
                
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")
            return False
    
    def _reset_config(self):
        """重置配置为默认值"""
        try:
            result = messagebox.askyesno("确认重置", "确定要重置所有配置为默认值吗？")
            if result:
                # 重新加载默认配置
                self._load_config()
                messagebox.showinfo("成功", "配置已重置为默认值")
                logger.info("配置重置完成")
        except Exception as e:
            logger.error(f"重置配置失败: {e}")
            messagebox.showerror("错误", f"重置配置失败:\n{str(e)}")
    
    def _apply_config(self):
        """应用配置（无需重启）"""
        try:
            # 先保存配置
            if self._save_config():
                # 更新分析服务的配置
                self.analysis_service.config = get_config()
                messagebox.showinfo("成功", "配置已应用")
                logger.info("配置应用成功")
            
        except Exception as e:
            logger.error(f"应用配置失败: {e}")
            messagebox.showerror("错误", f"应用配置失败:\n{str(e)}")
    
    def _test_connections(self):
        """测试API连接"""
        try:
            # 先应用当前配置
            self._apply_config()
            
            # 显示测试提示
            progress_window = tk.Toplevel(self.frame)
            progress_window.title("测试连接")
            progress_window.geometry("300x150")
            progress_window.transient(self.frame)
            progress_window.grab_set()
            
            ttk.Label(progress_window, text="正在测试API连接...").pack(pady=20)
            progress_bar = ttk.Progressbar(progress_window, mode='indeterminate')
            progress_bar.pack(pady=10, padx=20, fill=X)
            progress_bar.start()
            
            # 异步测试连接
            def test_async():
                try:
                    # 这里需要在主窗口的异步循环中运行
                    if hasattr(self.analysis_service, 'test_api_connections'):
                        # 由于是同步调用，这里简化处理
                        progress_window.destroy()
                        messagebox.showinfo("测试完成", "请查看状态栏中的连接状态")
                except Exception as e:
                    progress_window.destroy()
                    messagebox.showerror("测试失败", f"连接测试失败:\n{str(e)}")
            
            # 延迟执行测试
            progress_window.after(1000, test_async)
            
        except Exception as e:
            logger.error(f"测试连接失败: {e}")
            messagebox.showerror("错误", f"测试连接失败:\n{str(e)}")

    def _update_config_status(self):
        """更新配置状态显示"""
        try:
            # 检查DeepSeek配置
            api_key = self.config_vars.get('deepseek_api_key', tk.StringVar()).get().strip()
            if api_key:
                self.status_vars['deepseek'].set("✅ 已配置")
            else:
                self.status_vars['deepseek'].set("❌ 未配置")
            
            # 检查Chatlog配置
            base_url = self.config_vars.get('chatlog_base_url', tk.StringVar()).get().strip()
            if base_url:
                self.status_vars['chatlog'].set("✅ 已配置")
            else:
                self.status_vars['chatlog'].set("❌ 未配置")
            
            # 检查Feishu配置
            app_id = self.config_vars.get('feishu_app_id', tk.StringVar()).get().strip()
            app_secret = self.config_vars.get('feishu_app_secret', tk.StringVar()).get().strip()
            if app_id and app_secret:
                self.status_vars['feishu'].set("✅ 已配置")
            else:
                self.status_vars['feishu'].set("⚪ 未配置")
                
        except Exception as e:
            logger.warning(f"更新配置状态失败: {e}")

    def _start_config_wizard(self):
        """启动配置向导"""
        wizard_window = tk.Toplevel(self.parent)
        wizard_window.title("配置向导")
        wizard_window.geometry("700x500")
        wizard_window.transient(self.parent)
        wizard_window.grab_set()
        
        # 创建向导内容
        main_frame = ttk.Frame(wizard_window, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(
            main_frame, 
            text="🚀 一键配置向导",
            font=("微软雅黑", 16, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # 向导步骤
        steps_frame = ttk.Frame(main_frame)
        steps_frame.pack(fill=BOTH, expand=True)
        
        wizard_content = """
📋 配置向导将帮助您快速完成以下配置：

🔧 第一步：配置AI分析服务
• 获取DeepSeek API密钥
• 测试AI服务连接

📡 第二步：配置聊天记录服务  
• 设置Chatlog API地址
• 验证数据访问权限

📋 第三步：配置飞书同步（可选）
• 创建飞书应用
• 获取应用凭证
• 配置多维表格

✅ 第四步：完成配置
• 测试所有连接
• 开始使用分析功能

是否现在开始配置向导？
        """
        
        content_text = tk.Text(
            steps_frame,
            wrap=tk.WORD,
            font=("微软雅黑", 11),
            height=15,
            state="disabled"
        )
        content_text.pack(fill=BOTH, expand=True, pady=(0, 20))
        
        content_text.config(state="normal")
        content_text.insert(tk.END, wizard_content)
        content_text.config(state="disabled")
        
        # 按钮
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=X)
        
        ttk.Button(
            btn_frame,
            text="开始配置",
            command=lambda: self._start_step1_wizard(wizard_window),
            bootstyle="success"
        ).pack(side=LEFT)
        
        ttk.Button(
            btn_frame,
            text="关闭",
            command=wizard_window.destroy
        ).pack(side=RIGHT)
    
    def _start_step1_wizard(self, parent_window):
        """第一步：配置AI服务"""
        parent_window.destroy()
        
        step1_window = tk.Toplevel(self.parent)
        step1_window.title("配置向导 - AI服务配置")
        step1_window.geometry("600x400")
        step1_window.transient(self.parent)
        step1_window.grab_set()
        
        main_frame = ttk.Frame(step1_window, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        
        # 标题
        ttk.Label(
            main_frame, 
            text="🤖 第一步：配置AI分析服务",
            font=("微软雅黑", 14, "bold")
        ).pack(pady=(0, 20))
        
        # 内容
        content = """
请选择AI服务类型并获取API密钥：

🔹 推荐选择：DeepSeek官方服务
• 性能稳定，价格优惠
• 支持大量并发请求
• 官方服务保障

🔹 备选方案：OpenAI兼容服务
• 支持其他AI服务商
• 可使用代理服务
• 兼容性更好

📝 获取API密钥步骤：
1. 访问 https://platform.deepseek.com
2. 注册账号并登录
3. 进入API管理页面
4. 创建新的API密钥
5. 复制密钥到下方输入框

💰 费用说明：
DeepSeek API按使用量计费，分析1000条消息约需0.1-0.5元
        """
        
        content_text = tk.Text(
            main_frame,
            wrap=tk.WORD,
            font=("微软雅黑", 10),
            height=12,
            state="disabled"
        )
        content_text.pack(fill=BOTH, expand=True, pady=(0, 20))
        
        content_text.config(state="normal")
        content_text.insert(tk.END, content)
        content_text.config(state="disabled")
        
        # API密钥输入
        input_frame = ttk.LabelFrame(main_frame, text="API密钥配置", padding=10)
        input_frame.pack(fill=X, pady=(0, 20))
        
        ttk.Label(input_frame, text="API密钥:").grid(row=0, column=0, sticky=W, pady=5)
        api_key_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=api_key_var, width=50, show="*").grid(
            row=0, column=1, padx=(10, 0), pady=5, sticky=W+E
        )
        
        input_frame.columnconfigure(1, weight=1)
        
        # 按钮
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=X)
        
        def save_and_next():
            if api_key_var.get().strip():
                self.config_vars['deepseek_api_key'].set(api_key_var.get().strip())
                step1_window.destroy()
                self._start_step2_wizard()
            else:
                messagebox.showwarning("提示", "请输入API密钥")
        
        ttk.Button(
            btn_frame,
            text="下一步",
            command=save_and_next,
            bootstyle="success"
        ).pack(side=RIGHT)
        
        ttk.Button(
            btn_frame,
            text="跳过",
            command=lambda: (step1_window.destroy(), self._start_step2_wizard())
        ).pack(side=RIGHT, padx=(0, 10))
    
    def _start_step2_wizard(self):
        """第二步：配置聊天记录服务"""
        step2_window = tk.Toplevel(self.parent)
        step2_window.title("配置向导 - 聊天记录服务配置")
        step2_window.geometry("600x400")
        step2_window.transient(self.parent)
        step2_window.grab_set()
        
        main_frame = ttk.Frame(step2_window, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        
        # 标题
        ttk.Label(
            main_frame, 
            text="📡 第二步：配置聊天记录服务",
            font=("微软雅黑", 14, "bold")
        ).pack(pady=(0, 20))
        
        # 内容
        content = """
聊天记录服务用于获取微信群组消息数据：

🔧 设置步骤：
1. 确保您已经安装并运行了微信聊天记录导出工具
2. 默认服务地址为：http://127.0.0.1:5030
3. 如果使用其他地址，请修改下方配置

⚠️ 重要提示：
• 聊天记录服务必须先启动，否则无法获取群组数据
• 确保防火墙允许该端口访问
• 如果使用远程服务器，请修改IP地址

🔍 测试方法：
配置完成后可以点击"测试连接"验证服务是否正常
        """
        
        content_text = tk.Text(
            main_frame,
            wrap=tk.WORD,
            font=("微软雅黑", 10),
            height=10,
            state="disabled"
        )
        content_text.pack(fill=BOTH, expand=True, pady=(0, 20))
        
        content_text.config(state="normal")
        content_text.insert(tk.END, content)
        content_text.config(state="disabled")
        
        # 地址输入
        input_frame = ttk.LabelFrame(main_frame, text="服务地址配置", padding=10)
        input_frame.pack(fill=X, pady=(0, 20))
        
        ttk.Label(input_frame, text="服务地址:").grid(row=0, column=0, sticky=W, pady=5)
        url_var = tk.StringVar(value="http://127.0.0.1:5030")
        ttk.Entry(input_frame, textvariable=url_var, width=50).grid(
            row=0, column=1, padx=(10, 0), pady=5, sticky=W+E
        )
        
        input_frame.columnconfigure(1, weight=1)
        
        # 按钮
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=X)
        
        def save_and_next():
            self.config_vars['chatlog_base_url'].set(url_var.get().strip())
            step2_window.destroy()
            self._start_step3_wizard()
        
        ttk.Button(
            btn_frame,
            text="下一步",
            command=save_and_next,
            bootstyle="success"
        ).pack(side=RIGHT)
        
        ttk.Button(
            btn_frame,
            text="跳过",
            command=lambda: (step2_window.destroy(), self._start_step3_wizard())
        ).pack(side=RIGHT, padx=(0, 10))
    
    def _start_step3_wizard(self):
        """第三步：配置飞书同步（可选）"""
        step3_window = tk.Toplevel(self.parent)
        step3_window.title("配置向导 - 飞书同步配置")
        step3_window.geometry("600x500")
        step3_window.transient(self.parent)
        step3_window.grab_set()
        
        main_frame = ttk.Frame(step3_window, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        
        # 标题
        ttk.Label(
            main_frame, 
            text="📋 第三步：配置飞书同步（可选）",
            font=("微软雅黑", 14, "bold")
        ).pack(pady=(0, 20))
        
        # 选择框
        sync_var = tk.BooleanVar()
        ttk.Checkbutton(
            main_frame,
            text="启用飞书多维表格同步功能",
            variable=sync_var,
            command=lambda: self._toggle_feishu_config(config_frame, sync_var.get())
        ).pack(pady=(0, 10))
        
        # 配置区域
        config_frame = ttk.LabelFrame(main_frame, text="飞书配置", padding=10)
        config_frame.pack(fill=X, pady=(0, 20))
        config_frame.pack_forget()  # 初始隐藏
        
        # 应用ID
        ttk.Label(config_frame, text="应用ID:").grid(row=0, column=0, sticky=W, pady=2)
        app_id_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=app_id_var, width=40).grid(
            row=0, column=1, padx=(10, 0), pady=2, sticky=W+E
        )
        
        # 应用密钥
        ttk.Label(config_frame, text="应用密钥:").grid(row=1, column=0, sticky=W, pady=2)
        app_secret_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=app_secret_var, width=40, show="*").grid(
            row=1, column=1, padx=(10, 0), pady=2, sticky=W+E
        )
        
        # 表格Token
        ttk.Label(config_frame, text="表格Token:").grid(row=2, column=0, sticky=W, pady=2)
        app_token_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=app_token_var, width=40).grid(
            row=2, column=1, padx=(10, 0), pady=2, sticky=W+E
        )
        
        config_frame.columnconfigure(1, weight=1)
        
        # 帮助信息
        help_text = """
📝 飞书配置说明：
1. 访问 https://open.feishu.cn/ 创建企业自建应用
2. 获取应用ID和应用密钥
3. 配置权限：bitable:app、bitable:app:readonly、bitable:app:readwrite
4. 创建多维表格，从浏览器地址栏获取表格Token

💡 表格Token获取：
打开多维表格，地址栏 /base/后面的字符串就是Token
        """
        
        help_label = ttk.Label(
            main_frame,
            text=help_text,
            font=("微软雅黑", 9),
            foreground="gray",
            justify=LEFT
        )
        help_label.pack(fill=X, pady=(0, 20))
        
        # 按钮
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=X)
        
        def save_and_finish():
            if sync_var.get():
                self.config_vars['feishu_app_id'].set(app_id_var.get().strip())
                self.config_vars['feishu_app_secret'].set(app_secret_var.get().strip())
                self.config_vars['feishu_app_token'].set(app_token_var.get().strip())
            
            step3_window.destroy()
            self._finish_wizard()
        
        ttk.Button(
            btn_frame,
            text="完成配置",
            command=save_and_finish,
            bootstyle="success"
        ).pack(side=RIGHT)
        
        ttk.Button(
            btn_frame,
            text="查看详细教程",
            command=self._show_feishu_guide
        ).pack(side=RIGHT, padx=(0, 10))
    
    def _toggle_feishu_config(self, config_frame, enabled):
        """切换飞书配置显示"""
        if enabled:
            config_frame.pack(fill=X, pady=(0, 20))
        else:
            config_frame.pack_forget()
    
    def _finish_wizard(self):
        """完成配置向导"""
        # 保存配置
        if self._save_config():
            messagebox.showinfo(
                "配置完成", 
                "🎉 配置向导已完成！\n\n"
                "您现在可以：\n"
                "1. 在'分析任务'页面开始分析群组消息\n"
                "2. 在'分析结果'页面查看用户画像\n"
                "3. 手动同步结果到飞书（如已配置）\n\n"
                "建议先点击'测试API连接'确认所有服务正常。"
            )
    
    def _show_tutorial(self):
        """显示使用教程"""
        tutorial_window = tk.Toplevel(self.parent)
        tutorial_window.title("使用教程")
        tutorial_window.geometry("800x600")
        tutorial_window.transient(self.parent)
        tutorial_window.grab_set()
        
        main_frame = ttk.Frame(tutorial_window, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        
        # 标题
        ttk.Label(
            main_frame, 
            text="📖 微信群自我介绍分析工具使用教程",
            font=("微软雅黑", 16, "bold")
        ).pack(pady=(0, 20))
        
        # 创建滚动文本框
        text_frame = ttk.Frame(main_frame)
        text_frame.pack(fill=BOTH, expand=True, pady=(0, 20))
        
        tutorial_text = tk.Text(
            text_frame,
            wrap=tk.WORD,
            font=("微软雅黑", 10)
        )
        
        scrollbar = ttk.Scrollbar(text_frame, command=tutorial_text.yview)
        tutorial_text.configure(yscrollcommand=scrollbar.set)
        
        tutorial_text.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # 教程内容
        content = """📋 使用教程

🚀 快速开始

1. 首次使用配置
   • 点击"一键配置向导"按照步骤配置
   • 或手动填写各项配置参数
   • 点击"测试API连接"确认配置正确

2. 开始分析
   • 进入"分析任务"标签页
   • 选择要分析的微信群组
   • 设置分析时间范围
   • 点击"开始分析"

3. 查看结果
   • 进入"分析结果"标签页
   • 查看用户画像列表
   • 使用筛选和搜索功能
   • 导出Excel或JSON格式数据

4. 飞书同步（可选）
   • 配置飞书多维表格参数
   • 在结果页面点击"同步到飞书"
   • 监控同步进度和状态

🔧 详细配置说明

AI分析服务配置：
• DeepSeek官方服务：稳定性好，推荐使用
• OpenAI兼容服务：支持其他AI提供商
• API密钥获取：访问 https://platform.deepseek.com

聊天记录服务配置：
• 默认地址：http://127.0.0.1:5030
• 需要先启动微信聊天记录导出工具
• 确保网络连接和端口开放

飞书同步配置：
• 创建飞书企业自建应用
• 配置必要权限：bitable相关权限
• 获取应用ID、密钥和表格Token

🛠️ 常见问题解决

问题1：无法连接AI服务
解决：检查API密钥是否正确，网络是否正常

问题2：找不到群组数据
解决：确认聊天记录服务正常运行，地址配置正确

问题3：飞书同步失败
解决：检查应用权限，确认表格Token正确

问题4：分析结果质量不高
解决：调整置信度阈值，检查消息内容质量

💡 使用技巧

• 建议先用小范围时间测试分析效果
• 定期清理分析日志，避免占用过多空间
• 使用筛选功能快速找到目标用户画像
• 导出数据前先预览结果确认质量
• 飞书同步建议手动触发，便于控制时机

📞 技术支持

如果遇到问题：
1. 查看应用日志了解详细错误信息
2. 检查网络连接和服务状态
3. 确认所有配置参数正确
4. 重启应用重新测试

更多技术文档请查看项目根目录docs文件夹。
        """
        
        tutorial_text.insert(tk.END, content)
        tutorial_text.config(state="disabled")
        
        # 关闭按钮
        ttk.Button(
            main_frame,
            text="关闭",
            command=tutorial_window.destroy
        ).pack()
    
    def _test_all_connections(self):
        """测试所有连接"""
        self._test_connections()
    
    def _show_chatlog_tutorial(self):
        """显示Chatlog服务教程"""
        messagebox.showinfo(
            "Chatlog服务配置", 
            "📡 聊天记录服务配置说明：\n\n"
            "1. 确保已安装微信聊天记录导出工具\n"
            "2. 启动服务，默认端口5030\n"
            "3. 服务地址格式：http://IP:端口\n"
            "4. 本地服务：http://127.0.0.1:5030\n"
            "5. 远程服务：http://服务器IP:5030\n\n"
            "💡 测试方法：配置完成后点击'测试连接'"
        )
    
    def _show_deepseek_tutorial(self):
        """显示DeepSeek API获取教程"""
        tutorial_window = tk.Toplevel(self.parent)
        tutorial_window.title("DeepSeek API密钥获取教程")
        tutorial_window.geometry("600x500")
        tutorial_window.transient(self.parent)
        tutorial_window.grab_set()
        
        main_frame = ttk.Frame(tutorial_window, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        
        # 标题
        ttk.Label(
            main_frame, 
            text="🤖 DeepSeek API密钥获取教程",
            font=("微软雅黑", 14, "bold")
        ).pack(pady=(0, 20))
        
        # 步骤内容
        content = """
📝 获取步骤：

1️⃣ 访问DeepSeek平台
   • 打开网址：https://platform.deepseek.com
   • 如果没有账号，点击注册

2️⃣ 注册/登录账号
   • 可以使用邮箱或手机号注册
   • 完成邮箱/手机验证

3️⃣ 进入控制台
   • 登录后进入用户控制台
   • 点击左侧菜单"API管理"

4️⃣ 创建API密钥
   • 点击"创建API密钥"按钮
   • 输入密钥名称（如：微信群分析）
   • 点击"创建"

5️⃣ 复制密钥
   • 创建成功后会显示API密钥
   • ⚠️ 注意：密钥只显示一次，请立即复制保存
   • 将密钥粘贴到配置页面的"API密钥"字段

💰 费用说明：
   • DeepSeek API按使用量计费
   • 分析1000条消息大约需要0.1-0.5元
   • 新用户通常有免费额度
   • 可在控制台查看用量和费用

🔧 其他选项：
   如果不想使用DeepSeek，也可以选择"OpenAI兼容服务"
   使用其他AI服务商的API密钥
        """
        
        content_text = tk.Text(
            main_frame,
            wrap=tk.WORD,
            font=("微软雅黑", 10),
            height=20,
            state="disabled"
        )
        content_text.pack(fill=BOTH, expand=True, pady=(0, 20))
        
        content_text.config(state="normal")
        content_text.insert(tk.END, content)
        content_text.config(state="disabled")
        
        # 按钮
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=X)
        
        def open_deepseek():
            import webbrowser
            webbrowser.open("https://platform.deepseek.com")
        
        ttk.Button(
            btn_frame,
            text="打开DeepSeek官网",
            command=open_deepseek,
            bootstyle="info"
        ).pack(side=LEFT)
        
        ttk.Button(
            btn_frame,
            text="关闭",
            command=tutorial_window.destroy
        ).pack(side=RIGHT)
    
    def _show_app_token_help(self):
        """显示App Token获取帮助"""
        help_window = tk.Toplevel(self.parent)
        help_window.title("飞书表格Token获取方法")
        help_window.geometry("500x400")
        help_window.transient(self.parent)
        help_window.grab_set()
        
        main_frame = ttk.Frame(help_window, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        
        # 标题
        ttk.Label(
            main_frame, 
            text="📋 表格Token (App Token) 获取方法",
            font=("微软雅黑", 14, "bold")
        ).pack(pady=(0, 20))
        
        # 内容
        content = """
📝 获取步骤：

1️⃣ 创建或打开多维表格
   • 在飞书中创建新的多维表格
   • 或打开现有的多维表格

2️⃣ 查看浏览器地址栏
   地址格式：https://xxx.feishu.cn/base/表格Token?table=xxx
   
3️⃣ 复制Token
   • 复制 /base/ 后面，? 前面的字符串
   • 例如：bascnxxxxxxxxxxxxxxxxxxxxxx

📋 示例：
   完整地址：
   https://example.feishu.cn/base/bascn123456789abcdef?table=tbl123
   
   表格Token：
   bascn123456789abcdef

⚠️ 注意事项：
   • 确保对该表格有编辑权限
   • Token通常以 basc 开头
   • 长度约28个字符
   
💡 如果看不到完整地址：
   • 右键表格标题 → 复制链接
   • 使用分享功能获取链接
        """
        
        content_text = tk.Text(
            main_frame,
            wrap=tk.WORD,
            font=("微软雅黑", 10),
            height=15,
            state="disabled"
        )
        content_text.pack(fill=BOTH, expand=True, pady=(0, 20))
        
        content_text.config(state="normal")
        content_text.insert(tk.END, content)
        content_text.config(state="disabled")
        
        # 关闭按钮
        ttk.Button(
            main_frame,
            text="关闭",
            command=help_window.destroy
        ).pack()

    def _update_client_status(self):
        """更新客户端状态显示"""
        try:
            # 获取当前选择的客户端类型
            use_openai_like = self.config_vars['deepseek_use_openai_like'].get()
            
            if use_openai_like:
                self.client_status_label.config(text="当前: OpenAI兼容服务", bootstyle="warning")
            else:
                self.client_status_label.config(text="当前: DeepSeek官方服务", bootstyle="info")
                
            # 获取实际运行中的客户端信息
            try:
                client_info = self.analysis_service.get_deepseek_client_info()
                if client_info['use_openai_like'] != use_openai_like:
                    if use_openai_like:
                        self.client_status_label.config(text="选择: OpenAI兼容 (需应用)", bootstyle="danger")
                    else:
                        self.client_status_label.config(text="选择: DeepSeek官方 (需应用)", bootstyle="danger")
            except:
                pass
                    
        except Exception as e:
            logger.warning(f"更新客户端状态失败: {e}")
            self.client_status_label.config(text="状态未知")

    def _on_client_type_changed(self):
        """客户端类型切换时的回调"""
        self._update_client_status()

    def _switch_client_immediately(self):
        """立即应用客户端切换"""
        try:
            use_openai_like = self.config_vars['deepseek_use_openai_like'].get()
            
            # 检查是否正在分析
            if self.analysis_service.get_app_state().is_analyzing:
                messagebox.showwarning(
                    "无法切换", 
                    "正在进行分析任务，无法切换客户端类型。\n请等待当前任务完成后再切换。"
                )
                return
            
            # 执行切换
            success = self.analysis_service.switch_deepseek_client(use_openai_like)
            
            if success:
                # 更新配置文件
                config = get_config()
                config.deepseek.use_openai_like = use_openai_like
                save_config(config)
                
                client_type = "OpenAI兼容服务" if use_openai_like else "DeepSeek官方服务"
                messagebox.showinfo(
                    "切换成功", 
                    f"AI客户端已成功切换到: {client_type}\n"
                    "新的客户端类型将在下次AI分析时生效。"
                )
                
                # 更新状态显示
                self._update_client_status()
                
                logger.info(f"DeepSeek客户端切换成功: {client_type}")
                
            else:
                messagebox.showerror(
                    "切换失败", 
                    "AI客户端切换失败，请检查日志了解详细信息。"
                )
                
        except Exception as e:
            logger.error(f"客户端切换失败: {e}")
            messagebox.showerror("切换失败", f"客户端切换过程中发生错误:\n{str(e)}")

    def _show_client_info(self):
        """显示客户端详细信息"""
        try:
            client_info = self.analysis_service.get_deepseek_client_info()
            
            info_text = f"""
AI客户端信息:

客户端类型: {client_info['type']}
使用OpenAI兼容: {'是' if client_info['use_openai_like'] else '否'}
模型名称: {client_info['model']}
API地址: {client_info['base_url']}
状态: {'可用' if client_info['is_available'] else '不可用'}

配置说明:
• DeepSeek官方服务: 使用DeepSeek原生SDK，功能完整，稳定性高
• OpenAI兼容服务: 使用OpenAI兼容接口，兼容性更好，适用于代理场景

注意: 切换客户端类型需要重新初始化连接，建议在分析任务开始前设置。
            """.strip()
            
            messagebox.showinfo("客户端信息", info_text)
            
        except Exception as e:
            logger.error(f"获取客户端信息失败: {e}")
            messagebox.showerror("错误", f"无法获取客户端信息: {str(e)}")

    def _show_feishu_guide(self):
        """显示飞书配置指南"""
        try:
            # 创建配置指南窗口
            guide_window = tk.Toplevel(self.parent)
            guide_window.title("飞书配置指南")
            guide_window.geometry("800x600")
            guide_window.transient(self.parent)
            guide_window.grab_set()
            
            # 创建窗口内容
            main_frame = ttk.Frame(guide_window, padding=20)
            main_frame.pack(fill=BOTH, expand=True)
            
            # 标题
            title_label = ttk.Label(
                main_frame, 
                text="🚀 飞书多维表格配置指南",
                font=("微软雅黑", 16, "bold")
            )
            title_label.pack(pady=(0, 20))
            
            # 创建滚动文本框显示详细内容
            text_frame = ttk.Frame(main_frame)
            text_frame.pack(fill=BOTH, expand=True, pady=(0, 20))
            
            guide_text = tk.Text(
                text_frame,
                wrap=tk.WORD,
                font=("微软雅黑", 10),
                state="disabled"
            )
            
            scrollbar = ttk.Scrollbar(text_frame, command=guide_text.yview)
            guide_text.configure(yscrollcommand=scrollbar.set)
            
            guide_text.pack(side=LEFT, fill=BOTH, expand=True)
            scrollbar.pack(side=RIGHT, fill=Y)
            
            # 详细配置内容
            content = """📋 飞书多维表格同步配置指南

🚀 功能介绍
本系统支持将分析得到的用户画像自动同步到飞书多维表格，方便团队协作和数据管理。

📋 配置步骤

1. 创建飞书应用
   • 打开飞书开放平台: https://open.feishu.cn/
   • 登录您的飞书账号
   • 点击"创建应用" -> "企业自建应用"
   • 填写应用信息：
     - 应用名称：微信群用户画像分析
     - 应用描述：自动分析微信群自我介绍并同步到飞书

2. 获取应用凭证
   创建应用后，在应用管理页面找到：
   • App ID：应用的唯一标识（填入上方"应用ID"字段）
   • App Secret：应用密钥（填入上方"应用密钥"字段）
   
   ⚠️ 注意：App Secret 请妥善保管，不要泄露给他人。

3. 配置应用权限
   在应用管理页面，进入"权限管理"，添加以下权限：
   • bitable:app - 多维表格应用权限
   • bitable:app:readonly - 读取多维表格
   • bitable:app:readwrite - 读写多维表格

4. 创建多维表格
   • 在飞书中创建一个新的多维表格
   • 记录表格的 App Token（在浏览器地址栏中）
   • 创建以下字段：
     - 昵称 (文本)
     - 职业 (文本) 
     - 个人介绍 (多行文本)
     - 能够提供 (多行文本)
     - 寻求帮助 (多行文本)
     - 行业领域 (文本)
     - 置信度 (数字)
     - 消息ID (文本)
     - 群组ID (文本)
     - 创建时间 (日期时间)

🔧 手动同步使用

1. 配置完成后，进行微信群分析
2. 在"分析结果"标签页点击"🚀 同步到飞书"按钮
3. 系统会显示详细的同步进度
4. 同步完成后，数据会出现在飞书多维表格中

🛠️ 故障排除

常见问题：
• "飞书配置不完整" - 检查App ID、App Secret是否正确填写
• "无法连接到飞书API" - 检查网络连接和应用权限
• "同步失败" - 确保应用有表格的读写权限

💡 最佳实践

• 推荐使用手动同步，便于控制
• 为不同项目创建不同的表格
• 定期备份重要数据
• 不要在代码中硬编码应用密钥

📞 技术支持

如果遇到问题：
1. 查看应用日志文件获取详细错误信息
2. 检查飞书开放平台的应用状态
3. 确认多维表格的权限设置

详细文档请查看: docs/feishu_setup.md"""
            
            # 将内容添加到文本框
            guide_text.config(state="normal")
            guide_text.insert(tk.END, content)
            guide_text.config(state="disabled")
            
            # 按钮区域
            btn_frame = ttk.Frame(main_frame)
            btn_frame.pack(fill=X)
            
            # 打开详细文档按钮
            open_doc_btn = ttk.Button(
                btn_frame,
                text="📖 打开详细文档",
                command=self._open_feishu_doc,
                bootstyle="info"
            )
            open_doc_btn.pack(side=LEFT)
            
            # 测试连接按钮
            test_btn = ttk.Button(
                btn_frame,
                text="🔌 测试飞书连接",
                command=self._test_feishu_connection,
                bootstyle="success"
            )
            test_btn.pack(side=LEFT, padx=(10, 0))
            
            # 关闭按钮
            close_btn = ttk.Button(
                btn_frame,
                text="关闭",
                command=guide_window.destroy
            )
            close_btn.pack(side=RIGHT)
            
        except Exception as e:
            logger.error(f"显示飞书配置指南失败: {e}")
            messagebox.showerror("错误", f"无法显示飞书配置指南: {str(e)}")
    
    def _open_feishu_doc(self):
        """打开飞书配置详细文档"""
        try:
            import os
            import webbrowser
            
            # 尝试打开本地文档文件
            doc_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "docs", "feishu_setup.md")
            
            if os.path.exists(doc_path):
                # 在默认文本编辑器中打开
                os.startfile(doc_path) if os.name == 'nt' else os.system(f'open "{doc_path}"')
            else:
                messagebox.showinfo("提示", f"详细文档位置: docs/feishu_setup.md\n请在项目根目录查看")
                
        except Exception as e:
            logger.error(f"打开飞书文档失败: {e}")
            messagebox.showerror("错误", f"无法打开飞书文档: {str(e)}")
    
    def _test_feishu_connection(self):
        """测试飞书连接"""
        try:
            # 获取当前配置的值
            app_id = self.config_vars.get('feishu_app_id', tk.StringVar()).get().strip()
            app_secret = self.config_vars.get('feishu_app_secret', tk.StringVar()).get().strip()
            base_url = self.config_vars.get('feishu_base_url', tk.StringVar()).get().strip()
            
            if not all([app_id, app_secret, base_url]):
                messagebox.showwarning("配置不完整", "请先填写完整的飞书配置信息")
                return
            
            # 临时保存配置进行测试
            original_config = get_config()
            test_config = original_config
            test_config.feishu.app_id = app_id
            test_config.feishu.app_secret = app_secret
            test_config.feishu.base_url = base_url
            
            # 测试连接
            from ..services.feishu_client import SyncFeishuClient
            feishu_client = SyncFeishuClient()
            
            if feishu_client.test_connection():
                messagebox.showinfo("连接成功", "✅ 飞书API连接测试成功！\n配置信息正确，可以正常使用飞书同步功能。")
            else:
                messagebox.showerror("连接失败", "❌ 飞书API连接测试失败！\n请检查：\n1. App ID和App Secret是否正确\n2. 网络连接是否正常\n3. 应用权限是否已审核通过")
                
        except Exception as e:
            logger.error(f"测试飞书连接失败: {e}")
            messagebox.showerror("测试失败", f"飞书连接测试失败:\n{str(e)}") 