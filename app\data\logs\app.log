2025-06-05 23:19:45,420 - __main__ - INFO - 启动微信群自我介绍分析工具
2025-06-05 23:19:48,023 - app.ui.analysis_tab - WARNING - 更新客户端信息显示失败: 'NoneType' object has no attribute 'get_deepseek_client_info'
2025-06-05 23:22:56,365 - __main__ - INFO - 启动微信群自我介绍分析工具
2025-06-05 23:23:20,530 - __main__ - INFO - 启动微信群自我介绍分析工具
2025-06-05 23:26:50,826 - __main__ - INFO - 启动微信群自我介绍分析工具
2025-06-05 23:26:53,079 - app.services.deepseek_client - INFO - 使用OpenAILike通用方式初始化DeepSeek客户端成功
2025-06-05 23:26:53,080 - app.services.deepseek_client - INFO - 结构化LLM初始化完成 (使用OpenAILike)
2025-06-05 23:26:53,080 - app.ui.main_window - INFO - 分析服务初始化完成
2025-06-05 23:26:53,367 - app.ui.results_tab - INFO - 结果查看标签页初始化完成
2025-06-05 23:26:53,586 - app.ui.main_window - INFO - 主窗口初始化完成
2025-06-05 23:26:53,602 - app.services.chatlog_client - INFO - 获取到 64 个群组
2025-06-05 23:26:53,729 - app.ui.groups_tab - INFO - 群组列表刷新完成，共 64 个群组
2025-06-05 23:27:55,881 - app.ui.config_tab - INFO - 配置保存成功
2025-06-05 23:28:35,127 - app.ui.results_tab - INFO - 未找到已完成的分析结果
2025-06-05 23:28:35,127 - app.ui.results_tab - INFO - 结果数据刷新完成
2025-06-05 23:28:36,578 - app.ui.results_tab - INFO - 未找到已完成的分析结果
2025-06-05 23:28:36,579 - app.ui.results_tab - INFO - 结果数据刷新完成
2025-06-05 23:28:47,816 - app.services.chatlog_client - INFO - 获取到 64 个群组
2025-06-05 23:28:54,787 - app.ui.analysis_tab - ERROR - 启动分析失败: 'AnalysisTab' object has no attribute 'groups'
2025-06-05 23:29:09,478 - app.ui.results_tab - INFO - 未找到已完成的分析结果
2025-06-05 23:29:09,479 - app.ui.results_tab - INFO - 结果数据刷新完成
2025-06-05 23:29:11,645 - app.services.chatlog_client - INFO - 获取到 64 个群组
2025-06-05 23:29:11,648 - app.ui.groups_tab - INFO - 群组列表刷新完成，共 64 个群组
2025-06-05 23:29:21,550 - app.services.chatlog_client - INFO - 获取到 64 个群组
2025-06-05 23:29:21,557 - app.ui.groups_tab - INFO - 群组列表刷新完成，共 64 个群组
2025-06-05 23:30:07,202 - app.ui.results_tab - INFO - 未找到已完成的分析结果
2025-06-05 23:30:07,202 - app.ui.results_tab - INFO - 结果数据刷新完成
2025-06-05 23:30:08,640 - app.services.chatlog_client - INFO - 获取到 64 个群组
2025-06-05 23:30:08,657 - app.ui.groups_tab - INFO - 群组列表刷新完成，共 64 个群组
2025-06-05 23:30:10,994 - app.ui.results_tab - INFO - 未找到已完成的分析结果
2025-06-05 23:30:10,994 - app.ui.results_tab - INFO - 结果数据刷新完成
2025-06-05 23:31:06,163 - app.services.chatlog_client - INFO - 获取到 64 个群组
2025-06-05 23:31:06,188 - app.ui.groups_tab - INFO - 群组列表刷新完成，共 64 个群组
2025-06-05 23:31:10,987 - app.ui.main_window - INFO - 应用资源清理完成
2025-06-05 23:31:10,987 - __main__ - INFO - 应用正常退出
2025-06-05 23:31:16,420 - __main__ - INFO - 启动微信群自我介绍分析工具
2025-06-05 23:31:18,688 - app.services.deepseek_client - INFO - 使用OpenAILike通用方式初始化DeepSeek客户端成功
2025-06-05 23:31:18,688 - app.services.deepseek_client - INFO - 结构化LLM初始化完成 (使用OpenAILike)
2025-06-05 23:31:18,688 - app.ui.main_window - INFO - 分析服务初始化完成
2025-06-05 23:31:19,021 - app.ui.results_tab - INFO - 结果查看标签页初始化完成
2025-06-05 23:31:19,236 - app.ui.main_window - INFO - 主窗口初始化完成
2025-06-05 23:31:19,250 - app.services.chatlog_client - INFO - 获取到 64 个群组
2025-06-05 23:31:19,375 - app.ui.groups_tab - INFO - 群组列表刷新完成，共 64 个群组
2025-06-05 23:31:51,448 - app.ui.results_tab - INFO - 未找到已完成的分析结果
2025-06-05 23:31:51,449 - app.ui.results_tab - INFO - 结果数据刷新完成
2025-06-05 23:31:55,367 - app.services.chatlog_client - INFO - 获取到 64 个群组
2025-06-05 23:32:06,599 - app.ui.analysis_tab - ERROR - 启动分析失败: 'ChatGroup' object has no attribute 'group_id'
2025-06-05 23:32:19,425 - app.ui.analysis_tab - ERROR - 启动分析失败: 'ChatGroup' object has no attribute 'group_id'
2025-06-05 23:33:26,787 - app.ui.main_window - INFO - 应用资源清理完成
2025-06-05 23:33:26,788 - __main__ - INFO - 应用正常退出
2025-06-05 23:33:28,733 - __main__ - INFO - 启动微信群自我介绍分析工具
2025-06-05 23:33:31,089 - app.services.deepseek_client - INFO - 使用OpenAILike通用方式初始化DeepSeek客户端成功
2025-06-05 23:33:31,090 - app.services.deepseek_client - INFO - 结构化LLM初始化完成 (使用OpenAILike)
2025-06-05 23:33:31,090 - app.ui.main_window - INFO - 分析服务初始化完成
2025-06-05 23:33:31,429 - app.ui.results_tab - INFO - 结果查看标签页初始化完成
2025-06-05 23:33:31,657 - app.ui.main_window - INFO - 主窗口初始化完成
2025-06-05 23:33:31,672 - app.services.chatlog_client - INFO - 获取到 64 个群组
2025-06-05 23:33:31,812 - app.ui.groups_tab - INFO - 群组列表刷新完成，共 64 个群组
2025-06-05 23:33:47,964 - app.services.chatlog_client - INFO - 获取到 64 个群组
2025-06-05 23:34:08,086 - app.services.analysis_service - INFO - 开始分析群组 58482986673@chatroom，任务ID: 69a91cd2-c89b-427f-9191-d1f49fa080a7
2025-06-05 23:34:08,156 - app.services.analysis_service - INFO - 正在获取群组 58482986673@chatroom 的消息
2025-06-05 23:34:08,157 - app.services.analysis_service - INFO - 时间范围: 2025-05-20 00:00:00 到 2025-05-20 23:59:59
2025-06-05 23:34:08,213 - app.services.chatlog_client - INFO - 获取到群组 58482986673@chatroom 的 743 条消息
2025-06-05 23:34:08,214 - app.services.analysis_service - INFO - 获取到 743 条消息
2025-06-05 23:34:08,214 - app.services.analysis_service - INFO - 消息统计:
2025-06-05 23:34:08,214 - app.services.analysis_service - INFO -   - 总数: 743
2025-06-05 23:34:08,214 - app.services.analysis_service - INFO -   - 类型分布: {'text': 635, 'image': 58, 'system': 50}
2025-06-05 23:34:08,215 - app.services.analysis_service - INFO -   - 时间范围: 2025-05-20 00:01:11+08:00 到 2025-05-20 23:55:07+08:00
2025-06-05 23:34:08,215 - app.services.analysis_service - INFO -   - 可用于检测的文本消息: 303 条
2025-06-05 23:34:08,224 - app.services.analysis_service - INFO - 开始批量AI检测 303 条消息中的自我介绍
2025-06-05 23:34:08,224 - app.services.analysis_service - INFO - 检测参数: 批次大小=100, 并发数=6
2025-06-05 23:34:08,224 - app.services.deepseek_client - INFO - 开始多轮递进式筛选 303 条消息，块大小: 50
2025-06-05 23:34:08,224 - app.services.deepseek_client - INFO - 第0轮：本地预筛选...
2025-06-05 23:34:08,227 - app.services.deepseek_client - INFO - 本地预筛选完成：48 条已处理，255 条需要AI处理
2025-06-05 23:34:08,227 - app.services.deepseek_client - INFO - 第1轮：AI粗筛选（条件宽松）...
2025-06-05 23:34:08,227 - app.services.deepseek_client - INFO - 粗筛选块 1: 50 条消息
2025-06-05 23:34:11,745 - app.services.deepseek_client - INFO - 粗筛选块 1 完成：发现 1 个可能的自我介绍
2025-06-05 23:34:12,759 - app.services.deepseek_client - INFO - 粗筛选块 2: 50 条消息
2025-06-05 23:34:14,422 - app.services.deepseek_client - INFO - 粗筛选块 2 完成：发现 2 个可能的自我介绍
2025-06-05 23:34:15,429 - app.services.deepseek_client - INFO - 粗筛选块 3: 50 条消息
2025-06-05 23:34:17,237 - app.services.deepseek_client - INFO - 粗筛选块 3 完成：发现 9 个可能的自我介绍
2025-06-05 23:34:18,239 - app.services.deepseek_client - INFO - 粗筛选块 4: 50 条消息
2025-06-05 23:34:19,915 - app.services.deepseek_client - INFO - 粗筛选块 4 完成：发现 6 个可能的自我介绍
2025-06-05 23:34:20,925 - app.services.deepseek_client - INFO - 粗筛选块 5: 50 条消息
2025-06-05 23:34:22,716 - app.services.deepseek_client - INFO - 粗筛选块 5 完成：发现 8 个可能的自我介绍
2025-06-05 23:34:23,724 - app.services.deepseek_client - INFO - 粗筛选块 6: 5 条消息
2025-06-05 23:34:25,509 - app.services.deepseek_client - INFO - 粗筛选块 6 完成：发现 1 个可能的自我介绍
2025-06-05 23:34:26,520 - app.services.deepseek_client - INFO - 粗筛选完成：从 255 条中筛选出 27 条可能的自我介绍，耗时 18.30秒
2025-06-05 23:34:26,520 - app.services.deepseek_client - INFO - 第2轮：AI精筛选（条件严格）...
2025-06-05 23:34:26,521 - app.services.deepseek_client - INFO - 精筛选块 1: 20 条消息
2025-06-05 23:34:32,163 - app.services.deepseek_client - INFO - 精筛选块 1 完成
2025-06-05 23:34:32,666 - app.services.deepseek_client - INFO - 精筛选块 2: 7 条消息
2025-06-05 23:34:35,576 - app.services.deepseek_client - INFO - 精筛选块 2 完成
2025-06-05 23:34:36,084 - app.services.deepseek_client - INFO - 多轮递进式筛选完成:
2025-06-05 23:34:36,085 - app.services.deepseek_client - INFO -   总消息: 303 条
2025-06-05 23:34:36,085 - app.services.deepseek_client - INFO -   发现自我介绍: 10 个
2025-06-05 23:34:36,085 - app.services.deepseek_client - INFO -   本地处理: 48 条
2025-06-05 23:34:36,085 - app.services.deepseek_client - INFO -   粗筛选: 255 → 27 条
2025-06-05 23:34:36,085 - app.services.deepseek_client - INFO -   精筛选: 27 → 5 条
2025-06-05 23:34:36,085 - app.services.deepseek_client - INFO -   API请求次数: 8 次（vs 原来的 303 次）
2025-06-05 23:34:36,086 - app.services.deepseek_client - INFO -   请求减少: 97.4%
2025-06-05 23:34:36,086 - app.services.deepseek_client - INFO -   总耗时: 27.86秒
2025-06-05 23:34:36,086 - app.services.analysis_service - INFO - 发现自我介绍 [78/303]: 下夕烟 (置信度: 0.80)
2025-06-05 23:34:36,086 - app.services.analysis_service - INFO - 发现自我介绍 [84/303]: 沙龙小王子rain雨 (置信度: 0.95)
2025-06-05 23:34:36,086 - app.services.analysis_service - INFO - 发现自我介绍 [86/303]: 刘怡功『功导』 (置信度: 0.80)
2025-06-05 23:34:36,094 - app.services.analysis_service - INFO - 发现自我介绍 [167/303]: Tim (置信度: 0.85)
2025-06-05 23:34:36,095 - app.services.analysis_service - INFO - 发现自我介绍 [183/303]: 风缘+吴春锋 (置信度: 0.85)
2025-06-05 23:34:36,095 - app.services.analysis_service - INFO - 发现自我介绍 [190/303]: 佛山-家炜-合同审核自动化 (置信度: 0.85)
2025-06-05 23:34:36,097 - app.services.analysis_service - INFO - 发现自我介绍 [205/303]: 文斌 (置信度: 0.85)
2025-06-05 23:34:36,097 - app.services.analysis_service - INFO - 发现自我介绍 [240/303]: 佛山 小冬 (置信度: 0.85)
2025-06-05 23:34:36,097 - app.services.analysis_service - INFO - 发现自我介绍 [241/303]: 苏白 (置信度: 0.85)
2025-06-05 23:34:36,097 - app.services.analysis_service - INFO - 发现自我介绍 [243/303]: 刀哥 (置信度: 0.85)
2025-06-05 23:34:36,103 - app.services.analysis_service - INFO - 批量AI检测完成，在 303 条消息中发现 10 个自我介绍
2025-06-05 23:34:36,113 - app.services.analysis_service - INFO - 开始批量从 10 个自我介绍中提取用户画像
2025-06-05 23:34:36,113 - app.services.analysis_service - INFO - 提取参数: 批次大小=5, 并发数=1
2025-06-05 23:34:36,113 - app.services.deepseek_client - INFO - 开始稳定提取 10 个用户画像（避免批量结构化输出问题）
2025-06-05 23:34:38,311 - app.services.deepseek_client - INFO - AI分析记录 [ID: 58b5e257]
2025-06-05 23:34:38,311 - app.services.deepseek_client - INFO -   类型: extraction
2025-06-05 23:34:38,311 - app.services.deepseek_client - INFO -   成功: True
2025-06-05 23:34:38,311 - app.services.deepseek_client - INFO -   处理时间: 2197ms
2025-06-05 23:34:38,311 - app.services.deepseek_client - INFO -   结构化结果: {'industry': 'AI', 'personal_intro': '作者对AI行业中存在的“倒卖内测码”现象表达了不满，认为这种行为并非真正对AI感兴趣，而只是为了赚钱，且不属于AI产品开发或行业分析服务。', 'location': None, 'can_provide': [], 'need_help': [], 'tags': ['AI', '批判性思维', '行业观察']}
2025-06-05 23:34:38,312 - app.services.deepseek_client - INFO - 用户画像提取完成 [ID: 58b5e257]: AI
2025-06-05 23:34:42,070 - app.services.deepseek_client - INFO - AI分析记录 [ID: 02dae5b6]
2025-06-05 23:34:42,070 - app.services.deepseek_client - INFO -   类型: extraction
2025-06-05 23:34:42,071 - app.services.deepseek_client - INFO -   成功: True
2025-06-05 23:34:42,071 - app.services.deepseek_client - INFO -   处理时间: 2758ms
2025-06-05 23:34:42,071 - app.services.deepseek_client - INFO -   结构化结果: {'industry': '直播，教育，咨询', 'personal_intro': '前交个朋友运营总监，遥望明星直播操盘手，小红书官方认证讲师，雨创盟流量圈创始人，擅长知识博主直播陪跑孵化、沙龙策划和承接、Ai+实体企业获客培训和陪跑。', 'location': None, 'can_provide': ['直播带货经验', 'IP低粉高客单变现经验', '沙龙客单低转高经验', '明星达人资源'], 'need_help': ['结识知识付费博主或机构', '结识缺少成熟项目的个人或团队', '各地区可合作沙龙渠道（教育、同城流量赛道）'], 'tags': ['斜杠青年', '运营', '直播', '操盘手', '小红书讲师', 'MCN', '商业博主', '私域销转', '知识付费', '沙龙策划', 'AI+获客', 'AI+跨境', 'AI+大健康']}
2025-06-05 23:34:42,071 - app.services.deepseek_client - INFO - 用户画像提取完成 [ID: 02dae5b6]: 直播，教育，咨询
2025-06-05 23:34:46,081 - app.services.deepseek_client - INFO - AI分析记录 [ID: 4d23083d]
2025-06-05 23:34:46,081 - app.services.deepseek_client - INFO -   类型: extraction
2025-06-05 23:34:46,081 - app.services.deepseek_client - INFO -   成功: True
2025-06-05 23:34:46,082 - app.services.deepseek_client - INFO -   处理时间: 3008ms
2025-06-05 23:34:46,082 - app.services.deepseek_client - INFO -   结构化结果: {'industry': 'AI/人工智能', 'personal_intro': '分享AI职业化的学习资源、路径、实践项目和工作机会', 'location': None, 'can_provide': ['AI职业化学习资源', 'AI职业化路径', 'AI实践项目', 'AI工作机会'], 'need_help': [], 'tags': ['AI', '职业化', '学习', '就业']}
2025-06-05 23:34:46,082 - app.services.deepseek_client - INFO - 用户画像提取完成 [ID: 4d23083d]: AI/人工智能
2025-06-05 23:34:50,147 - app.services.deepseek_client - INFO - AI分析记录 [ID: 0fe7f8b5]
2025-06-05 23:34:50,147 - app.services.deepseek_client - INFO -   类型: extraction
2025-06-05 23:34:50,147 - app.services.deepseek_client - INFO -   成功: True
2025-06-05 23:34:50,147 - app.services.deepseek_client - INFO -   处理时间: 3062ms
2025-06-05 23:34:50,147 - app.services.deepseek_client - INFO -   结构化结果: {'industry': 'APP出海', 'personal_intro': 'APP出海创业，前阿里出海业务', 'location': None, 'can_provide': [], 'need_help': [], 'tags': ['APP出海', '创业', '前阿里']}
2025-06-05 23:34:50,148 - app.services.deepseek_client - INFO - 用户画像提取完成 [ID: 0fe7f8b5]: APP出海
2025-06-05 23:34:53,342 - app.services.deepseek_client - INFO - AI分析记录 [ID: 2488561e]
2025-06-05 23:34:53,342 - app.services.deepseek_client - INFO -   类型: extraction
2025-06-05 23:34:53,342 - app.services.deepseek_client - INFO -   成功: True
2025-06-05 23:34:53,342 - app.services.deepseek_client - INFO -   处理时间: 2183ms
2025-06-05 23:34:53,343 - app.services.deepseek_client - INFO -   结构化结果: {'industry': '培训', 'personal_intro': '在云南昆明给街道办培训公务应用', 'location': '云南昆明', 'can_provide': ['公务应用培训'], 'need_help': [], 'tags': ['培训师', '公务应用', '昆明']}
2025-06-05 23:34:53,343 - app.services.deepseek_client - INFO - 用户画像提取完成 [ID: 2488561e]: 培训
2025-06-05 23:34:53,343 - app.services.deepseek_client - INFO - 提取进度: 5/10 (50.0%) - 速度: 0.3 个/秒 - 预计剩余: 17.2秒
2025-06-05 23:34:58,082 - app.services.deepseek_client - INFO - AI分析记录 [ID: 6c41db4e]
2025-06-05 23:34:58,083 - app.services.deepseek_client - INFO -   类型: extraction
2025-06-05 23:34:58,083 - app.services.deepseek_client - INFO -   成功: True
2025-06-05 23:34:58,083 - app.services.deepseek_client - INFO -   处理时间: 3727ms
2025-06-05 23:34:58,083 - app.services.deepseek_client - INFO -   结构化结果: {'industry': None, 'personal_intro': '在夜校教了三期课程', 'location': None, 'can_provide': ['课程教学'], 'need_help': [], 'tags': ['教师', '教育']}
2025-06-05 23:34:58,083 - app.services.deepseek_client - INFO - 用户画像提取完成 [ID: 6c41db4e]: None
2025-06-05 23:35:02,564 - app.services.deepseek_client - INFO - AI分析记录 [ID: b13061ec]
2025-06-05 23:35:02,564 - app.services.deepseek_client - INFO -   类型: extraction
2025-06-05 23:35:02,564 - app.services.deepseek_client - INFO -   成功: True
2025-06-05 23:35:02,564 - app.services.deepseek_client - INFO -   处理时间: 3474ms
2025-06-05 23:35:02,565 - app.services.deepseek_client - INFO -   结构化结果: {'industry': 'AI', 'personal_intro': 'InstantStudioAI 专注于企业ComfyUI工作流和图像模型定制，提供文生图、图生图、虚拟试衣及相关API服务。', 'location': None, 'can_provide': ['企业ComfyUI工作流', '图像模型定制', '文生图', '图生图', '虚拟试衣', '相关API服务'], 'need_help': [], 'tags': ['ComfyUI', '图像模型', 'AI', '文生图', '图生图', '虚拟试衣']}
2025-06-05 23:35:02,565 - app.services.deepseek_client - INFO - 用户画像提取完成 [ID: b13061ec]: AI
2025-06-05 23:35:05,524 - app.services.deepseek_client - INFO - AI分析记录 [ID: d0178ff8]
2025-06-05 23:35:05,524 - app.services.deepseek_client - INFO -   类型: extraction
2025-06-05 23:35:05,524 - app.services.deepseek_client - INFO -   成功: True
2025-06-05 23:35:05,524 - app.services.deepseek_client - INFO -   处理时间: 1946ms
2025-06-05 23:35:05,524 - app.services.deepseek_client - INFO -   结构化结果: {'industry': '新媒体', 'personal_intro': '此人之前在新媒体公司工作，但公司排斥员工使用AI。', 'location': None, 'can_provide': [], 'need_help': [], 'tags': ['AI抵触', '新媒体']}
2025-06-05 23:35:05,525 - app.services.deepseek_client - INFO - 用户画像提取完成 [ID: d0178ff8]: 新媒体
2025-06-05 23:35:09,519 - app.services.deepseek_client - INFO - AI分析记录 [ID: 74ba5e65]
2025-06-05 23:35:09,519 - app.services.deepseek_client - INFO -   类型: extraction
2025-06-05 23:35:09,519 - app.services.deepseek_client - INFO -   成功: True
2025-06-05 23:35:09,519 - app.services.deepseek_client - INFO -   处理时间: 2979ms
2025-06-05 23:35:09,519 - app.services.deepseek_client - INFO -   结构化结果: {'industry': None, 'personal_intro': '此人表示离开AI就不会工作了', 'location': None, 'can_provide': [], 'need_help': [], 'tags': ['AI', '工作']}
2025-06-05 23:35:09,520 - app.services.deepseek_client - INFO - 用户画像提取完成 [ID: 74ba5e65]: None
2025-06-05 23:35:12,864 - app.services.deepseek_client - INFO - AI分析记录 [ID: fb2c1c28]
2025-06-05 23:35:12,865 - app.services.deepseek_client - INFO -   类型: extraction
2025-06-05 23:35:12,865 - app.services.deepseek_client - INFO -   成功: True
2025-06-05 23:35:12,865 - app.services.deepseek_client - INFO -   处理时间: 2330ms
2025-06-05 23:35:12,865 - app.services.deepseek_client - INFO -   结构化结果: {'industry': '3D动画制作', 'personal_intro': 'Mixamo是一个基于web版的在线3D人物动画制作平台，可以更轻易地创建出3D人物动画，提供3D模型和动画文件，并支持上传自己的3D模型进行创作。适用于游戏、广告、视频等项目，可与主流3D软件和游戏引擎协同工作。', 'location': None, 'can_provide': ['3D人物动画制作平台', '3D模型', '动画文件', '简化动画制作流程', 'Mixamo使用教程'], 'need_help': [], 'tags': ['3D动画', '在线平台', 'Adobe', '游戏开发', '视频制作', '动画制作工具']}
2025-06-05 23:35:12,866 - app.services.deepseek_client - INFO - 用户画像提取完成 [ID: fb2c1c28]: 3D动画制作
2025-06-05 23:35:12,866 - app.services.deepseek_client - INFO - 提取进度: 10/10 (100.0%) - 速度: 0.3 个/秒 - 预计剩余: 0.0秒
2025-06-05 23:35:12,866 - app.services.deepseek_client - INFO - 稳定提取完成:
2025-06-05 23:35:12,866 - app.services.deepseek_client - INFO -   总自我介绍: 10 个
2025-06-05 23:35:12,866 - app.services.deepseek_client - INFO -   成功提取: 10 个
2025-06-05 23:35:12,866 - app.services.deepseek_client - INFO -   成功率: 100.0%
2025-06-05 23:35:12,866 - app.services.deepseek_client - INFO -   总耗时: 36.75秒
2025-06-05 23:35:12,866 - app.services.deepseek_client - INFO -   平均速度: 0.3 个/秒
2025-06-05 23:35:12,866 - app.services.analysis_service - INFO - 提取用户画像成功 [1/10]: 下夕烟 - AI
2025-06-05 23:35:12,884 - app.services.analysis_service - INFO - 提取用户画像成功 [2/10]: 沙龙小王子rain雨 - 直播，教育，咨询
2025-06-05 23:35:12,887 - app.services.analysis_service - INFO - 提取用户画像成功 [3/10]: 刘怡功『功导』 - AI/人工智能
2025-06-05 23:35:12,888 - app.services.analysis_service - INFO - 提取用户画像成功 [4/10]: Tim - APP出海
2025-06-05 23:35:12,889 - app.services.analysis_service - INFO - 提取用户画像成功 [5/10]: 风缘+吴春锋 - 培训
2025-06-05 23:35:12,892 - app.services.analysis_service - INFO - 提取用户画像成功 [6/10]: 佛山-家炜-合同审核自动化 - 未知行业
2025-06-05 23:35:12,896 - app.services.analysis_service - INFO - 提取用户画像成功 [7/10]: 文斌 - AI
2025-06-05 23:35:12,898 - app.services.analysis_service - INFO - 提取用户画像成功 [8/10]: 佛山 小冬 - 新媒体
2025-06-05 23:35:12,899 - app.services.analysis_service - INFO - 提取用户画像成功 [9/10]: 苏白 - 未知行业
2025-06-05 23:35:12,902 - app.services.analysis_service - INFO - 提取用户画像成功 [10/10]: 刀哥 - 3D动画制作
2025-06-05 23:35:12,904 - app.services.analysis_service - INFO - 批量用户画像提取完成，成功提取 10 个用户画像
2025-06-05 23:35:12,915 - app.services.analysis_service - WARNING - 飞书表格配置不完整，跳过同步到飞书
2025-06-05 23:35:12,916 - app.services.analysis_service - WARNING - app_token: 未配置
2025-06-05 23:35:12,916 - app.services.analysis_service - WARNING - table_id: 未配置
2025-06-05 23:35:12,916 - app.services.analysis_service - INFO - 已将 10 个用户画像保存到应用状态
2025-06-05 23:35:12,916 - app.services.analysis_service - INFO - 分析任务完成，总共处理743条消息，发现10个自我介绍，提取10个用户画像
2025-06-05 23:35:17,185 - app.ui.results_tab - INFO - 从应用状态加载了 10 个用户画像
2025-06-05 23:35:17,185 - app.ui.results_tab - INFO - 结果数据刷新完成
2025-06-05 23:37:50,817 - app.ui.config_tab - INFO - 配置保存成功
2025-06-05 23:37:51,357 - app.ui.results_tab - INFO - 从应用状态加载了 10 个用户画像
2025-06-05 23:37:51,357 - app.ui.results_tab - INFO - 结果数据刷新完成
2025-06-05 23:39:01,611 - app.ui.main_window - INFO - 应用资源清理完成
2025-06-05 23:39:01,611 - __main__ - INFO - 应用正常退出
