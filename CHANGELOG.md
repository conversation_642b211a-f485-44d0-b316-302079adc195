# 更新日志

## v1.0.2 (2024-12-04)

### 🔧 重要修复
- **Chatlog API格式适配**：根据实际API格式更新了chatlog客户端
  - 群聊列表API：正确使用 `/api/v1/chatroom?format=json` 格式
  - 消息查询API：支持 `time=2024-05-22~2025-05-22` 时间范围格式
  - 消息解析：正确解析 `seq`、`time`、`sender`、`senderName` 等字段
  - 消息类型：支持根据 `type` 字段正确识别消息类型（文本、图片、文件、系统消息）

### ✨ 功能改进
- **时间解析增强**：支持带时区的ISO时间格式 `2025-05-01T13:04:33+08:00`
- **群组信息完善**：正确提取群组名称（nickName）、备注（remark）和成员数量
- **消息处理优化**：改进了系统消息的处理逻辑
- **错误处理**：增强了API调用的错误处理和日志记录

### 🧪 测试工具
- **新增chatlog测试脚本**：`test_chatlog_api.py` 用于验证API集成
- **格式验证**：测试实际API数据格式的解析正确性
- **连接测试**：支持异步和同步两种客户端的测试

### 📚 技术细节
- **API端点更新**：所有chatlog API调用都正确添加了 `format=json` 参数
- **数据映射**：群组 `name` 字段作为ID，`nickName` 字段作为显示名称
- **时间范围**：消息查询使用 `YYYY-MM-DD~YYYY-MM-DD` 格式
- **兼容性**：保持向前兼容，支持多种时间格式的解析

---

## v1.0.1 (2024-12-04)

### 🔧 Bug修复
- **修复飞书客户端导入错误**：修复了 `feishu_client.py` 中 `timedelta` 未导入的问题
- **修复DeepSeek集成**：根据官方文档正确集成 `llama-index-llms-deepseek`

### ✨ 功能改进
- **DeepSeek官方集成**：使用 LlamaIndex 官方的 DeepSeek 集成包，替代之前的 OpenAILike 方案
- **智能降级方案**：当 DeepSeek 包未安装时，自动降级到 OpenAILike 作为 fallback
- **更好的错误处理**：改进了 AI 响应解析和错误处理逻辑
- **更准确的提示词**：优化了自我介绍检测和用户画像提取的提示词

### 🔄 依赖更新
- **Python版本要求**：从 3.8.1+ 提升到 3.9+（DeepSeek集成包要求）
- **新增AI依赖**：`llama-index-llms-deepseek>=0.1.0,<0.2.0`
- **依赖管理**：继续使用 uv 进行现代化包管理

### 📚 文档更新
- **README更新**：更新了Python版本要求和技术架构描述
- **集成测试**：添加了 DeepSeek 集成测试脚本

---

## v1.0.0 (2024-12-03)

### 🎉 首次发布
- ✨ 完整的用户画像分析功能
- 🔧 现代化图形界面（ttkbootstrap）
- 📊 数据导出和飞书同步功能
- 🤖 AI智能识别和提取（基于DeepSeek）
- ⚡ 使用uv进行现代化包管理
- 🏗️ 完整的项目架构和测试框架

### 核心功能
- **智能识别**：基于正则表达式模式匹配和AI智能识别自我介绍消息
- **画像提取**：自动提取昵称、职业、个人介绍、能提供的帮助、寻求的帮助、行业领域等信息
- **数据同步**：支持同步到飞书多维表格，便于团队协作
- **可视化界面**：现代化的图形界面，操作简单直观
- **数据导出**：支持导出Excel、JSON等格式
- **实时监控**：分析进度实时显示，任务状态清晰可见

### 技术特性
- **项目结构**：模块化设计，易于维护和扩展
- **依赖管理**：使用pyproject.toml和uv进行现代化包管理
- **测试框架**：集成pytest测试框架
- **代码质量**：配置了black、isort、flake8等代码质量工具
- **构建系统**：自动化构建和发布流程

---

## 贡献指南

### 版本规范
- 遵循语义化版本控制 (Semantic Versioning)
- 主版本号：重大不兼容更改
- 次版本号：新功能添加
- 修订版本号：Bug修复

### 更新类型
- 🎉 **新功能** (feat)
- 🔧 **Bug修复** (fix) 
- 📚 **文档更新** (docs)
- 🔄 **依赖更新** (deps)
- ♻️ **代码重构** (refactor)
- 🚀 **性能优化** (perf)
- 🧪 **测试相关** (test) 