#!/usr/bin/env python3
"""
构建和部署脚本 - 支持uv包管理器
用于打包应用程序和创建发布版本
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_uv_installed():
    """检查uv是否已安装"""
    try:
        result = subprocess.run(['uv', '--version'], capture_output=True, text=True, check=True)
        print(f"✅ uv已安装: {result.stdout.strip()}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ uv未安装")
        print("请使用以下命令安装uv:")
        print("pip install uv")
        print("或者访问: https://docs.astral.sh/uv/")
        return False

def check_dependencies():
    """检查项目依赖是否已同步"""
    print("检查项目依赖...")
    
    # 检查虚拟环境
    venv_path = Path(".venv")
    if not venv_path.exists():
        print("❌ 虚拟环境不存在，请运行: uv sync")
        return False
    
    try:
        # 使用uv检查依赖状态
        result = subprocess.run(['uv', 'pip', 'list'], capture_output=True, text=True, check=True)
        packages = result.stdout.strip().split('\n')
        print(f"✅ 已安装 {len(packages)-1} 个依赖包")
        return True
    except subprocess.CalledProcessError:
        print("❌ 依赖检查失败")
        return False

def sync_dependencies():
    """同步项目依赖"""
    print("\n同步项目依赖...")
    
    try:
        subprocess.run(['uv', 'sync'], check=True)
        print("✅ 依赖同步完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖同步失败: {e}")
        return False

def install_ai_dependencies():
    """安装AI相关依赖"""
    print("\n安装AI相关依赖...")
    
    try:
        subprocess.run(['uv', 'sync', '--extra', 'ai'], check=True)
        print("✅ AI依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ AI依赖安装失败: {e}")
        return False

def install_build_dependencies():
    """安装构建依赖"""
    print("\n安装构建依赖...")
    
    try:
        subprocess.run(['uv', 'sync', '--extra', 'build'], check=True)
        print("✅ 构建依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建依赖安装失败: {e}")
        return False

def create_executable():
    """创建可执行文件"""
    print("\n创建可执行文件...")
    
    # 清理之前的构建
    build_dirs = ['dist', 'build', '__pycache__']
    for dir_name in build_dirs:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name)
            print(f"清理 {dir_name} 目录")
    
    # PyInstaller 参数
    cmd = [
        'uv', 'run', 'pyinstaller',
        '--onefile',
        '--windowed',
        '--name=微信群用户档案分析器',
        '--clean',
        '--noconfirm',
        'app/main.py'
    ]
    
    # 添加图标（如果存在）
    icon_path = Path('app/assets/icon.ico')
    if icon_path.exists():
        cmd.extend(['--icon', str(icon_path)])
    
    # 添加数据文件
    data_files = [
        ('README.md', '.'),
        ('env.example', '.'),
    ]
    
    for src, dst in data_files:
        if Path(src).exists():
            cmd.extend(['--add-data', f'{src};{dst}'])
    
    try:
        print("执行命令:", ' '.join(cmd))
        subprocess.run(cmd, check=True)
        print("✅ 可执行文件创建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 可执行文件创建失败: {e}")
        return False

def run_tests():
    """运行测试"""
    print("\n运行测试...")
    
    try:
        subprocess.run(['uv', 'run', 'pytest', '-v'], check=True)
        print("✅ 测试通过")
        return True
    except subprocess.CalledProcessError as e:
        print(f"⚠️ 测试失败: {e}")
        return False

def run_linting():
    """运行代码检查"""
    print("\n运行代码检查...")
    
    checks = [
        (['uv', 'run', 'black', '--check', 'app/'], "代码格式检查"),
        (['uv', 'run', 'isort', '--check-only', 'app/'], "导入排序检查"),
        (['uv', 'run', 'flake8', 'app/'], "代码风格检查"),
    ]
    
    all_passed = True
    for cmd, desc in checks:
        try:
            subprocess.run(cmd, check=True, capture_output=True)
            print(f"✅ {desc}通过")
        except subprocess.CalledProcessError:
            print(f"⚠️ {desc}失败")
            all_passed = False
    
    return all_passed

def format_code():
    """格式化代码"""
    print("\n格式化代码...")
    
    try:
        subprocess.run(['uv', 'run', 'black', 'app/'], check=True)
        subprocess.run(['uv', 'run', 'isort', 'app/'], check=True)
        print("✅ 代码格式化完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 代码格式化失败: {e}")
        return False

def create_release_package():
    """创建发布包"""
    print("\n创建发布包...")
    
    release_dir = Path("release")
    if release_dir.exists():
        shutil.rmtree(release_dir)
        print("清理旧的发布包")
    
    release_dir.mkdir()
    
    # 复制必要文件
    files_to_copy = [
        "README.md",
        "pyproject.toml",
        "env.example",
        "技术架构文档.md"
    ]
    
    for file in files_to_copy:
        if Path(file).exists():
            shutil.copy2(file, release_dir)
            print(f"✅ 复制 {file}")
    
    # 复制可执行文件（如果存在）
    exe_file = Path("dist/微信群用户档案分析器.exe")
    if exe_file.exists():
        shutil.copy2(exe_file, release_dir)
        print(f"✅ 复制可执行文件")
    
    # 复制应用目录
    app_dir = Path("app")
    if app_dir.exists():
        shutil.copytree(app_dir, release_dir / "app")
        print("✅ 复制应用代码")
    
    # 创建uv安装脚本
    install_script = release_dir / "安装依赖.bat"
    with open(install_script, 'w', encoding='utf-8') as f:
        f.write("""@echo off
chcp 65001 >nul
echo 微信群用户档案分析器 - 依赖安装脚本
echo.

REM 检查uv是否安装
uv --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到uv包管理器
    echo 请先安装uv: pip install uv
    echo 或访问: https://docs.astral.sh/uv/
    pause
    exit /b 1
)

echo 正在同步项目依赖...
uv sync

if %errorlevel% equ 0 (
    echo.
    echo ✅ 依赖安装完成！
    echo 现在可以运行: uv run python app/main.py
) else (
    echo.
    echo ❌ 依赖安装失败，请检查错误信息
)

pause
""")
    
    # 创建启动脚本
    start_script = release_dir / "启动应用.bat"
    with open(start_script, 'w', encoding='utf-8') as f:
        f.write("""@echo off
chcp 65001 >nul
echo 启动微信群用户档案分析器...
echo.

REM 检查uv
uv --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到uv包管理器
    echo 请先运行: 安装依赖.bat
    pause
    exit /b 1
)

REM 启动应用
echo 启动应用程序...
uv run python app/main.py

pause
""")
    
    # 创建Linux/Mac启动脚本
    start_script_unix = release_dir / "start.sh"
    with open(start_script_unix, 'w', encoding='utf-8') as f:
        f.write("""#!/bin/bash
echo "启动微信群用户档案分析器..."

# 检查uv
if ! command -v uv &> /dev/null; then
    echo "错误: 未找到uv包管理器"
    echo "请先安装uv: https://docs.astral.sh/uv/"
    exit 1
fi

# 同步依赖（如果需要）
echo "检查依赖..."
uv sync

# 启动应用
echo "启动应用程序..."
uv run python app/main.py
""")
    
    # 设置执行权限
    start_script_unix.chmod(0o755)
    
    print(f"✅ 创建启动脚本")
    
    # 创建使用说明
    usage_file = release_dir / "使用说明.md"
    with open(usage_file, 'w', encoding='utf-8') as f:
        f.write("""# 使用说明

## 安装和启动

### Windows用户
1. 双击运行 `安装依赖.bat` 安装依赖
2. 双击运行 `启动应用.bat` 启动程序

### Linux/Mac用户
1. 运行 `chmod +x start.sh` 设置权限
2. 运行 `./start.sh` 启动程序

### 手动运行
```bash
# 安装依赖
uv sync

# 安装AI功能（可选）
uv sync --extra ai

# 启动程序
uv run python app/main.py
```

## 开发模式

```bash
# 安装开发依赖
uv sync --extra dev

# 运行测试
uv run pytest

# 代码格式化
uv run black app/
uv run isort app/

# 代码检查
uv run flake8 app/
```

## 配置

1. 复制 `env.example` 为 `.env`
2. 在 `.env` 中填入你的API密钥
3. 在应用的"配置设置"标签页中完成配置

详细说明请参考 README.md
""")
    
    print(f"✅ 创建使用说明")
    print(f"\n📦 发布包已创建到: {release_dir.absolute()}")

def main():
    """主函数"""
    print("="*60)
    print("微信群用户档案分析器 - 构建脚本 (uv版本)")
    print("="*60)
    
    # 检查uv
    if not check_uv_installed():
        return
    
    print("\n选择操作:")
    print("1. 同步依赖")
    print("2. 安装AI依赖")
    print("3. 安装构建依赖")
    print("4. 运行测试")
    print("5. 代码检查")
    print("6. 格式化代码")
    print("7. 创建可执行文件")
    print("8. 创建发布包")
    print("9. 完整构建流程（推荐）")
    print("0. 退出")
    
    choice = input("\n请选择 (0-9): ").strip()
    
    if choice == "0":
        print("退出构建")
        return
    elif choice == "1":
        sync_dependencies()
    elif choice == "2":
        install_ai_dependencies()
    elif choice == "3":
        install_build_dependencies()
    elif choice == "4":
        run_tests()
    elif choice == "5":
        run_linting()
    elif choice == "6":
        format_code()
    elif choice == "7":
        if install_build_dependencies():
            create_executable()
    elif choice == "8":
        create_release_package()
    elif choice == "9":
        print("\n🚀 开始完整构建流程...")
        
        steps = [
            ("同步基础依赖", sync_dependencies),
            ("安装AI依赖", install_ai_dependencies),
            ("安装构建依赖", install_build_dependencies),
            ("格式化代码", format_code),
            ("运行代码检查", run_linting),
            ("运行测试", run_tests),
            ("创建可执行文件", create_executable),
            ("创建发布包", create_release_package),
        ]
        
        success_count = 0
        for step_name, step_func in steps:
            print(f"\n📋 执行步骤: {step_name}")
            if step_func():
                success_count += 1
                print(f"✅ {step_name} 完成")
            else:
                print(f"⚠️ {step_name} 失败，继续下一步...")
        
        print(f"\n🎉 构建完成！成功执行 {success_count}/{len(steps)} 个步骤")
        
        if success_count == len(steps):
            print("🌟 所有步骤都成功完成！")
        else:
            print("⚠️ 部分步骤失败，请检查错误信息")
        
    else:
        print("无效选择")

if __name__ == "__main__":
    main() 