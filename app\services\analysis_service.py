"""
核心分析服务
协调整个用户画像分析流程，包括消息获取、AI自我介绍检测、画像提取和数据同步
"""

import asyncio
import uuid
from typing import List, Dict, Any, Optional, Callable
import logging
from datetime import datetime, timedelta

from ..models import (
    AnalysisTask, UserProfile, IntroductionCandidate, 
    IntroductionStatus, ChatGroup, Message, AppState, MessageType
)
from ..config import get_config
from .chatlog_client import ChatlogClient
from .deepseek_client import DeepSeekClient
from .feishu_client import FeishuClient

logger = logging.getLogger(__name__)

class AnalysisService:
    """核心分析服务"""
    
    def __init__(self):
        self.config = get_config()
        self.app_state = AppState()
        
        # 初始化各个服务
        self.chatlog_client = None
        self.deepseek_client = DeepSeekClient(use_openai_like=self.config.deepseek.use_openai_like)
        self.feishu_client = None
        
        # 进度回调函数
        self.progress_callback: Optional[Callable[[str, Dict[str, Any]], None]] = None
        
        # 当前分析任务
        self.current_task: Optional[AnalysisTask] = None
        
        # 取消令牌
        self._cancel_event = asyncio.Event()
        
    def set_progress_callback(self, callback: Callable[[str, Dict[str, Any]], None]):
        """设置进度回调函数"""
        self.progress_callback = callback
    
    def _notify_progress(self, event: str, data: Dict[str, Any]):
        """通知进度更新"""
        if self.progress_callback:
            try:
                self.progress_callback(event, data)
            except Exception as e:
                logger.error(f"进度回调函数执行失败: {e}")
    
    async def test_api_connections(self) -> Dict[str, bool]:
        """测试所有API连接"""
        results = {}
        
        try:
            # 测试Chatlog连接
            async with ChatlogClient() as chatlog:
                self.app_state.update_api_status('chatlog', 'testing')
                chatlog_ok = await chatlog.test_connection()
                results['chatlog'] = chatlog_ok
                self.app_state.update_api_status('chatlog', 'connected' if chatlog_ok else 'failed')
        except Exception as e:
            logger.error(f"测试Chatlog连接失败: {e}")
            results['chatlog'] = False
            self.app_state.update_api_status('chatlog', 'failed')
        
        try:
            # 测试DeepSeek连接
            self.app_state.update_api_status('deepseek', 'testing')
            deepseek_ok = await self.deepseek_client.test_connection()
            results['deepseek'] = deepseek_ok
            self.app_state.update_api_status('deepseek', 'connected' if deepseek_ok else 'failed')
        except Exception as e:
            logger.error(f"测试DeepSeek连接失败: {e}")
            results['deepseek'] = False
            self.app_state.update_api_status('deepseek', 'failed')
        
        try:
            # 测试Feishu连接
            async with FeishuClient() as feishu:
                self.app_state.update_api_status('feishu', 'testing')
                feishu_ok = await feishu.test_connection()
                results['feishu'] = feishu_ok
                self.app_state.update_api_status('feishu', 'connected' if feishu_ok else 'failed')
        except Exception as e:
            logger.error(f"测试Feishu连接失败: {e}")
            results['feishu'] = False
            self.app_state.update_api_status('feishu', 'failed')
        
        self._notify_progress('api_test_completed', results)
        return results
    
    async def get_chat_groups(self) -> List[ChatGroup]:
        """获取所有群组列表"""
        try:
            async with ChatlogClient() as chatlog:
                groups = await chatlog.get_chat_groups()
                self._notify_progress('groups_loaded', {'count': len(groups)})
                return groups
        except Exception as e:
            logger.error(f"获取群组列表失败: {e}")
            self._notify_progress('groups_load_failed', {'error': str(e)})
            return []
    
    async def start_analysis(
        self, 
        chat_id: str, 
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> AnalysisTask:
        """
        开始分析指定群组
        
        Args:
            chat_id: 群组ID
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            分析任务对象
        """
        # 检查是否已有任务在运行
        if self.app_state.is_analyzing:
            raise RuntimeError("已有分析任务在运行中")
        
        # 重置取消事件
        self._cancel_event.clear()
        
        # 创建分析任务
        task = AnalysisTask(
            id=str(uuid.uuid4()),
            chat_id=chat_id,
            start_time=datetime.now()
        )
        
        self.current_task = task
        self.app_state.is_analyzing = True
        self.app_state.current_task = task
        self.app_state.current_chat_id = chat_id
        
        logger.info(f"开始分析群组 {chat_id}，任务ID: {task.id}")
        
        try:
            # 执行分析流程
            await self._run_analysis_pipeline(task, start_time, end_time)
            
        except asyncio.CancelledError:
            logger.info(f"分析任务被取消: {task.id}")
            task.status = "cancelled"
            
        except Exception as e:
            logger.error(f"分析任务失败: {e}")
            task.status = "failed"
            task.error_messages.append(str(e))
            
        finally:
            task.end_time = datetime.now()
            self.app_state.is_analyzing = False
            
        self._notify_progress('analysis_completed', {
            'task_id': task.id,
            'status': task.status,
            'duration': (task.end_time - task.start_time).total_seconds(),
            'results': {
                'total_messages': task.total_messages,
                'found_introductions': task.found_introductions,
                'extracted_profiles': task.extracted_profiles,
                'synced_to_feishu': task.synced_to_feishu
            },
            'ai_stats': self.deepseek_client.get_analysis_stats()
        })
        
        return task
    
    async def _run_analysis_pipeline(
        self, 
        task: AnalysisTask, 
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ):
        """执行分析流水线"""
        
        # 第一步：获取群组消息
        self._check_cancelled()
        self._notify_progress('step_started', {'step': 'fetch_messages', 'task_id': task.id})
        messages = await self._fetch_messages(task, start_time, end_time)
        
        if not messages:
            task.status = "completed"
            logger.warning("未获取到任何消息")
            return
        
        # 第二步：使用AI检测所有消息中的自我介绍
        self._check_cancelled()
        self._notify_progress('step_started', {'step': 'ai_detect_introductions', 'task_id': task.id})
        candidates = await self._ai_detect_introductions(task, messages)
        
        if not candidates:
            task.status = "completed"
            logger.info("未发现自我介绍")
            return
        
        # 第三步：使用AI提取用户画像
        self._check_cancelled()
        self._notify_progress('step_started', {'step': 'ai_extract_profiles', 'task_id': task.id})
        profiles = await self._ai_extract_profiles(task, candidates)
        
        if not profiles:
            task.status = "completed"
            logger.warning("未成功提取用户画像")
            return
        
        # 第四步：同步到飞书
        self._check_cancelled()
        self._notify_progress('step_started', {'step': 'sync_to_feishu', 'task_id': task.id})
        await self._sync_to_feishu(task, profiles)
        
        # 保存用户画像到AppState
        if profiles:
            self.app_state.add_completed_profiles(profiles)
            logger.info(f"已将 {len(profiles)} 个用户画像保存到应用状态")
        
        task.status = "completed"
        logger.info(f"分析任务完成，总共处理{len(messages)}条消息，发现{len(candidates)}个自我介绍，提取{len(profiles)}个用户画像")
    
    async def _fetch_messages(
        self, 
        task: AnalysisTask, 
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> List[Message]:
        """获取群组消息"""
        try:
            async with ChatlogClient() as chatlog:
                # 确保总是有时间范围
                if end_time is None:
                    end_time = datetime.now()
                if start_time is None:
                    start_time = end_time - timedelta(days=30)  # 默认30天
                
                logger.info(f"正在获取群组 {task.chat_id} 的消息")
                logger.info(f"时间范围: {start_time.strftime('%Y-%m-%d %H:%M:%S')} 到 {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
                
                # 使用正确的方法名
                messages = await chatlog.get_chat_messages(
                    chat_id=task.chat_id,
                    start_time=start_time,
                    end_time=end_time,
                    limit=5000  # 增加限制数量
                )
                
                task.total_messages = len(messages)
                logger.info(f"获取到 {len(messages)} 条消息")
                
                # 添加调试信息
                if len(messages) == 0:
                    logger.warning(f"未获取到任何消息，请检查:")
                    logger.warning(f"  - 群组ID: {task.chat_id}")
                    logger.warning(f"  - 时间范围: {start_time} 到 {end_time}")
                    logger.warning(f"  - 该群组在此时间范围内可能没有消息")
                else:
                    # 显示获取到的消息统计
                    logger.info(f"消息统计:")
                    logger.info(f"  - 总数: {len(messages)}")
                    
                    # 按消息类型统计
                    type_counts = {}
                    for msg in messages:
                        msg_type = msg.message_type.value
                        type_counts[msg_type] = type_counts.get(msg_type, 0) + 1
                    logger.info(f"  - 类型分布: {type_counts}")
                    
                    # 显示时间范围
                    if messages:
                        timestamps = [msg.timestamp for msg in messages if msg.timestamp]
                        if timestamps:
                            logger.info(f"  - 时间范围: {min(timestamps)} 到 {max(timestamps)}")
                            
                            # 统计文本消息数量（用于自我介绍检测）
                            text_messages = [msg for msg in messages if msg.message_type == MessageType.TEXT and len(msg.content.strip()) >= 10]
                            logger.info(f"  - 可用于检测的文本消息: {len(text_messages)} 条")
                
                self._notify_progress('messages_fetched', {
                    'task_id': task.id,
                    'count': len(messages),
                    'time_range': {
                        'start': start_time.isoformat(),
                        'end': end_time.isoformat()
                    }
                })
                
                return messages
                
        except Exception as e:
            logger.error(f"获取消息失败: {e}")
            task.error_messages.append(f"获取消息失败: {str(e)}")
            raise
    
    async def _ai_detect_introductions(
        self, 
        task: AnalysisTask, 
        messages: List[Message]
    ) -> List[IntroductionCandidate]:
        """使用AI批量检测所有消息中的自我介绍"""
        candidates = []
        
        # 过滤出可能的自我介绍候选消息（文本消息且长度合适）
        text_messages = [
            msg for msg in messages 
            if msg.message_type == MessageType.TEXT and 10 <= len(msg.content.strip()) <= 2000
        ]
        
        if not text_messages:
            logger.info("没有找到合适的文本消息进行检测")
            return candidates
        
        logger.info(f"开始批量AI检测 {len(text_messages)} 条消息中的自我介绍")
        
        try:
            # 检查是否已取消
            self._check_cancelled()
            
            # 提取消息内容
            message_contents = [msg.content for msg in text_messages]
            
            # 使用并发批量检测方法
            # 根据消息数量动态调整参数
            if len(message_contents) <= 100:
                batch_size = 40
                max_concurrent = 2
            elif len(message_contents) <= 1000:
                batch_size = 100
                max_concurrent = 6
            else:
                batch_size = 2000
                max_concurrent = 4
            
            logger.info(f"检测参数: 批次大小={batch_size}, 并发数={max_concurrent}")
            
            detection_results = await self.deepseek_client.batch_detect_introductions(
                message_contents, 
                batch_size=batch_size,
                max_concurrent=max_concurrent,
                cancel_event=self._cancel_event  # 传递取消事件
            )
            
            # 处理检测结果
            for i, (message, detection_result) in enumerate(zip(text_messages, detection_results)):
                try:
                    # 定期检查是否已取消
                    if i % 50 == 0:
                        self._check_cancelled()
                    
                    # 如果AI检测为自我介绍且置信度足够高
                    if detection_result.get('is_introduction', False) and detection_result.get('confidence', 0) >= 0.6:
                        candidate = IntroductionCandidate(
                            id=str(uuid.uuid4()),
                            message_id=message.id,
                            sender_id=message.user_id,
                            sender_name=message.username,
                            content=message.content,
                            timestamp=message.timestamp,
                            confidence_score=detection_result.get('confidence', 0.0),
                            detection_reason=detection_result.get('reason', ''),
                            keywords=detection_result.get('keywords', []),
                            introduction_type=detection_result.get('introduction_type', 'unknown'),
                            ai_request_id=detection_result.get('request_id', ''),
                            status=IntroductionStatus.DETECTED
                        )
                        
                        candidates.append(candidate)
                        task.found_introductions += 1
                        
                        logger.info(f"发现自我介绍 [{i+1}/{len(text_messages)}]: "
                                  f"{message.username} (置信度: {detection_result.get('confidence', 0):.2f})")
                
                    # 每处理100条消息更新一次进度
                    if (i + 1) % 100 == 0 or i == len(text_messages) - 1:
                        self._notify_progress('detection_progress', {
                            'task_id': task.id,
                            'processed': i + 1,
                            'total': len(text_messages),
                            'found': len(candidates)
                        })
                    
                except Exception as e:
                    logger.error(f"处理检测结果失败 [消息{i+1}]: {e}")
                    continue
            
        except asyncio.CancelledError:
            logger.info("AI检测过程被取消")
            raise
        except Exception as e:
            logger.error(f"批量AI检测失败: {e}")
            task.error_messages.append(f"AI检测失败: {str(e)}")
            raise
        
        logger.info(f"批量AI检测完成，在 {len(text_messages)} 条消息中发现 {len(candidates)} 个自我介绍")
        return candidates
    
    async def _ai_extract_profiles(
        self, 
        task: AnalysisTask, 
        candidates: List[IntroductionCandidate]
    ) -> List[UserProfile]:
        """使用AI批量从自我介绍中提取用户画像"""
        profiles = []
        
        if not candidates:
            logger.info("没有自我介绍候选需要提取")
            return profiles
        
        logger.info(f"开始批量从 {len(candidates)} 个自我介绍中提取用户画像")
        
        try:
            # 检查是否已取消
            self._check_cancelled()
            
            # 提取自我介绍内容
            introduction_contents = [candidate.content for candidate in candidates]
            
            # 使用并发批量提取方法
            # 根据候选数量动态调整参数
            if len(introduction_contents) <= 20:
                batch_size = 5
                max_concurrent = 1
            elif len(introduction_contents) <= 50:
                batch_size = 8
                max_concurrent = 2
            else:
                batch_size = 10
                max_concurrent = 2
            
            logger.info(f"提取参数: 批次大小={batch_size}, 并发数={max_concurrent}")
            
            extraction_results = await self.deepseek_client.batch_extract_profiles(
                introduction_contents, 
                batch_size=batch_size,
                max_concurrent=max_concurrent,
                cancel_event=self._cancel_event  # 传递取消事件
            )
            
            # 处理提取结果
            for i, (candidate, extraction_result) in enumerate(zip(candidates, extraction_results)):
                try:
                    # 定期检查是否已取消
                    if i % 10 == 0:
                        self._check_cancelled()
                    
                    if extraction_result.get('success', False):
                        profile_data = extraction_result.get('profile', {})
                        
                        # 创建UserProfile对象
                        profile = UserProfile(
                            id=str(uuid.uuid4()),
                            introduction_id=candidate.id,
                            chat_id=task.chat_id,
                            sender_id=candidate.sender_id,
                            sender_name=candidate.sender_name,
                            
                            # 直接使用微信昵称，不从AI结果中提取
                            nickname=candidate.sender_name,  # 直接使用发言人微信昵称
                            industry=profile_data.get('industry'),
                            personal_intro=profile_data.get('personal_intro'),
                            location=profile_data.get('location'),
                            
                            # 服务和需求
                            can_provide=profile_data.get('can_provide', []),
                            need_help=profile_data.get('need_help', []),
                            
                            # 标签
                            tags=profile_data.get('tags', []),
                            
                            # 元数据
                            original_content=candidate.content,
                            extraction_timestamp=datetime.now(),
                            ai_request_id=extraction_result.get('request_id', ''),
                            confidence_score=candidate.confidence_score
                        )
                        
                        profiles.append(profile)
                        task.extracted_profiles += 1
                        candidate.status = IntroductionStatus.EXTRACTED
                        
                        logger.info(f"提取用户画像成功 [{i+1}/{len(candidates)}]: "
                                  f"{profile.nickname or candidate.sender_name} - {profile.industry or '未知行业'}")
                    else:
                        logger.warning(f"提取用户画像失败 [{i+1}/{len(candidates)}] {candidate.sender_name}: "
                                     f"{extraction_result.get('error', '未知错误')}")
                        candidate.status = IntroductionStatus.EXTRACTION_FAILED
                    
                    # 更新进度
                    self._notify_progress('extraction_progress', {
                        'task_id': task.id,
                        'processed': i + 1,
                        'total': len(candidates),
                        'extracted': len(profiles)
                    })
                    
                except Exception as e:
                    logger.error(f"处理提取结果失败 [候选人{i+1}]: {e}")
                    candidate.status = IntroductionStatus.EXTRACTION_FAILED
                    continue
            
        except asyncio.CancelledError:
            logger.info("AI提取过程被取消")
            raise
        except Exception as e:
            logger.error(f"批量用户画像提取失败: {e}")
            task.error_messages.append(f"画像提取失败: {str(e)}")
            raise
        
        logger.info(f"批量用户画像提取完成，成功提取 {len(profiles)} 个用户画像")
        return profiles
    
    async def _sync_to_feishu(self, task: AnalysisTask, profiles: List[UserProfile]):
        """同步到飞书多维表格"""
        if not profiles:
            logger.info("没有用户画像需要同步")
            return
        
        # 检查飞书配置
        feishu_config = self.config.feishu
        if not feishu_config.app_id or not feishu_config.app_secret:
            logger.warning("飞书配置不完整，跳过同步到飞书")
            logger.warning(f"app_id: {'已配置' if feishu_config.app_id else '未配置'}")
            logger.warning(f"app_secret: {'已配置' if feishu_config.app_secret else '未配置'}")
            return
        
        if not feishu_config.app_token or not feishu_config.table_id:
            logger.warning("飞书表格配置不完整，跳过同步到飞书")
            logger.warning(f"app_token: {'已配置' if feishu_config.app_token else '未配置'}")
            logger.warning(f"table_id: {'已配置' if feishu_config.table_id else '未配置'}")
            return
        
        try:
            logger.info(f"开始同步 {len(profiles)} 个用户画像到飞书")
            
            async with FeishuClient() as feishu:
                # 首先获取表格字段映射
                logger.info("获取飞书表格字段映射...")
                field_mapping = await feishu.get_table_fields(
                    feishu_config.app_token, 
                    feishu_config.table_id
                )
                
                if not field_mapping:
                    logger.warning("无法获取飞书表格字段映射，将使用直接字段名映射")
                    field_mapping = {}
                
                synced_count = 0
                failed_count = 0
                
                for i, profile in enumerate(profiles):
                    try:
                        logger.debug(f"正在同步用户画像: {profile.nickname or '未知'}")
                        
                        # 调用正确的方法名和参数
                        record_id = await feishu.add_record(
                            app_token=feishu_config.app_token,
                            table_id=feishu_config.table_id,
                            profile=profile,
                            field_mapping=field_mapping
                        )
                        
                        if record_id:
                            synced_count += 1
                            task.synced_to_feishu += 1
                            logger.info(f"用户画像同步成功: {profile.nickname or '未知'} -> 记录ID: {record_id}")
                        else:
                            failed_count += 1
                            logger.warning(f"用户画像同步失败: {profile.nickname or '未知'}")
                        
                        # 更新进度
                        self._notify_progress('sync_progress', {
                            'task_id': task.id,
                            'processed': i + 1,
                            'total': len(profiles),
                            'synced': synced_count,
                            'failed': failed_count
                        })
                        
                        # 添加延迟避免API限流
                        await asyncio.sleep(0.2)
                        
                    except Exception as e:
                        failed_count += 1
                        logger.error(f"同步用户画像失败 [{profile.nickname or '未知'}]: {e}")
                        continue
                
                logger.info(f"飞书同步完成，成功: {synced_count}, 失败: {failed_count}")
                
                # 记录同步结果到任务
                if synced_count > 0:
                    task.error_messages.append(f"成功同步 {synced_count} 个用户画像到飞书")
                if failed_count > 0:
                    task.error_messages.append(f"同步失败 {failed_count} 个用户画像")
                
        except Exception as e:
            error_msg = f"飞书同步失败: {str(e)}"
            logger.error(error_msg)
            task.error_messages.append(error_msg)
            
            # 不抛出异常，避免影响整个分析流程
            # raise
    
    def _convert_profile_to_feishu_record(self, profile: UserProfile) -> Dict[str, Any]:
        """将用户画像转换为飞书记录格式"""
        return {
            "昵称": profile.nickname or "",
            "微信发送者": profile.sender_name or "",
            "行业": profile.industry or "",
            "个人介绍": profile.personal_intro or "",
            "地区": profile.location or "",
            "能提供的服务": ", ".join(profile.can_provide) if profile.can_provide else "",
            "需要的帮助": ", ".join(profile.need_help) if profile.need_help else "",
            "标签": ", ".join(profile.tags) if profile.tags else "",
            "原始内容": profile.original_content[:500] + "..." if len(profile.original_content) > 500 else profile.original_content,
            "置信度": profile.confidence_score,
            "提取时间": profile.extraction_timestamp.strftime("%Y-%m-%d %H:%M:%S"),
            "AI请求ID": profile.ai_request_id or ""
        }
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        if self.current_task and self.current_task.id == task_id:
            return {
                'id': self.current_task.id,
                'status': self.current_task.status,
                'chat_id': self.current_task.chat_id,
                'start_time': self.current_task.start_time.isoformat(),
                'end_time': self.current_task.end_time.isoformat() if self.current_task.end_time else None,
                'total_messages': self.current_task.total_messages,
                'found_introductions': self.current_task.found_introductions,
                'extracted_profiles': self.current_task.extracted_profiles,
                'synced_to_feishu': self.current_task.synced_to_feishu,
                'error_messages': self.current_task.error_messages,
                'ai_stats': self.deepseek_client.get_analysis_stats()
            }
        return None
    
    def cancel_analysis(self) -> bool:
        """取消当前分析"""
        if self.app_state.is_analyzing and self.current_task:
            self.current_task.status = "cancelled"
            self.app_state.is_analyzing = False
            
            # 设置取消事件
            self._cancel_event.set()
            
            logger.info(f"分析任务已取消: {self.current_task.id}")
            
            self._notify_progress('analysis_cancelled', {
                'task_id': self.current_task.id,
                'ai_stats': self.deepseek_client.get_analysis_stats()
            })
            return True
        return False
    
    def _check_cancelled(self):
        """检查是否已取消"""
        if self._cancel_event.is_set():
            raise asyncio.CancelledError("分析任务已被用户取消")
    
    def get_app_state(self) -> AppState:
        """获取应用状态"""
        return self.app_state
    
    def get_ai_analysis_log(self) -> List[Dict[str, Any]]:
        """获取AI分析日志"""
        return self.deepseek_client.get_analysis_log()
    
    def get_ai_analysis_stats(self) -> Dict[str, Any]:
        """获取AI分析统计信息"""
        return self.deepseek_client.get_analysis_stats()
    
    def switch_deepseek_client(self, use_openai_like: bool) -> bool:
        """
        切换DeepSeek客户端类型
        
        Args:
            use_openai_like: True使用OpenAILike，False使用DeepSeek官方集成
            
        Returns:
            是否切换成功
        """
        if self.app_state.is_analyzing:
            logger.warning("分析过程中无法切换客户端类型")
            return False
        
        try:
            logger.info(f"切换DeepSeek客户端: {'OpenAILike' if use_openai_like else 'DeepSeek官方集成'}")
            self.deepseek_client = DeepSeekClient(use_openai_like=use_openai_like)
            
            # 更新配置
            self.config.deepseek.use_openai_like = use_openai_like
            
            logger.info("DeepSeek客户端切换成功")
            return True
            
        except Exception as e:
            logger.error(f"切换DeepSeek客户端失败: {e}")
            return False
    
    def get_deepseek_client_info(self) -> Dict[str, Any]:
        """获取当前DeepSeek客户端信息"""
        return {
            'type': 'OpenAILike' if self.deepseek_client.use_openai_like else 'DeepSeek官方集成',
            'use_openai_like': self.deepseek_client.use_openai_like,
            'model': self.config.deepseek.model,
            'base_url': self.config.deepseek.base_url,
            'is_available': True  # 可以添加连接检查
        } 