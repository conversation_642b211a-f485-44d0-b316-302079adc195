"""
核心分析服务
协调整个用户画像分析流程，包括消息获取、自我介绍检测、画像提取和数据同步
"""

import asyncio
import uuid
from typing import List, Dict, Any, Optional, Callable
import logging
from datetime import datetime, timedelta

from ..models import (
    AnalysisTask, UserProfile, IntroductionCandidate, 
    IntroductionStatus, ChatGroup, Message, AppState
)
from ..config import get_config
from .chatlog_client import ChatlogClient
from .introduction_detector import IntroductionDetector
from .profile_extractor import ProfileExtractor
from .feishu_client import FeishuClient

logger = logging.getLogger(__name__)

class AnalysisService:
    """核心分析服务"""
    
    def __init__(self):
        self.config = get_config()
        self.app_state = AppState()
        
        # 初始化各个服务
        self.chatlog_client = None
        self.introduction_detector = IntroductionDetector()
        self.profile_extractor = ProfileExtractor()
        self.feishu_client = None
        
        # 进度回调函数
        self.progress_callback: Optional[Callable[[str, Dict[str, Any]], None]] = None
        
        # 当前分析任务
        self.current_task: Optional[AnalysisTask] = None
        
    def set_progress_callback(self, callback: Callable[[str, Dict[str, Any]], None]):
        """设置进度回调函数"""
        self.progress_callback = callback
    
    def _notify_progress(self, event: str, data: Dict[str, Any]):
        """通知进度更新"""
        if self.progress_callback:
            try:
                self.progress_callback(event, data)
            except Exception as e:
                logger.error(f"进度回调函数执行失败: {e}")
    
    async def test_api_connections(self) -> Dict[str, bool]:
        """测试所有API连接"""
        results = {}
        
        try:
            # 测试Chatlog连接
            async with ChatlogClient() as chatlog:
                self.app_state.update_api_status('chatlog', 'testing')
                chatlog_ok = await chatlog.test_connection()
                results['chatlog'] = chatlog_ok
                self.app_state.update_api_status('chatlog', 'connected' if chatlog_ok else 'failed')
        except Exception as e:
            logger.error(f"测试Chatlog连接失败: {e}")
            results['chatlog'] = False
            self.app_state.update_api_status('chatlog', 'failed')
        
        try:
            # 测试DeepSeek连接
            from .deepseek_client import DeepSeekClient
            deepseek = DeepSeekClient()
            self.app_state.update_api_status('deepseek', 'testing')
            deepseek_ok = await deepseek.test_connection()
            results['deepseek'] = deepseek_ok
            self.app_state.update_api_status('deepseek', 'connected' if deepseek_ok else 'failed')
        except Exception as e:
            logger.error(f"测试DeepSeek连接失败: {e}")
            results['deepseek'] = False
            self.app_state.update_api_status('deepseek', 'failed')
        
        try:
            # 测试Feishu连接
            async with FeishuClient() as feishu:
                self.app_state.update_api_status('feishu', 'testing')
                feishu_ok = await feishu.test_connection()
                results['feishu'] = feishu_ok
                self.app_state.update_api_status('feishu', 'connected' if feishu_ok else 'failed')
        except Exception as e:
            logger.error(f"测试Feishu连接失败: {e}")
            results['feishu'] = False
            self.app_state.update_api_status('feishu', 'failed')
        
        self._notify_progress('api_test_completed', results)
        return results
    
    async def get_chat_groups(self) -> List[ChatGroup]:
        """获取所有群组列表"""
        try:
            async with ChatlogClient() as chatlog:
                groups = await chatlog.get_chat_groups()
                self._notify_progress('groups_loaded', {'count': len(groups)})
                return groups
        except Exception as e:
            logger.error(f"获取群组列表失败: {e}")
            self._notify_progress('groups_load_failed', {'error': str(e)})
            return []
    
    async def start_analysis(
        self, 
        chat_id: str, 
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> AnalysisTask:
        """
        开始分析指定群组
        
        Args:
            chat_id: 群组ID
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            分析任务对象
        """
        # 检查是否已有任务在运行
        if self.app_state.is_analyzing:
            raise RuntimeError("已有分析任务在运行中")
        
        # 创建分析任务
        task = AnalysisTask(
            id=str(uuid.uuid4()),
            chat_id=chat_id,
            start_time=datetime.now()
        )
        
        self.current_task = task
        self.app_state.is_analyzing = True
        self.app_state.current_task = task
        self.app_state.current_chat_id = chat_id
        
        logger.info(f"开始分析群组 {chat_id}，任务ID: {task.id}")
        
        try:
            # 执行分析流程
            await self._run_analysis_pipeline(task, start_time, end_time)
            
        except Exception as e:
            logger.error(f"分析任务失败: {e}")
            task.status = "failed"
            task.error_messages.append(str(e))
            
        finally:
            task.end_time = datetime.now()
            self.app_state.is_analyzing = False
            
        self._notify_progress('analysis_completed', {
            'task_id': task.id,
            'status': task.status,
            'duration': (task.end_time - task.start_time).total_seconds(),
            'results': {
                'total_messages': task.total_messages,
                'found_introductions': task.found_introductions,
                'extracted_profiles': task.extracted_profiles,
                'synced_to_feishu': task.synced_to_feishu
            }
        })
        
        return task
    
    async def _run_analysis_pipeline(
        self, 
        task: AnalysisTask, 
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ):
        """执行分析流水线"""
        
        # 第一步：获取群组消息
        self._notify_progress('step_started', {'step': 'fetch_messages', 'task_id': task.id})
        messages = await self._fetch_messages(task, start_time, end_time)
        
        if not messages:
            task.status = "completed"
            logger.warning("未获取到任何消息")
            return
        
        # 第二步：检测自我介绍
        self._notify_progress('step_started', {'step': 'detect_introductions', 'task_id': task.id})
        candidates = await self._detect_introductions(task, messages)
        
        if not candidates:
            task.status = "completed"
            logger.info("未发现自我介绍")
            return
        
        # 第三步：提取用户画像
        self._notify_progress('step_started', {'step': 'extract_profiles', 'task_id': task.id})
        profiles = await self._extract_profiles(task, candidates)
        
        if not profiles:
            task.status = "completed"
            logger.info("未提取到用户画像")
            return
        
        # 第四步：同步到飞书
        self._notify_progress('step_started', {'step': 'sync_to_feishu', 'task_id': task.id})
        await self._sync_to_feishu(task, profiles)
        
        task.status = "completed"
        logger.info(f"分析任务完成: {task.id}")
    
    async def _fetch_messages(
        self, 
        task: AnalysisTask, 
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> List[Message]:
        """获取群组消息"""
        try:
            async with ChatlogClient() as chatlog:
                # 批量获取消息
                messages = await chatlog.get_messages_in_batches(
                    chat_id=task.chat_id,
                    start_time=start_time,
                    end_time=end_time,
                    batch_size=self.config.analysis.batch_size
                )
                
                task.total_messages = len(messages)
                
                self._notify_progress('messages_fetched', {
                    'task_id': task.id,
                    'count': len(messages),
                    'time_range': {
                        'start': start_time.isoformat() if start_time else None,
                        'end': end_time.isoformat() if end_time else None
                    }
                })
                
                return messages
                
        except Exception as e:
            task.error_messages.append(f"获取消息失败: {str(e)}")
            raise
    
    async def _detect_introductions(
        self, 
        task: AnalysisTask, 
        messages: List[Message]
    ) -> List[IntroductionCandidate]:
        """检测自我介绍"""
        try:
            candidates = await self.introduction_detector.detect_and_confirm(messages)
            
            # 更新任务状态
            task.found_introductions = sum(1 for c in candidates if c.is_introduction)
            task.processed_messages = len(messages)
            
            # 获取检测统计
            stats = self.introduction_detector.get_detection_stats(candidates)
            
            self._notify_progress('introductions_detected', {
                'task_id': task.id,
                'candidates': len(candidates),
                'confirmed': stats['confirmed_introductions'],
                'stats': stats
            })
            
            return candidates
            
        except Exception as e:
            task.error_messages.append(f"检测自我介绍失败: {str(e)}")
            raise
    
    async def _extract_profiles(
        self, 
        task: AnalysisTask, 
        candidates: List[IntroductionCandidate]
    ) -> List[UserProfile]:
        """提取用户画像"""
        try:
            profiles = await self.profile_extractor.extract_profiles(candidates)
            
            # 过滤高质量画像
            high_quality_profiles = self.profile_extractor.filter_high_quality_profiles(profiles)
            
            # 更新任务状态
            task.extracted_profiles = len(high_quality_profiles)
            
            # 获取提取统计
            stats = self.profile_extractor.get_extraction_stats(profiles)
            
            self._notify_progress('profiles_extracted', {
                'task_id': task.id,
                'total': len(profiles),
                'high_quality': len(high_quality_profiles),
                'stats': stats
            })
            
            return high_quality_profiles
            
        except Exception as e:
            task.error_messages.append(f"提取用户画像失败: {str(e)}")
            raise
    
    async def _sync_to_feishu(self, task: AnalysisTask, profiles: List[UserProfile]):
        """同步用户画像到飞书"""
        try:
            # 检查是否配置了飞书
            if not self.config.feishu.app_id or not self.config.feishu.app_secret:
                logger.warning("未配置飞书API，跳过同步")
                return
            
            synced_count = 0
            failed_count = 0
            
            async with FeishuClient() as feishu:
                # 这里需要用户配置飞书表格信息
                # 实际使用时需要通过UI配置app_token和table_id
                app_token = "your_app_token"  # 需要配置
                table_id = "your_table_id"    # 需要配置
                
                # 获取字段映射
                field_mapping = await feishu.get_table_fields(app_token, table_id)
                
                # 批量同步
                for profile in profiles:
                    try:
                        record_id = await feishu.add_record(app_token, table_id, profile, field_mapping)
                        if record_id:
                            profile.feishu_record_id = record_id
                            synced_count += 1
                        else:
                            failed_count += 1
                    except Exception as e:
                        logger.error(f"同步用户画像失败: {profile.id}, {e}")
                        failed_count += 1
                    
                    # 添加延迟避免API限制
                    await asyncio.sleep(0.2)
            
            task.synced_to_feishu = synced_count
            self.app_state.last_sync_time = datetime.now()
            
            self._notify_progress('sync_completed', {
                'task_id': task.id,
                'synced': synced_count,
                'failed': failed_count,
                'total': len(profiles)
            })
            
        except Exception as e:
            task.error_messages.append(f"同步到飞书失败: {str(e)}")
            logger.error(f"同步到飞书失败: {e}")
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        if self.current_task and self.current_task.id == task_id:
            return {
                'id': self.current_task.id,
                'status': self.current_task.status,
                'progress': self.current_task.progress,
                'total_messages': self.current_task.total_messages,
                'processed_messages': self.current_task.processed_messages,
                'found_introductions': self.current_task.found_introductions,
                'extracted_profiles': self.current_task.extracted_profiles,
                'synced_to_feishu': self.current_task.synced_to_feishu,
                'errors': self.current_task.error_messages,
                'start_time': self.current_task.start_time.isoformat(),
                'end_time': self.current_task.end_time.isoformat() if self.current_task.end_time else None
            }
        return None
    
    def cancel_analysis(self) -> bool:
        """取消当前分析任务"""
        if self.app_state.is_analyzing and self.current_task:
            self.current_task.status = "cancelled"
            self.current_task.end_time = datetime.now()
            self.app_state.is_analyzing = False
            
            self._notify_progress('analysis_cancelled', {
                'task_id': self.current_task.id
            })
            
            logger.info(f"用户取消了分析任务: {self.current_task.id}")
            return True
        return False
    
    def get_app_state(self) -> AppState:
        """获取应用状态"""
        return self.app_state 