"""
主窗口界面
包含配置、群组管理、分析和结果查看等功能标签页
"""

import asyncio
import threading
import tkinter as tk
from tkinter import messagebox
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import logging
from typing import Dict, Any

from ..services.analysis_service import AnalysisService
from .config_tab import ConfigTab
from .groups_tab import GroupsTab
from .analysis_tab import AnalysisTab
from .results_tab import ResultsTab
from ..config import get_config, save_config

logger = logging.getLogger(__name__)

class MainWindow:
    """主窗口类"""
    
    def __init__(self):
        self.root = ttk.Window(themename="lumen")
        self.analysis_service = None
        
        # 创建界面
        self._setup_window()
        self._create_menu()
        
        # 初始化服务（必须在创建标签页之前）
        self._init_analysis_service()
        
        self._create_tabs()
        self._create_status_bar()
        
        # 检查首次启动
        self._check_first_launch()
        
        logger.info("主窗口初始化完成")
    
    def _check_first_launch(self):
        """检查是否为首次启动，显示欢迎向导"""
        try:
            config = get_config()
            
            # 检查关键配置是否为空
            is_first_launch = (
                not config.deepseek.api_key.strip() and
                not config.chatlog.base_url.strip()
            )
            
            if is_first_launch:
                # 延迟显示欢迎向导，确保界面完全加载
                self.root.after(1000, self._show_welcome_guide)
                
        except Exception as e:
            logger.warning(f"检查首次启动状态失败: {e}")
    
    def _show_welcome_guide(self):
        """显示欢迎向导"""
        welcome_window = ttk.Toplevel(self.root)
        welcome_window.title("欢迎使用微信群自我介绍分析工具")
        welcome_window.geometry("650x550")
        welcome_window.transient(self.root)
        welcome_window.grab_set()
        
        # 设置窗口居中
        welcome_window.geometry("+{}+{}".format(
            (welcome_window.winfo_screenwidth() // 2) - 325,
            (welcome_window.winfo_screenheight() // 2) - 275
        ))
        
        # 创建内容
        main_frame = ttk.Frame(welcome_window, padding=25)
        main_frame.pack(fill=BOTH, expand=True)
        
        # 欢迎标题
        title_label = ttk.Label(
            main_frame,
            text="🎉 欢迎使用微信群自我介绍分析工具",
            font=("微软雅黑", 18, "bold"),
            bootstyle="primary"
        )
        title_label.pack(pady=(0, 25))
        
        # 功能介绍
        intro_frame = ttk.LabelFrame(main_frame, text="🚀 主要功能", padding=15)
        intro_frame.pack(fill=X, pady=(0, 20))
        
        features = [
            "🤖 智能识别群组中的自我介绍消息",
            "📊 自动提取用户画像信息（职业、技能、需求等）",
            "📋 支持导出分析结果到Excel和JSON格式",
            "📱 可选择同步结果到飞书多维表格",
            "📈 提供详细的分析进度和统计信息"
        ]
        
        for feature in features:
            feature_label = ttk.Label(
                intro_frame,
                text=feature,
                font=("微软雅黑", 11),
                justify=LEFT
            )
            feature_label.pack(anchor=W, pady=3)
        
        # 配置提示
        config_frame = ttk.LabelFrame(main_frame, text="⚙️ 开始前的准备", padding=15)
        config_frame.pack(fill=X, pady=(0, 20))
        
        config_text = """为了正常使用本工具，您需要：

1️⃣ 配置AI分析服务
   • 获取DeepSeek API密钥（推荐）或其他AI服务密钥
   • 用于智能识别和分析自我介绍内容

2️⃣ 配置聊天记录服务
   • 安装并运行微信聊天记录导出工具
   • 提供群组消息数据来源

3️⃣ 配置飞书同步（可选）
   • 创建飞书应用获取相关凭证
   • 用于将分析结果同步到飞书多维表格

💡 不用担心，我们提供了详细的配置向导来帮助您完成设置！"""
        
        config_label = ttk.Label(
            config_frame,
            text=config_text,
            font=("微软雅黑", 10),
            justify=LEFT
        )
        config_label.pack(anchor=W)
        
        # 按钮区域
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=X, pady=(20, 0))
        
        # 开始配置按钮
        start_config_btn = ttk.Button(
            btn_frame,
            text="🔧 开始配置向导",
            command=lambda: self._start_welcome_config(welcome_window),
            bootstyle="success",
            style="large.TButton"
        )
        start_config_btn.pack(side=LEFT, padx=(0, 15))
        
        # 稍后配置按钮
        later_btn = ttk.Button(
            btn_frame,
            text="稍后配置",
            command=welcome_window.destroy,
            bootstyle="secondary-outline"
        )
        later_btn.pack(side=LEFT, padx=(0, 15))
        
        # 查看教程按钮
        tutorial_btn = ttk.Button(
            btn_frame,
            text="📖 查看使用教程",
            command=lambda: self._show_tutorial_from_welcome(welcome_window),
            bootstyle="info-outline"
        )
        tutorial_btn.pack(side=LEFT)
        
        # 不再显示选项
        dont_show_frame = ttk.Frame(main_frame)
        dont_show_frame.pack(fill=X, pady=(15, 0))
        
        self.dont_show_var = tk.BooleanVar()
        dont_show_check = ttk.Checkbutton(
            dont_show_frame,
            text="不再显示此欢迎页面",
            variable=self.dont_show_var
        )
        dont_show_check.pack(anchor=W)
    
    def _start_welcome_config(self, welcome_window):
        """从欢迎页面启动配置向导"""
        welcome_window.destroy()
        
        # 切换到配置标签页
        self.notebook.select(3)  # 假设配置是第4个标签页
        
        # 启动配置向导
        if hasattr(self.config_tab, '_start_config_wizard'):
            self.config_tab._start_config_wizard()
    
    def _show_tutorial_from_welcome(self, welcome_window):
        """从欢迎页面显示教程"""
        welcome_window.destroy()
        
        # 切换到配置标签页
        self.notebook.select(3)
        
        # 显示教程
        if hasattr(self.config_tab, '_show_tutorial'):
            self.config_tab._show_tutorial()
    
    def _save_welcome_preference(self):
        """保存欢迎页面偏好设置"""
        if hasattr(self, 'dont_show_var') and self.dont_show_var.get():
            try:
                # 在配置中标记不再显示欢迎页面
                config = get_config()
                if not hasattr(config, 'ui_preferences'):
                    setattr(config, 'ui_preferences', {})
                config.ui_preferences['show_welcome'] = False
                save_config(config)
            except Exception as e:
                logger.warning(f"保存欢迎页面偏好失败: {e}")
    
    def _setup_window(self):
        """设置窗口属性"""
        try:
            config = get_config()
            
            # 设置窗口标题和大小
            self.root.title("微信群自我介绍分析工具 v1.0")
            self.root.geometry(f"{config.ui.window_width}x{config.ui.window_height}")
            self.root.minsize(config.ui.min_width, config.ui.min_height)
            
            # 设置窗口图标（如果有的话）
            try:
                # self.root.iconbitmap("app/assets/icon.ico")  # 如果有图标文件
                pass
            except:
                pass
            
            # 设置窗口居中
            self.root.place_window_center()
            
        except Exception as e:
            logger.warning(f"设置窗口属性失败: {e}")
            # 使用默认设置
            self.root.title("微信群自我介绍分析工具")
            self.root.geometry("1200x800")
            self.root.minsize(1000, 600)
    
    def _create_tabs(self):
        """创建标签页"""
        # 创建Notebook
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=BOTH, expand=True, padx=10, pady=5)
        
        # 创建各个标签页
        self.groups_tab = GroupsTab(self.notebook, self.analysis_service)
        self.notebook.add(self.groups_tab.frame, text="📱 群组管理")
        
        self.analysis_tab = AnalysisTab(self.notebook, self.analysis_service, self.groups_tab)
        self.notebook.add(self.analysis_tab.frame, text="🚀 分析任务")
        
        self.results_tab = ResultsTab(self.notebook, self.analysis_service)
        self.notebook.add(self.results_tab.frame, text="📊 分析结果")
        
        self.config_tab = ConfigTab(self.notebook, self.analysis_service)
        self.notebook.add(self.config_tab.frame, text="⚙️ 配置设置")
        
        # 绑定标签页切换事件
        self.notebook.bind("<<NotebookTabChanged>>", self.on_tab_changed)
    
    def _create_status_bar(self):
        """创建状态栏"""
        status_frame = ttk.Frame(self.root)
        status_frame.pack(fill=X, side=BOTTOM, padx=5, pady=2)
        
        # 状态信息
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(status_frame, textvariable=self.status_var)
        status_label.pack(side=LEFT)
        
        # 分隔符
        ttk.Separator(status_frame, orient=VERTICAL).pack(side=LEFT, fill=Y, padx=10)
        
        # API连接状态
        self.api_status_frame = ttk.Frame(status_frame)
        self.api_status_frame.pack(side=LEFT)
        
        # AI服务状态
        self.ai_status_var = tk.StringVar(value="🔴 AI服务未连接")
        ai_status_label = ttk.Label(self.api_status_frame, textvariable=self.ai_status_var)
        ai_status_label.pack(side=LEFT, padx=(0, 10))
        
        # 聊天记录服务状态
        self.chat_status_var = tk.StringVar(value="🔴 聊天服务未连接")
        chat_status_label = ttk.Label(self.api_status_frame, textvariable=self.chat_status_var)
        chat_status_label.pack(side=LEFT, padx=(0, 10))
        
        # 版本信息
        version_label = ttk.Label(status_frame, text="v1.0.0")
        version_label.pack(side=RIGHT)
    
    def _create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="导出配置...", command=self.export_config)
        file_menu.add_command(label="导入配置...", command=self.import_config)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="测试API连接", command=self.test_api_connections)
        tools_menu.add_command(label="清理日志文件", command=self.clean_logs)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用教程", command=self.show_help)
        help_menu.add_command(label="配置向导", command=self.show_config_wizard)
        help_menu.add_separator()
        help_menu.add_command(label="关于", command=self.show_about)
    
    def _init_analysis_service(self):
        """初始化分析服务"""
        try:
            from ..services.analysis_service import AnalysisService
            self.analysis_service = AnalysisService()
            # 设置进度回调
            self.analysis_service.set_progress_callback(self.on_progress_update)
            logger.info("分析服务初始化完成")
        except Exception as e:
            logger.error(f"初始化分析服务失败: {e}")
            # 创建一个模拟的服务对象以避免 None 错误
            class MockAnalysisService:
                def __init__(self):
                    self.config = None
                
                def set_progress_callback(self, callback):
                    pass
                
                def get_deepseek_client_info(self):
                    return {
                        'type': '未知',
                        'use_openai_like': False,
                        'model': '未配置',
                        'base_url': '未配置',
                        'is_available': False
                    }
                
                def get_app_state(self):
                    class MockState:
                        def __init__(self):
                            self.is_analyzing = False
                    return MockState()
                
                def switch_deepseek_client(self, use_openai_like):
                    return False
            
            self.analysis_service = MockAnalysisService()
            
            # 显示错误信息，但不阻止程序启动
            self.root.after(2000, lambda: messagebox.showerror(
                "服务初始化失败", 
                f"分析服务初始化失败:\n{str(e)}\n\n程序将以受限模式运行。\n请检查配置后重启程序。"
            ))
    
    def on_tab_changed(self, event):
        """标签页切换事件"""
        try:
            selected_tab = event.widget.tab('current')['text']
            logger.debug(f"切换到标签页: {selected_tab}")
            
            # 根据标签页执行相应操作
            if "群组管理" in selected_tab:
                # 刷新群组列表
                if hasattr(self.groups_tab, 'refresh_groups'):
                    self.groups_tab.refresh_groups()
            elif "分析结果" in selected_tab:
                # 刷新结果数据
                if hasattr(self.results_tab, 'refresh_results'):
                    self.results_tab.refresh_results()
                    
        except Exception as e:
            logger.warning(f"处理标签页切换事件失败: {e}")
    
    def on_progress_update(self, event: str, data: Dict[str, Any]):
        """处理进度更新事件"""
        # 在主线程中执行UI更新
        self.root.after(0, self._handle_progress_update, event, data)
    
    def _handle_progress_update(self, event: str, data: Dict[str, Any]):
        """在主线程中处理进度更新"""
        try:
            # 更新状态栏
            if event == "analysis_started":
                self.status_var.set("🚀 分析任务已启动")
            elif event == "analysis_completed":
                self.status_var.set("✅ 分析任务已完成")
            elif event == "analysis_failed":
                self.status_var.set("❌ 分析任务失败")
            
            # 转发给各个标签页
            if hasattr(self.analysis_tab, 'on_progress_update'):
                self.analysis_tab.on_progress_update(event, data)
            if hasattr(self.results_tab, 'on_progress_update'):
                self.results_tab.on_progress_update(event, data)
                
        except Exception as e:
            logger.error(f"处理进度更新失败: {e}")
    
    def test_api_connections(self):
        """测试API连接"""
        try:
            self.status_var.set("正在测试API连接...")
            
            # 这里应该调用异步方法，但为了简化，使用线程
            def test_async():
                try:
                    # 模拟连接测试
                    import time
                    time.sleep(1)
                    
                    # 更新状态
                    self.root.after(0, lambda: self.status_var.set("API连接测试完成"))
                    self.root.after(0, lambda: messagebox.showinfo("测试完成", "API连接测试已完成，请查看状态栏"))
                    
                except Exception as e:
                    self.root.after(0, lambda: messagebox.showerror("测试失败", f"API连接测试失败:\n{str(e)}"))
            
            threading.Thread(target=test_async, daemon=True).start()
            
        except Exception as e:
            logger.error(f"测试API连接失败: {e}")
            messagebox.showerror("错误", f"无法启动连接测试:\n{str(e)}")
    
    def export_config(self):
        """导出配置"""
        try:
            from tkinter import filedialog
            import json
            
            filename = filedialog.asksaveasfilename(
                title="导出配置文件",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            
            if filename:
                config = get_config()
                # 移除敏感信息
                config_dict = {
                    "chatlog": {
                        "base_url": config.chatlog.base_url,
                        "timeout": config.chatlog.timeout,
                        "max_retries": config.chatlog.max_retries
                    },
                    "deepseek": {
                        "base_url": config.deepseek.base_url,
                        "model": config.deepseek.model,
                        "max_tokens": config.deepseek.max_tokens,
                        "temperature": config.deepseek.temperature,
                        "use_openai_like": config.deepseek.use_openai_like
                    },
                    "feishu": {
                        "base_url": config.feishu.base_url
                    },
                    "analysis": {
                        "batch_size": config.analysis.batch_size,
                        "max_workers": config.analysis.max_workers,
                        "confidence_threshold": config.analysis.confidence_threshold,
                        "auto_detect": config.analysis.auto_detect
                    },
                    "ui": {
                        "theme": config.ui.theme,
                        "window_width": config.ui.window_width,
                        "window_height": config.ui.window_height
                    }
                }
                
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(config_dict, f, ensure_ascii=False, indent=2)
                
                messagebox.showinfo("导出成功", f"配置已导出到:\n{filename}\n\n注意：敏感信息（如API密钥）不会被导出")
                
        except Exception as e:
            logger.error(f"导出配置失败: {e}")
            messagebox.showerror("导出失败", f"导出配置失败:\n{str(e)}")
    
    def import_config(self):
        """导入配置"""
        try:
            from tkinter import filedialog
            import json
            
            filename = filedialog.askopenfilename(
                title="导入配置文件",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            
            if filename:
                with open(filename, 'r', encoding='utf-8') as f:
                    imported_config = json.load(f)
                
                # 询问是否确认导入
                result = messagebox.askyesno(
                    "确认导入", 
                    "导入配置将覆盖当前设置（不包括API密钥等敏感信息）。\n确定要继续吗？"
                )
                
                if result:
                    # 更新配置
                    config = get_config()
                    
                    if 'chatlog' in imported_config:
                        config.chatlog.base_url = imported_config['chatlog'].get('base_url', config.chatlog.base_url)
                        config.chatlog.timeout = imported_config['chatlog'].get('timeout', config.chatlog.timeout)
                        config.chatlog.max_retries = imported_config['chatlog'].get('max_retries', config.chatlog.max_retries)
                    
                    # 其他配置项类似处理...
                    
                    save_config(config)
                    
                    # 重新加载配置界面
                    if hasattr(self.config_tab, '_load_config'):
                        self.config_tab._load_config()
                    
                    messagebox.showinfo("导入成功", "配置导入成功！\n请重新填写API密钥等敏感信息。")
                
        except Exception as e:
            logger.error(f"导入配置失败: {e}")
            messagebox.showerror("导入失败", f"导入配置失败:\n{str(e)}")
    
    def clean_logs(self):
        """清理日志文件"""
        try:
            result = messagebox.askyesno("确认清理", "确定要清理所有日志文件吗？")
            if result:
                import os
                import glob
                
                log_pattern = "app/data/logs/*.log"
                log_files = glob.glob(log_pattern)
                
                cleaned_count = 0
                for log_file in log_files:
                    try:
                        os.remove(log_file)
                        cleaned_count += 1
                    except:
                        pass
                
                messagebox.showinfo("清理完成", f"已清理 {cleaned_count} 个日志文件")
                
        except Exception as e:
            logger.error(f"清理日志失败: {e}")
            messagebox.showerror("清理失败", f"清理日志失败:\n{str(e)}")
    
    def show_help(self):
        """显示帮助"""
        if hasattr(self.config_tab, '_show_tutorial'):
            self.config_tab._show_tutorial()
        else:
            messagebox.showinfo("帮助", "使用教程功能正在开发中...")
    
    def show_config_wizard(self):
        """显示配置向导"""
        # 切换到配置标签页
        self.notebook.select(3)
        
        # 启动配置向导
        if hasattr(self.config_tab, '_start_config_wizard'):
            self.config_tab._start_config_wizard()
        else:
            messagebox.showinfo("配置向导", "配置向导功能正在开发中...")
    
    def show_about(self):
        """显示关于信息"""
        about_text = """微信群自我介绍分析工具 v1.0

🚀 主要功能：
• 智能识别群组中的自我介绍消息
• 自动提取用户画像信息
• 支持导出分析结果
• 可选择同步到飞书多维表格

🔧 技术特点：
• 基于AI大模型的智能分析
• 友好的图形界面
• 完整的配置向导
• 详细的进度显示

📞 技术支持：
• 查看使用教程了解详细功能
• 使用配置向导快速开始
• 查看日志文件排查问题

感谢您使用本工具！"""
        
        messagebox.showinfo("关于", about_text)
    
    def run(self):
        """运行应用"""
        try:
            self.root.mainloop()
        except Exception as e:
            logger.error(f"运行应用失败: {e}")
            messagebox.showerror("错误", f"应用运行失败:\n{str(e)}")
        finally:
            # 清理资源
            self._cleanup()
    
    def _cleanup(self):
        """清理资源"""
        try:
            if self.analysis_service:
                # 停止分析服务
                pass
            logger.info("应用资源清理完成")
        except Exception as e:
            logger.warning(f"清理资源失败: {e}") 