"""
配置设置页面
管理系统配置和API设置
"""

import flet as ft
from typing import TYPE_CHECKING
import asyncio

if TYPE_CHECKING:
    from ..main_app import FletMainApp

class SettingsPage:
    """配置设置页面"""
    
    def __init__(self, app: 'FletMainApp'):
        self.app = app
        
        # 配置字段
        self.deepseek_api_key = ""
        self.chatlog_url = "http://localhost:8080"
        self.feishu_app_id = ""
        self.feishu_app_secret = ""
        self.feishu_app_token = ""
        self.feishu_table_id = ""
        
        # UI组件引用
        self.api_key_field = None
        self.chatlog_url_field = None
        self.feishu_fields = {}
        self.test_buttons = {}
    
    def build(self) -> ft.Column:
        """构建设置页面"""
        
        # AI服务配置
        ai_config_section = self._create_ai_config_section()
        
        # Chatlog配置
        chatlog_config_section = self._create_chatlog_config_section()
        
        # 飞书配置
        feishu_config_section = self._create_feishu_config_section()
        
        # 系统配置
        system_config_section = self._create_system_config_section()
        
        # 操作按钮
        action_section = self._create_action_section()
        
        return ft.Column(
            controls=[
                # 配置区域并排显示
                ft.Row(
                    controls=[
                        ft.Column(
                            controls=[
                                ai_config_section,
                                ft.Container(height=20),
                                chatlog_config_section
                            ],
                            expand=1
                        ),
                        ft.Container(width=20),
                        ft.Column(
                            controls=[
                                feishu_config_section,
                                ft.Container(height=20),
                                system_config_section
                            ],
                            expand=1
                        )
                    ],
                    expand=True,
                    alignment=ft.MainAxisAlignment.START,
                    vertical_alignment=ft.CrossAxisAlignment.START
                ),
                
                ft.Container(height=20),
                
                # 操作按钮
                action_section
            ],
            spacing=0,
            expand=True,
            scroll=ft.ScrollMode.AUTO
        )
    
    def _create_ai_config_section(self) -> ft.Container:
        """创建AI服务配置区域"""
        
        # API密钥输入框
        self.api_key_field = ft.TextField(
            label="DeepSeek API密钥",
            hint_text="请输入您的DeepSeek API密钥",
            value=self.deepseek_api_key,
            password=True,
            can_reveal_password=True,
            prefix_icon=ft.icons.KEY,
            on_change=self._on_api_key_change
        )
        
        # 测试连接按钮
        self.test_buttons['deepseek'] = ft.ElevatedButton(
            text="测试连接",
            icon=ft.icons.WIFI_PROTECTED_SETUP,
            on_click=self._on_test_deepseek,
            style=ft.ButtonStyle(
                bgcolor=ft.colors.BLUE,
                color=ft.colors.WHITE
            )
        )
        
        return ft.Container(
            content=ft.Column(
                controls=[
                    ft.Text(
                        "🤖 AI服务配置",
                        size=18,
                        weight=ft.FontWeight.BOLD,
                        color=ft.colors.GREY_800
                    ),
                    ft.Container(height=10),
                    
                    self.api_key_field,
                    
                    ft.Container(height=10),
                    
                    ft.Row(
                        controls=[
                            self.test_buttons['deepseek'],
                            ft.TextButton(
                                text="获取API密钥",
                                icon=ft.icons.OPEN_IN_NEW,
                                on_click=lambda e: self._open_url("https://platform.deepseek.com")
                            )
                        ],
                        spacing=10
                    ),
                    
                    ft.Container(height=15),
                    
                    # 配置说明
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                ft.Text("💡 配置说明", size=14, weight=ft.FontWeight.BOLD, color=ft.colors.BLUE),
                                ft.Text("• 访问 https://platform.deepseek.com 注册账号", size=12, color=ft.colors.GREY_600),
                                ft.Text("• 在API管理页面创建新的API密钥", size=12, color=ft.colors.GREY_600),
                                ft.Text("• 新用户通常有免费额度可以试用", size=12, color=ft.colors.GREY_600),
                                ft.Text("• 分析1000条消息大约消耗0.1-0.5元", size=12, color=ft.colors.GREY_600)
                            ],
                            spacing=5
                        ),
                        padding=ft.padding.all(15),
                        bgcolor=ft.colors.BLUE_50,
                        border_radius=8,
                        border=ft.border.all(1, ft.colors.BLUE_200)
                    )
                ]
            ),
            padding=ft.padding.all(25),
            bgcolor=ft.colors.WHITE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.colors.BLACK12,
                offset=ft.Offset(0, 4)
            )
        )
    
    def _create_chatlog_config_section(self) -> ft.Container:
        """创建Chatlog配置区域"""
        
        # URL输入框
        self.chatlog_url_field = ft.TextField(
            label="Chatlog服务地址",
            hint_text="http://localhost:8080",
            value=self.chatlog_url,
            prefix_icon=ft.icons.LINK,
            on_change=self._on_chatlog_url_change
        )
        
        # 测试连接按钮
        self.test_buttons['chatlog'] = ft.ElevatedButton(
            text="测试连接",
            icon=ft.icons.WIFI_PROTECTED_SETUP,
            on_click=self._on_test_chatlog,
            style=ft.ButtonStyle(
                bgcolor=ft.colors.GREEN,
                color=ft.colors.WHITE
            )
        )
        
        return ft.Container(
            content=ft.Column(
                controls=[
                    ft.Text(
                        "💬 聊天记录服务",
                        size=18,
                        weight=ft.FontWeight.BOLD,
                        color=ft.colors.GREY_800
                    ),
                    ft.Container(height=10),
                    
                    self.chatlog_url_field,
                    
                    ft.Container(height=10),
                    
                    ft.Row(
                        controls=[
                            self.test_buttons['chatlog'],
                            ft.TextButton(
                                text="下载Chatlog",
                                icon=ft.icons.DOWNLOAD,
                                on_click=lambda e: self._open_url("https://github.com/LC044/WeChatMsg")
                            )
                        ],
                        spacing=10
                    ),
                    
                    ft.Container(height=15),
                    
                    # 配置说明
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                ft.Text("💡 配置说明", size=14, weight=ft.FontWeight.BOLD, color=ft.colors.GREEN),
                                ft.Text("• 下载并安装WeChatMsg工具", size=12, color=ft.colors.GREY_600),
                                ft.Text("• 启动工具并开启API服务", size=12, color=ft.colors.GREY_600),
                                ft.Text("• 默认端口为8080，可在工具中修改", size=12, color=ft.colors.GREY_600),
                                ft.Text("• 确保微信已登录且有聊天记录", size=12, color=ft.colors.GREY_600)
                            ],
                            spacing=5
                        ),
                        padding=ft.padding.all(15),
                        bgcolor=ft.colors.GREEN_50,
                        border_radius=8,
                        border=ft.border.all(1, ft.colors.GREEN_200)
                    )
                ]
            ),
            padding=ft.padding.all(25),
            bgcolor=ft.colors.WHITE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.colors.BLACK12,
                offset=ft.Offset(0, 4)
            )
        )
    
    def _create_feishu_config_section(self) -> ft.Container:
        """创建飞书配置区域"""
        
        # 飞书配置字段
        feishu_fields = [
            ("app_id", "应用ID", "请输入飞书应用ID"),
            ("app_secret", "应用密钥", "请输入飞书应用密钥"),
            ("app_token", "表格Token", "请输入多维表格的app_token"),
            ("table_id", "表格ID", "请输入表格ID")
        ]
        
        field_controls = []
        for field_key, label, hint in feishu_fields:
            field = ft.TextField(
                label=label,
                hint_text=hint,
                value=getattr(self, f"feishu_{field_key}"),
                password=(field_key == "app_secret"),
                can_reveal_password=(field_key == "app_secret"),
                prefix_icon=ft.icons.CLOUD if field_key != "app_secret" else ft.icons.LOCK,
                on_change=lambda e, key=field_key: self._on_feishu_field_change(key, e.control.value)
            )
            self.feishu_fields[field_key] = field
            field_controls.append(field)
        
        # 测试连接按钮
        self.test_buttons['feishu'] = ft.ElevatedButton(
            text="测试连接",
            icon=ft.icons.WIFI_PROTECTED_SETUP,
            on_click=self._on_test_feishu,
            style=ft.ButtonStyle(
                bgcolor=ft.colors.PURPLE,
                color=ft.colors.WHITE
            )
        )
        
        return ft.Container(
            content=ft.Column(
                controls=[
                    ft.Text(
                        "📱 飞书多维表格",
                        size=18,
                        weight=ft.FontWeight.BOLD,
                        color=ft.colors.GREY_800
                    ),
                    ft.Container(height=10),

                    *field_controls,

                    ft.Container(height=10),

                    ft.Row(
                        controls=[
                            self.test_buttons['feishu'],
                            ft.TextButton(
                                text="配置教程",
                                icon=ft.icons.HELP_OUTLINE,
                                on_click=self._show_feishu_tutorial
                            )
                        ],
                        spacing=10
                    ),

                    ft.Container(height=15),

                    # 可选标识
                    ft.Container(
                        content=ft.Row(
                            controls=[
                                ft.Icon(ft.icons.INFO_OUTLINE, size=16, color=ft.colors.ORANGE),
                                ft.Text("飞书同步为可选功能，不配置也可正常使用", size=12, color=ft.colors.ORANGE)
                            ],
                            spacing=8
                        ),
                        padding=ft.padding.all(10),
                        bgcolor=ft.colors.ORANGE_50,
                        border_radius=8,
                        border=ft.border.all(1, ft.colors.ORANGE_200)
                    )
                ]
            ),
            padding=ft.padding.all(25),
            bgcolor=ft.colors.WHITE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.colors.BLACK12,
                offset=ft.Offset(0, 4)
            )
        )

    def _create_system_config_section(self) -> ft.Container:
        """创建系统配置区域"""

        # 主题选择
        theme_dropdown = ft.Dropdown(
            label="界面主题",
            hint_text="选择界面主题",
            options=[
                ft.dropdown.Option("light", "浅色主题"),
                ft.dropdown.Option("dark", "深色主题"),
                ft.dropdown.Option("auto", "跟随系统")
            ],
            value="light",
            prefix_icon=ft.icons.PALETTE
        )

        # 语言选择
        language_dropdown = ft.Dropdown(
            label="界面语言",
            hint_text="选择界面语言",
            options=[
                ft.dropdown.Option("zh_CN", "简体中文"),
                ft.dropdown.Option("en_US", "English")
            ],
            value="zh_CN",
            prefix_icon=ft.icons.LANGUAGE
        )

        # 自动保存开关
        auto_save_switch = ft.Switch(
            label="自动保存配置",
            value=True
        )

        # 启动时检查更新
        check_update_switch = ft.Switch(
            label="启动时检查更新",
            value=True
        )

        return ft.Container(
            content=ft.Column(
                controls=[
                    ft.Text(
                        "⚙️ 系统设置",
                        size=18,
                        weight=ft.FontWeight.BOLD,
                        color=ft.colors.GREY_800
                    ),
                    ft.Container(height=10),

                    theme_dropdown,
                    ft.Container(height=10),
                    language_dropdown,
                    ft.Container(height=15),

                    ft.Row(
                        controls=[
                            ft.Text("自动保存配置", size=14, expand=True),
                            auto_save_switch
                        ],
                        alignment=ft.MainAxisAlignment.SPACE_BETWEEN
                    ),

                    ft.Container(height=10),

                    ft.Row(
                        controls=[
                            ft.Text("启动时检查更新", size=14, expand=True),
                            check_update_switch
                        ],
                        alignment=ft.MainAxisAlignment.SPACE_BETWEEN
                    ),

                    ft.Container(height=20),

                    # 系统信息
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                ft.Text("📊 系统信息", size=14, weight=ft.FontWeight.BOLD, color=ft.colors.GREY_700),
                                ft.Text("版本: v2.0.0", size=12, color=ft.colors.GREY_600),
                                ft.Text("Python: 3.11.0", size=12, color=ft.colors.GREY_600),
                                ft.Text("Flet: 0.21.0", size=12, color=ft.colors.GREY_600),
                                ft.Text("构建时间: 2024-12-20", size=12, color=ft.colors.GREY_600)
                            ],
                            spacing=5
                        ),
                        padding=ft.padding.all(15),
                        bgcolor=ft.colors.GREY_50,
                        border_radius=8,
                        border=ft.border.all(1, ft.colors.GREY_200)
                    )
                ]
            ),
            padding=ft.padding.all(25),
            bgcolor=ft.colors.WHITE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.colors.BLACK12,
                offset=ft.Offset(0, 4)
            )
        )

    def _create_action_section(self) -> ft.Container:
        """创建操作区域"""

        return ft.Container(
            content=ft.Row(
                controls=[
                    # 左侧：配置状态
                    ft.Column(
                        controls=[
                            ft.Text("配置状态", size=14, weight=ft.FontWeight.BOLD, color=ft.colors.GREY_700),
                            ft.Row(
                                controls=[
                                    ft.Icon(ft.icons.CIRCLE, size=12, color=ft.colors.RED),
                                    ft.Text("AI服务: 未配置", size=12, color=ft.colors.GREY_600)
                                ],
                                spacing=8
                            ),
                            ft.Row(
                                controls=[
                                    ft.Icon(ft.icons.CIRCLE, size=12, color=ft.colors.RED),
                                    ft.Text("聊天服务: 未连接", size=12, color=ft.colors.GREY_600)
                                ],
                                spacing=8
                            ),
                            ft.Row(
                                controls=[
                                    ft.Icon(ft.icons.CIRCLE, size=12, color=ft.colors.GREY_400),
                                    ft.Text("飞书同步: 未配置", size=12, color=ft.colors.GREY_600)
                                ],
                                spacing=8
                            )
                        ],
                        spacing=8
                    ),

                    # 右侧：操作按钮
                    ft.Row(
                        controls=[
                            ft.ElevatedButton(
                                text="保存配置",
                                icon=ft.icons.SAVE,
                                on_click=self._on_save_config,
                                style=ft.ButtonStyle(
                                    bgcolor=ft.colors.GREEN,
                                    color=ft.colors.WHITE,
                                    padding=ft.padding.symmetric(horizontal=25, vertical=12)
                                )
                            ),
                            ft.OutlinedButton(
                                text="重置配置",
                                icon=ft.icons.RESTORE,
                                on_click=self._on_reset_config
                            ),
                            ft.OutlinedButton(
                                text="导入配置",
                                icon=ft.icons.UPLOAD_FILE,
                                on_click=self._on_import_config
                            ),
                            ft.OutlinedButton(
                                text="导出配置",
                                icon=ft.icons.DOWNLOAD,
                                on_click=self._on_export_config
                            )
                        ],
                        spacing=10
                    )
                ],
                alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                vertical_alignment=ft.CrossAxisAlignment.CENTER
            ),
            padding=ft.padding.all(25),
            bgcolor=ft.colors.WHITE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.colors.BLACK12,
                offset=ft.Offset(0, 4)
            )
        )

    # 事件处理方法
    def _on_api_key_change(self, e):
        """API密钥变化"""
        self.deepseek_api_key = e.control.value

    def _on_chatlog_url_change(self, e):
        """Chatlog URL变化"""
        self.chatlog_url = e.control.value

    def _on_feishu_field_change(self, field_key: str, value: str):
        """飞书字段变化"""
        setattr(self, f"feishu_{field_key}", value)

    async def _on_test_deepseek(self, e):
        """测试DeepSeek连接"""
        if not self.deepseek_api_key.strip():
            await self.app.show_message("提示", "请先输入DeepSeek API密钥", "warning")
            return

        # 禁用按钮并显示测试中
        self.test_buttons['deepseek'].disabled = True
        self.test_buttons['deepseek'].text = "测试中..."
        await self.app.page.update_async()

        # 模拟测试
        await asyncio.sleep(2)

        # 恢复按钮
        self.test_buttons['deepseek'].disabled = False
        self.test_buttons['deepseek'].text = "测试连接"
        await self.app.page.update_async()

        await self.app.show_message("测试结果", "DeepSeek API连接成功！", "success")

    async def _on_test_chatlog(self, e):
        """测试Chatlog连接"""
        if not self.chatlog_url.strip():
            await self.app.show_message("提示", "请先输入Chatlog服务地址", "warning")
            return

        # 禁用按钮并显示测试中
        self.test_buttons['chatlog'].disabled = True
        self.test_buttons['chatlog'].text = "测试中..."
        await self.app.page.update_async()

        # 模拟测试
        await asyncio.sleep(2)

        # 恢复按钮
        self.test_buttons['chatlog'].disabled = False
        self.test_buttons['chatlog'].text = "测试连接"
        await self.app.page.update_async()

        await self.app.show_message("测试结果", "Chatlog服务连接失败，请检查服务是否启动", "error")

    async def _on_test_feishu(self, e):
        """测试飞书连接"""
        required_fields = ['app_id', 'app_secret', 'app_token', 'table_id']
        for field in required_fields:
            if not getattr(self, f"feishu_{field}").strip():
                await self.app.show_message("提示", f"请先填写完整的飞书配置信息", "warning")
                return

        # 禁用按钮并显示测试中
        self.test_buttons['feishu'].disabled = True
        self.test_buttons['feishu'].text = "测试中..."
        await self.app.page.update_async()

        # 模拟测试
        await asyncio.sleep(2)

        # 恢复按钮
        self.test_buttons['feishu'].disabled = False
        self.test_buttons['feishu'].text = "测试连接"
        await self.app.page.update_async()

        await self.app.show_message("测试结果", "飞书多维表格连接成功！", "success")

    def _open_url(self, url: str):
        """打开URL"""
        import webbrowser
        webbrowser.open(url)

    async def _show_feishu_tutorial(self, e):
        """显示飞书配置教程"""
        tutorial_text = """
🔧 飞书多维表格配置教程：

1. 创建飞书应用
   • 访问 https://open.feishu.cn/app
   • 创建企业自建应用
   • 记录应用ID和应用密钥

2. 配置权限
   • 在应用管理中添加多维表格权限
   • 发布应用版本

3. 创建多维表格
   • 在飞书中创建多维表格
   • 复制表格的app_token和table_id

4. 获取Token信息
   • app_token: 表格URL中的标识
   • table_id: 具体表格的ID
        """

        await self.app.show_message("飞书配置教程", tutorial_text.strip(), "info")

    async def _on_save_config(self, e):
        """保存配置"""
        await self.app.show_message("保存成功", "配置已保存", "success")

    async def _on_reset_config(self, e):
        """重置配置"""
        await self.app.show_message("重置配置", "配置重置功能开发中...", "info")

    async def _on_import_config(self, e):
        """导入配置"""
        await self.app.show_message("导入配置", "配置导入功能开发中...", "info")

    async def _on_export_config(self, e):
        """导出配置"""
        await self.app.show_message("导出配置", "配置导出功能开发中...", "info")
