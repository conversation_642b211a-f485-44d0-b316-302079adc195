"""
Flet UI测试脚本
用于测试Flet界面是否正常工作
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """测试导入"""
    print("🔍 测试模块导入...")
    
    try:
        import flet as ft
        print("✅ Flet 导入成功")
    except ImportError as e:
        print(f"❌ Flet 导入失败: {e}")
        return False
    
    try:
        from app.ui_flet.main_app import FletMainApp
        print("✅ FletMainApp 导入成功")
    except ImportError as e:
        print(f"❌ FletMainApp 导入失败: {e}")
        return False
    
    try:
        from app.ui_flet.components.sidebar import Sidebar
        from app.ui_flet.components.header import Header
        print("✅ UI组件 导入成功")
    except ImportError as e:
        print(f"❌ UI组件 导入失败: {e}")
        return False
    
    try:
        from app.ui_flet.pages.dashboard import DashboardPage
        from app.ui_flet.pages.groups import GroupsPage
        from app.ui_flet.pages.analysis import AnalysisPage
        from app.ui_flet.pages.results import ResultsPage
        from app.ui_flet.pages.settings import SettingsPage
        print("✅ 页面组件 导入成功")
    except ImportError as e:
        print(f"❌ 页面组件 导入失败: {e}")
        return False
    
    return True

def test_simple_ui():
    """测试简单UI"""
    print("\n🎨 测试简单Flet界面...")
    
    try:
        import flet as ft
        
        def main(page: ft.Page):
            page.title = "Flet UI 测试"
            page.window_width = 800
            page.window_height = 600
            
            # 创建测试界面
            test_card = ft.Card(
                content=ft.Container(
                    content=ft.Column(
                        controls=[
                            ft.Text(
                                "🎉 Flet UI 测试成功！",
                                size=24,
                                weight=ft.FontWeight.BOLD,
                                color=ft.colors.BLUE
                            ),
                            ft.Text(
                                "现代化界面已准备就绪",
                                size=16,
                                color=ft.colors.GREY_700
                            ),
                            ft.Container(height=20),
                            ft.ElevatedButton(
                                text="关闭测试",
                                icon=ft.icons.CHECK_CIRCLE,
                                on_click=lambda e: page.window_close(),
                                style=ft.ButtonStyle(
                                    bgcolor=ft.colors.GREEN,
                                    color=ft.colors.WHITE
                                )
                            )
                        ],
                        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                        spacing=10
                    ),
                    padding=ft.padding.all(40),
                    alignment=ft.alignment.center
                )
            )
            
            page.add(
                ft.Container(
                    content=test_card,
                    alignment=ft.alignment.center,
                    expand=True
                )
            )
        
        print("✅ 启动测试界面...")
        ft.app(target=main, view=ft.AppView.FLET_APP)
        print("✅ 测试界面运行完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试界面失败: {e}")
        return False

def test_main_app():
    """测试主应用"""
    print("\n🚀 测试主应用...")
    
    try:
        from app.ui_flet.main_app import FletMainApp
        
        # 创建应用实例
        app = FletMainApp()
        print("✅ 主应用实例创建成功")
        
        # 测试服务初始化
        if app.analysis_service is not None:
            print("✅ 分析服务初始化成功")
        else:
            print("⚠️ 分析服务初始化失败（这是正常的，因为缺少配置）")
        
        # 测试页面实例
        if hasattr(app, 'page_instances'):
            print("✅ 页面实例准备就绪")
        
        return True
        
    except Exception as e:
        print(f"❌ 主应用测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 Flet UI 测试套件")
    print("=" * 60)
    
    # 测试导入
    if not test_imports():
        print("\n❌ 导入测试失败，请检查依赖安装")
        print("运行: pip install flet>=0.21.0")
        sys.exit(1)
    
    # 测试主应用
    if not test_main_app():
        print("\n❌ 主应用测试失败")
        sys.exit(1)
    
    print("\n" + "=" * 60)
    print("✅ 所有测试通过！")
    print("🎨 Flet UI 已准备就绪")
    print("=" * 60)
    
    # 询问是否启动测试界面
    try:
        choice = input("\n是否启动测试界面？(y/n): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            test_simple_ui()
    except KeyboardInterrupt:
        print("\n👋 测试结束")

if __name__ == "__main__":
    main()
