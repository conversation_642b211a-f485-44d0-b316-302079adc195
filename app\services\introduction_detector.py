"""
自我介绍检测器
结合正则表达式模式匹配和AI智能检测，识别自我介绍消息
"""

import re
import asyncio
from typing import List, Dict, Any, Optional
import logging
from datetime import datetime

from ..models import Message, IntroductionCandidate, IntroductionStatus
from ..config import get_config
from .deepseek_client import DeepSeekClient

logger = logging.getLogger(__name__)

class IntroductionDetector:
    """自我介绍检测器"""
    
    def __init__(self):
        self.config = get_config().analysis
        self.deepseek_client = DeepSeekClient()
        
        # 自我介绍关键词模式
        self.intro_patterns = [
            # 基本介绍模式
            r'我是.*?[，,。]',
            r'我叫.*?[，,。]',
            r'大家好.*?我.*?[，,。]',
            r'Hello.*?我.*?[，,。]',
            r'Hi.*?我.*?[，,。]',
            
            # 职业介绍模式
            r'我.*?工作.*?[，,。]',
            r'我.*?从事.*?[，,。]',
            r'我.*?做.*?[，,。]',
            r'我的职业.*?[，,。]',
            r'我在.*?公司.*?[，,。]',
            
            # 能力/资源介绍模式
            r'我.*?可以.*?[，,。]',
            r'我.*?能够.*?[，,。]',
            r'我.*?擅长.*?[，,。]',
            r'我.*?会.*?[，,。]',
            r'我有.*?经验.*?[，,。]',
            
            # 需求表达模式
            r'我.*?希望.*?[，,。]',
            r'我.*?想.*?[，,。]',
            r'我.*?需要.*?[，,。]',
            r'我.*?寻找.*?[，,。]',
            r'我.*?求.*?[，,。]',
            
            # 行业/领域介绍模式
            r'我.*?行业.*?[，,。]',
            r'我.*?领域.*?[，,。]',
            r'我专业.*?[，,。]',
            r'我学.*?[，,。]',
        ]
        
        # 排除模式（避免误判）
        self.exclude_patterns = [
            r'^我觉得',
            r'^我认为',
            r'^我想说',
            r'^我觉得',
            r'^我同意',
            r'^我不同意',
            r'我刚才',
            r'我刚刚',
            r'我今天',
            r'我昨天',
            r'我明天',
        ]
        
        # 编译正则表达式
        self.compiled_patterns = [re.compile(pattern) for pattern in self.intro_patterns]
        self.compiled_exclude_patterns = [re.compile(pattern) for pattern in self.exclude_patterns]
    
    async def detect_introduction_candidates(self, messages: List[Message]) -> List[IntroductionCandidate]:
        """
        检测自我介绍候选项
        
        Args:
            messages: 消息列表
            
        Returns:
            自我介绍候选项列表
        """
        candidates = []
        
        for message in messages:
            # 跳过系统消息和非文本消息
            if message.message_type.value != 'text' or not message.content.strip():
                continue
            
            # 初步模式匹配检测
            pattern_result = self._pattern_detection(message.content)
            
            if pattern_result['is_candidate']:
                candidate = IntroductionCandidate(
                    message=message,
                    is_introduction=False,  # 待AI确认
                    confidence=pattern_result['confidence'],
                    detection_method='pattern',
                    reason=pattern_result['reason'],
                    status=IntroductionStatus.PENDING
                )
                candidates.append(candidate)
                logger.info(f"发现自我介绍候选项: {message.id} - {pattern_result['reason']}")
        
        logger.info(f"模式匹配发现 {len(candidates)} 个候选项")
        return candidates
    
    async def confirm_with_ai(self, candidates: List[IntroductionCandidate]) -> List[IntroductionCandidate]:
        """
        使用AI确认自我介绍候选项
        
        Args:
            candidates: 候选项列表
            
        Returns:
            确认后的候选项列表
        """
        confirmed_candidates = []
        
        # 批量处理，避免API调用过于频繁
        batch_size = self.config.batch_size
        for i in range(0, len(candidates), batch_size):
            batch = candidates[i:i + batch_size]
            batch_results = await self._process_batch_with_ai(batch)
            confirmed_candidates.extend(batch_results)
            
            # 添加延迟避免API限制
            if i + batch_size < len(candidates):
                await asyncio.sleep(1)
        
        # 统计结果
        confirmed_count = sum(1 for c in confirmed_candidates if c.is_introduction)
        logger.info(f"AI确认了 {confirmed_count}/{len(candidates)} 个自我介绍")
        
        return confirmed_candidates
    
    async def _process_batch_with_ai(self, batch: List[IntroductionCandidate]) -> List[IntroductionCandidate]:
        """处理一批候选项"""
        tasks = []
        for candidate in batch:
            task = self._confirm_single_candidate(candidate)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        confirmed_batch = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"AI确认失败: {result}")
                # 保持原状态
                confirmed_batch.append(batch[i])
            else:
                confirmed_batch.append(result)
        
        return confirmed_batch
    
    async def _confirm_single_candidate(self, candidate: IntroductionCandidate) -> IntroductionCandidate:
        """确认单个候选项"""
        try:
            candidate.status = IntroductionStatus.PROCESSING
            
            # 调用AI检测
            ai_result = await self.deepseek_client.detect_introduction(candidate.message.content)
            
            # 更新候选项信息
            if ai_result.get('is_introduction', False):
                candidate.is_introduction = True
                candidate.confidence = max(candidate.confidence, ai_result.get('confidence', 0.0))
                candidate.detection_method = 'pattern+ai'
                candidate.reason = f"模式匹配+AI确认: {ai_result.get('reason', '')}"
                candidate.status = IntroductionStatus.COMPLETED
            else:
                candidate.is_introduction = False
                candidate.confidence = ai_result.get('confidence', 0.0)
                candidate.reason = f"AI拒绝: {ai_result.get('reason', '')}"
                candidate.status = IntroductionStatus.SKIPPED
            
            return candidate
            
        except Exception as e:
            logger.error(f"AI确认候选项失败: {e}")
            candidate.status = IntroductionStatus.FAILED
            candidate.reason = f"AI确认失败: {str(e)}"
            return candidate
    
    def _pattern_detection(self, content: str) -> Dict[str, Any]:
        """
        基于模式的检测
        
        Args:
            content: 消息内容
            
        Returns:
            检测结果
        """
        # 预处理内容
        content = content.strip()
        
        # 检查排除模式
        for exclude_pattern in self.compiled_exclude_patterns:
            if exclude_pattern.search(content):
                return {
                    'is_candidate': False,
                    'confidence': 0.0,
                    'reason': f'匹配排除模式: {exclude_pattern.pattern}'
                }
        
        # 检查介绍模式
        matched_patterns = []
        for pattern in self.compiled_patterns:
            if pattern.search(content):
                matched_patterns.append(pattern.pattern)
        
        if matched_patterns:
            # 计算置信度
            confidence = self._calculate_pattern_confidence(content, matched_patterns)
            
            return {
                'is_candidate': True,
                'confidence': confidence,
                'reason': f'匹配模式: {", ".join(matched_patterns[:3])}'  # 只显示前3个
            }
        
        return {
            'is_candidate': False,
            'confidence': 0.0,
            'reason': '无匹配模式'
        }
    
    def _calculate_pattern_confidence(self, content: str, matched_patterns: List[str]) -> float:
        """
        计算模式匹配置信度
        
        Args:
            content: 消息内容
            matched_patterns: 匹配的模式列表
            
        Returns:
            置信度 (0.0-1.0)
        """
        base_confidence = 0.3  # 基础置信度
        
        # 根据匹配模式数量增加置信度
        pattern_bonus = min(len(matched_patterns) * 0.1, 0.3)
        
        # 根据内容长度调整置信度
        content_length = len(content)
        if content_length > 100:  # 较长的内容更可能是自我介绍
            length_bonus = 0.2
        elif content_length > 50:
            length_bonus = 0.1
        else:
            length_bonus = 0.0
        
        # 检查关键词密度
        intro_keywords = ['我是', '我叫', '我的', '我在', '我做', '我有', '我能', '我会', '我希望', '我想', '我需要']
        keyword_count = sum(1 for keyword in intro_keywords if keyword in content)
        keyword_bonus = min(keyword_count * 0.05, 0.2)
        
        # 检查是否包含联系方式或具体信息
        contact_patterns = [
            r'微信',
            r'手机',
            r'电话',
            r'邮箱',
            r'公司',
            r'工作',
            r'经验',
            r'年',
            r'专业',
            r'学历'
        ]
        
        contact_count = sum(1 for pattern in contact_patterns if re.search(pattern, content))
        contact_bonus = min(contact_count * 0.03, 0.15)
        
        # 计算最终置信度
        total_confidence = base_confidence + pattern_bonus + length_bonus + keyword_bonus + contact_bonus
        
        return min(total_confidence, 0.95)  # 模式匹配最高不超过0.95
    
    async def detect_and_confirm(self, messages: List[Message]) -> List[IntroductionCandidate]:
        """
        完整的检测和确认流程
        
        Args:
            messages: 消息列表
            
        Returns:
            确认的自我介绍候选项列表
        """
        logger.info(f"开始检测 {len(messages)} 条消息中的自我介绍")
        
        # 第一步：模式匹配检测候选项
        candidates = await self.detect_introduction_candidates(messages)
        
        if not candidates:
            logger.info("未发现自我介绍候选项")
            return []
        
        # 第二步：AI确认候选项
        if self.config.auto_detect:
            confirmed_candidates = await self.confirm_with_ai(candidates)
        else:
            # 如果禁用自动检测，将所有候选项标记为待确认
            for candidate in candidates:
                candidate.status = IntroductionStatus.PENDING
            confirmed_candidates = candidates
        
        # 过滤出确认的自我介绍
        introductions = [c for c in confirmed_candidates if c.is_introduction]
        
        logger.info(f"检测完成，发现 {len(introductions)} 个自我介绍")
        return confirmed_candidates
    
    def get_detection_stats(self, candidates: List[IntroductionCandidate]) -> Dict[str, Any]:
        """
        获取检测统计信息
        
        Args:
            candidates: 候选项列表
            
        Returns:
            统计信息
        """
        total = len(candidates)
        confirmed = sum(1 for c in candidates if c.is_introduction)
        rejected = sum(1 for c in candidates if not c.is_introduction and c.status != IntroductionStatus.PENDING)
        pending = sum(1 for c in candidates if c.status == IntroductionStatus.PENDING)
        failed = sum(1 for c in candidates if c.status == IntroductionStatus.FAILED)
        
        # 计算平均置信度
        avg_confidence = 0.0
        if confirmed > 0:
            total_confidence = sum(c.confidence for c in candidates if c.is_introduction)
            avg_confidence = total_confidence / confirmed
        
        return {
            'total_candidates': total,
            'confirmed_introductions': confirmed,
            'rejected_candidates': rejected,
            'pending_candidates': pending,
            'failed_candidates': failed,
            'average_confidence': avg_confidence,
            'detection_rate': confirmed / total if total > 0 else 0.0
        } 