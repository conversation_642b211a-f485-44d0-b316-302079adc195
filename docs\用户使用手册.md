# 微信群自我介绍分析工具 - 用户使用手册

## 📋 工具介绍

微信群自我介绍分析工具是一款智能化的用户画像分析软件，能够自动识别微信群中的自我介绍消息，并提取出用户的职业、技能、需求等关键信息，帮助群管理员快速了解群成员画像。

### 🚀 主要功能

- **智能识别**：自动识别群组中的自我介绍消息
- **用户画像**：提取职业、技能、需求等关键信息
- **数据导出**：支持Excel和JSON格式导出
- **飞书同步**：可选择同步到飞书多维表格
- **进度监控**：详细的分析进度和统计信息

### 💻 系统要求

- Windows 10/11 64位系统
- 内存：至少4GB RAM
- 硬盘：至少500MB可用空间
- 网络：稳定的互联网连接

## 🔧 快速开始

### 第一步：启动程序

1. 双击 `微信群分析工具.exe` 启动程序
2. 首次启动会显示欢迎向导
3. 点击 **"开始配置向导"** 进行初始设置

### 第二步：配置AI分析服务

#### 获取DeepSeek API密钥（推荐）

1. **访问官网**
   - 打开浏览器，访问：https://platform.deepseek.com
   - 如果没有账号，点击"注册"创建新账号

2. **注册账号**
   - 使用邮箱或手机号注册
   - 完成邮箱/手机验证
   - 建议使用稳定的邮箱地址

3. **获取API密钥**
   - 登录后进入控制台
   - 点击左侧菜单"API管理"
   - 点击"创建API密钥"按钮
   - 输入密钥名称（如：微信群分析）
   - 复制生成的API密钥

4. **配置密钥**
   - 在软件中粘贴API密钥
   - 点击"下一步"继续

> 💰 **费用说明**：DeepSeek按使用量计费，分析1000条消息约需0.1-0.5元，新用户通常有免费额度。

### 第三步：配置聊天记录服务

1. **准备工作**
   - 确保已安装微信聊天记录导出工具
   - 启动聊天记录服务（默认端口5030）

2. **配置服务地址**
   - 本地服务：`http://127.0.0.1:5030`
   - 远程服务：`http://服务器IP:5030`
   - 如果不确定，使用默认地址即可

3. **测试连接**
   - 配置完成后点击"测试连接"
   - 确保显示"连接成功"

### 第四步：配置飞书同步（可选）

如果需要将分析结果同步到飞书多维表格，按以下步骤配置：

1. **创建飞书应用**
   - 访问：https://open.feishu.cn/
   - 登录飞书账号
   - 创建"企业自建应用"
   - 记录应用ID和应用密钥

2. **配置权限**
   - 在应用管理中添加权限：
     - `bitable:app` - 多维表格应用权限
     - `bitable:app:readonly` - 读取多维表格
     - `bitable:app:readwrite` - 读写多维表格

3. **获取表格Token**
   - 在飞书中创建多维表格
   - 从浏览器地址栏复制Token（/base/后面的字符串）

4. **完成配置**
   - 将应用ID、应用密钥、表格Token填入软件
   - 可点击"测试飞书连接"验证配置

## 📱 使用教程

### 1. 群组管理

**功能**：查看和选择要分析的微信群组

**操作步骤**：
1. 点击"群组管理"标签页
2. 点击"刷新群组列表"获取最新群组
3. 选择要分析的群组
4. 查看群组基本信息（成员数、消息数等）

**注意事项**：
- 确保聊天记录服务正常运行
- 如果看不到群组，检查服务连接状态

### 2. 分析任务

**功能**：启动智能分析任务

**操作步骤**：
1. 点击"分析任务"标签页
2. 选择群组和时间范围
3. 设置分析参数：
   - **置信度阈值**：建议0.7-0.9
   - **批处理大小**：建议10-20
   - **最大线程数**：建议3-8
4. 点击"开始分析"
5. 查看实时进度和统计信息

**分析流程**：
1. 📡 加载群组消息
2. 🤖 AI智能识别自我介绍
3. 📊 提取用户画像信息
4. 💾 保存分析结果
5. 📋 同步到飞书（如已配置）

### 3. 分析结果

**功能**：查看、筛选和导出分析结果

**操作步骤**：
1. 点击"分析结果"标签页
2. 查看用户画像列表
3. 使用筛选功能：
   - 按职业筛选
   - 按技能关键词搜索
   - 按置信度过滤
4. 导出数据：
   - Excel格式：适合数据分析
   - JSON格式：适合程序处理
5. 手动同步到飞书

**结果说明**：
- **昵称**：用户在群中的昵称
- **职业**：提取的职业信息
- **个人介绍**：原始自我介绍内容
- **能够提供**：用户能提供的服务/技能
- **寻求帮助**：用户需要的帮助
- **置信度**：AI分析的可信度（0-1）

### 4. 配置设置

**功能**：管理所有系统配置

**主要配置项**：
- **AI分析服务**：API密钥、模型参数
- **聊天记录服务**：服务地址、连接参数
- **飞书同步**：应用凭证、表格信息
- **分析参数**：批处理、线程数、阈值
- **界面设置**：主题、窗口大小

**快速操作**：
- 点击"一键配置向导"快速设置
- 点击"测试API连接"验证配置
- 使用"导出配置"和"导入配置"备份设置

## 🛠️ 故障排除

### 常见问题1：无法连接AI服务

**症状**：提示"AI服务未连接"或分析失败

**解决方案**：
1. **检查API密钥**
   - 确认密钥完整且无空格
   - 在DeepSeek控制台验证密钥状态
   - 检查账户余额是否充足

2. **检查网络连接**
   - 确保能访问外网
   - 检查防火墙设置
   - 尝试切换网络环境

3. **更换AI服务**
   - 选择"OpenAI兼容服务"
   - 使用其他AI服务商的API

### 常见问题2：找不到群组数据

**症状**：群组列表为空或无法刷新

**解决方案**：
1. **检查聊天记录服务**
   - 确认服务已启动
   - 检查端口5030是否被占用
   - 重启聊天记录服务

2. **检查服务地址**
   - 确认IP地址和端口正确
   - 本地服务使用127.0.0.1
   - 远程服务使用实际IP地址

3. **检查数据权限**
   - 确保有微信数据访问权限
   - 检查数据文件是否存在

### 常见问题3：飞书同步失败

**症状**：提示同步错误或权限不足

**解决方案**：
1. **检查应用配置**
   - 确认应用ID和密钥正确
   - 检查应用状态是否正常
   - 重新生成应用密钥

2. **检查权限设置**
   - 确保已添加bitable相关权限
   - 检查权限是否已审核通过
   - 联系管理员开通权限

3. **检查表格配置**
   - 确认表格Token正确
   - 检查对表格的编辑权限
   - 尝试重新创建表格

### 常见问题4：分析结果质量不高

**症状**：识别准确率低或提取信息不准确

**解决方案**：
1. **调整参数**
   - 提高置信度阈值（建议0.8以上）
   - 减少批处理大小
   - 调整AI模型温度参数

2. **优化数据质量**
   - 选择自我介绍较多的时间段
   - 过滤掉过短的消息
   - 检查消息内容完整性

3. **手动筛选**
   - 使用筛选功能过滤结果
   - 手动验证重要数据
   - 导出后进一步处理

## 💡 使用技巧

### 提高分析效果

1. **选择合适的时间范围**
   - 新群建立初期（前1-2周）
   - 群活动期间（群成员较多的时段）
   - 避免选择过长时间范围

2. **优化分析参数**
   - 首次使用建议用小范围测试
   - 根据结果调整置信度阈值
   - 根据电脑性能调整线程数

3. **合理使用筛选**
   - 按置信度过滤低质量结果
   - 使用关键词搜索特定技能
   - 分类导出不同职业群体

### 数据管理建议

1. **定期备份**
   - 导出重要分析结果
   - 备份配置文件
   - 清理过期日志

2. **团队协作**
   - 使用飞书同步功能
   - 统一配置文件格式
   - 建立数据更新机制

3. **隐私保护**
   - 妥善保管API密钥
   - 定期更换访问凭证
   - 注意数据使用合规性

## 📞 技术支持

### 获取帮助

1. **内置帮助**
   - 点击"帮助"菜单查看使用教程
   - 使用"配置向导"快速设置
   - 查看"关于"了解版本信息

2. **日志分析**
   - 查看`app/data/logs/app.log`了解详细错误
   - 使用"工具"菜单清理日志文件
   - 记录错误时间和操作步骤

3. **问题反馈**
   - 详细描述问题现象
   - 提供错误日志信息
   - 说明操作系统和软件版本

### 更新说明

- 程序会自动检查更新（需联网）
- 建议定期更新获取最新功能
- 更新前请备份重要配置和数据

---

## 📋 附录

### A. 配置文件说明

配置文件位置：`app/data/config.json`

主要配置项：
- `deepseek`：AI服务配置
- `chatlog`：聊天记录服务配置
- `feishu`：飞书同步配置
- `analysis`：分析参数配置
- `ui`：界面设置配置

### B. 日志文件说明

日志文件位置：`app/data/logs/`

日志级别：
- `INFO`：正常操作信息
- `WARNING`：警告信息
- `ERROR`：错误信息
- `DEBUG`：调试信息（开发模式）

### C. 数据格式说明

#### Excel导出格式

| 列名 | 说明 | 示例 |
|------|------|------|
| 昵称 | 用户昵称 | 张三 |
| 职业 | 职业信息 | 软件工程师 |
| 个人介绍 | 完整自我介绍 | 大家好，我是... |
| 能够提供 | 可提供的服务 | Python开发，技术咨询 |
| 寻求帮助 | 需要的帮助 | 产品设计，市场推广 |
| 行业领域 | 所属行业 | 互联网，软件开发 |
| 置信度 | AI分析可信度 | 0.85 |
| 消息ID | 原始消息标识 | msg_123456 |
| 群组ID | 群组标识 | group_789 |
| 创建时间 | 分析时间 | 2024-01-01 10:30:00 |

#### JSON导出格式

```json
{
  "nickname": "张三",
  "profession": "软件工程师",
  "introduction": "大家好，我是...",
  "can_provide": "Python开发，技术咨询",
  "seeking_help": "产品设计，市场推广",
  "industry": "互联网，软件开发",
  "confidence": 0.85,
  "message_id": "msg_123456",
  "group_id": "group_789",
  "created_at": "2024-01-01T10:30:00"
}
```

---

*感谢您使用微信群自我介绍分析工具！如有任何问题，请查看本手册或联系技术支持。* 