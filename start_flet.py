"""
快速启动Flet现代化界面
"""

import sys
import os
from pathlib import Path

def main():
    """主函数"""
    print("🚀 启动现代化界面...")
    
    try:
        # 检查Flet依赖
        try:
            import flet
            print("✅ Flet 依赖检查通过")
        except ImportError:
            print("❌ 缺少Flet依赖，正在安装...")
            os.system("pip install flet>=0.21.0")
            print("✅ Flet 安装完成")
        
        # 启动Flet应用
        from app.main_flet import main as flet_main
        flet_main()
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n🔧 可能的解决方案：")
        print("1. 运行: pip install -r requirements.txt")
        print("2. 或者运行: python start_ui.py 选择界面版本")
        sys.exit(1)

if __name__ == "__main__":
    main()
